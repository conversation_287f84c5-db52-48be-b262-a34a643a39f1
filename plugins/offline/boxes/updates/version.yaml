1.0.1:
- Initial Version
- create_boxes_table.php
- create_pages_table.php
2.0.23:
- Introduces theme specific pages
- add_theme_column_to_pages_table.php
- imageWidth
2.0.36:
- Added more SEO tags to pages
- add_opengraph_columns_to_pages_table.php
2.0.37:
- Make pages useable as model content
2.0.47:
- First public release
2.0.48:
- Fixed bug in Editor layout
2.0.49:
- Fixed bug in URL generation
2.0.50:
- Fixed handling of uploaded file descriptions
2.0.53:
- Fixed Import/Export logic
2.0.54:
- Fixed RainLab.Translate integration
2.0.55:
- Fixed database migration
2.0.56:
- Fixed RainLab.Translate compatibility
2.0.57:
- Fixed duplicate Box feature
2.0.59:
- Added `offline.boxes.editorRefreshed` JS event
2.0.60:
- Added option to manually define a padding for the focus rectangle in the Boxes Editor
2.0.61:
- Fixed SQLite compatibility
2.0.62:
- Multi-theme usage fix
- reset_theme_for_system_pages.php
2.0.64:
- Fixed export of nested Boxes
2.0.65:
- Added `custom_config` field to Page model
- add_custom_config_column_to_pages_table.php
2.0.66:
- Added `custom_config` data to export
2.0.67:
- Added OFFLINE.SiteSearch support
2.0.68:
- Bugfix when having partial and full exports in the same installation
2.0.69:
- Use `slug` as references in RainLab.Pages menu to be independent of the database ID
2.0.70:
- Fixed menu migration logic
- migrate_rainlab_pages_menu_references.php
2.0.71:
- Optimized menu item URL generation
2.0.72:
- Fixed import bug
2.0.73:
- Fixed parsing of repeater config without fields
2.0.74:
- Fixed saving of nested pages
2.0.75:
- Fixed page duplication
2.0.76:
- Minor editor optimizations
2.0.77:
- Fixed generation of nested menu stuctures
2.0.79:
- Optimized OFFLINE.SiteSearch integration
2.0.80:
- Fixed slug generation when duplicating pages
2.0.81:
- Fixed OpenGraph Tag output
2.0.82:
- "Fixed October 3.1 compatibility issue"
2.0.83:
- "Fixed another October 3.1 compatibility issue"
2.0.84:
- "The plugin is no longer booted when no active theme is set"
2.0.85:
- "Added uniqueAlias option to components (see documentation)"
2.0.86:
- "Added support for RainLab.Translate 2.0"
2.0.87:
- "Prevented Boxes Editor styles from leaking into Rich editor form widget"
2.0.88:
- "Another fix regarding Rich editor styles"
2.0.89:
- "Ignore translations when caching menu items"
2.0.90:
- "Fixed Tailor editor support"
2.0.91:
- "Added custom section sorting (see https://docs.boxes.offline.ch/use-cases/editor-customization.html#changing-the-order-of-partial-sections)"
2.0.92:
- "Fixed bug where the Editor reloaded on tab navigation"
2.0.93:
- "Fixed style conflict"
2.1.0:
- "Adds multisite support"
- add_multisite_support.php
2.1.1:
- "Refactored database structure"
- create_content_table.php
2.1.2:
- "Please see migration guide for update instructions to version 2.1: https://docs.boxes.offline.ch/getting-started/migration-guide.html"
2.1.3:
- "Fixed style conflict with rich editor form widget"
2.1.4:
- "Introduced new 'contexts' option for partials (see https://docs.boxes.offline.ch/reference/schema.html#contexts)"
2.1.5:
- "Minor UI tweaks"
2.1.6:
- "Added method to manually init partial configuration in Tailor (see https://docs.boxes.offline.ch/use-cases/usage-in-plugins.html#manually-initializing-partials)"
2.1.7:
- "Added option to disable the output of the scaffolding (see https://docs.boxes.offline.ch/getting-started/configuration.html)"
2.1.8:
- "Added option to override the backend menu item position (see https://docs.boxes.offline.ch/getting-started/configuration.html)"
2.1.9:
- "Fixed replication of Content model instances"
2.1.10:
- "Fixed Multisite bug when editing meta information of a Boxes Page"
2.1.11:
- "Persist currently selected page across page reloads in Editor"
2.1.12:
- "Fixed bug in importer"
2.1.13:
- "Fixed validation of attachment fields, Boxes now guesses the right attribute names for validation rules by using the field label from the YAML config automatically"
2.1.14:
- "Added support for read-only Boxes"
- add_read_only_flag_to_boxes.php
2.1.15:
- "Added support to enforce a Boxes structure if Boxes is used as a plugin editor (see https://docs.boxes.offline.ch/use-cases/usage-in-plugins.html)"
2.1.16:
- "Recompiled assets"
2.1.17:
- "Added model property to context variable to make the related model accessible in partials"
2.1.18:
- "Added Page Templates (see https://docs.boxes.offline.ch/use-cases/page-templates.html)"
- add_locked_column_to_boxes.php
2.1.19:
- "Recompiled assets"
2.1.20:
- "Cleanup"
2.1.21:
- "Updated page template config structure"
2.1.22:
- "Fixed name of latest migration class"
2.1.23:
- "Fixed migration from older Boxes versions to 2.1"
2.1.24:
- "Added boxes:mirror-site command to copy all Boxes pages from one Site to another (see https://docs.boxes.offline.ch/use-cases/multisite.html#mirror-all-boxes-pages-to-a-new-site)"
2.1.25:
- "Export site_id in boxes:export command"
2.1.26:
- "The boxesPage filter now automatically always returns data for the currently active Site (see https://docs.boxes.offline.ch/concepts/box-pages.html#boxespage-when-using-the-multisite-feature)"
2.1.27:
- "Added support for the new native page finder widget"
2.1.28:
- "Include Site route_prefix when generating boxes page urls"
2.1.29:
- "Fixed Multisite support for translatable fields"
2.1.30:
- "Fixed migration for new projects"
2.1.31:
- "Fixed handling of multi-language Sites in the Boxes Editor"
2.1.32:
- "Fixed handling of sub-page navigation in the Boxes Editor"
2.1.33:
- "Fixed a bug with the Rainlab.Translate Plugin < 2.0"
2.1.34:
- "Correctly remove old Boxes when importing a page"
2.1.35:
- "Do not mirror RainLab.Translate translation strings when using boxes:mirror-site command"
2.1.36:
- "Handle URL generation for prefixed sites correctly when using the new |link Twig filter to link to a Boxes Page"
2.1.37:
- "Fixed page duplication"
2.1.38:
- "Fixed wrong Box order when saving"
2.1.39:
- "Recompiled assets"
2.1.40:
- "Added support to specify an array of contexts as allowed children of a Box (see https://docs.boxes.offline.ch/use-cases/nested-boxes.html#enable-nested-boxes)"
2.1.41:
- "Recompiled assets"
2.2.3:
- "Rolled back faulty migration"
- fix_faulty_page_publishing.php
2.2.4:
- "Fixed menu item generation when nesting is disabled"
2.2.5:
- "Fixed handling of active Theme when changed in the Backend settings"
2.2.7:
- "Fixed display bug in Editor"
2.2.9:
- "Fixed loading of partials when a custom theme on a site is configured"
2.2.10:
- "Added missing down migration method"
2.2.11:
- "Fixed partial contexts when using Boxes as a FormWidget"
2.2.12:
- "Fixed spacing issue in partial selection modal"
2.2.13:
- "Fixed CSS for icons in partial selection modal"
2.2.15:
- "Optimized theme resolver when mutltisite feature is in use"
2.2.16:
- "Added additional theme resolver fixes"
2.2.17:
- "Added additional theme resolver fixes"
2.2.19:
- "Fixed error when duplicating boxes"
3.0.0:
- "Added page publishing feature"
- page_publishing.php
3.0.1:
- "Added support for Box references"
- add_box_references.php
3.0.2:
- "Added a checkbox to hide pages in the navigation"
- add_is_hidden_in_navigation.php
3.0.3:
- "Added pending changes flag"
- add_has_pending_changes_flag.php
3.0.5:
- "Initial public release of Boxes 3.0"
3.0.9:
- "Various minor bugfixes"
3.0.10:
- "Fixed bug with nested boxes rendering"
3.0.11:
- "Fixed issue when switching between sites before a page was selected"
3.1.0:
- "Placeholder previews, Dark mode and minor bugfixes (see https://docs.boxes.offline.ch/getting-started/changelog.html#version-3-1)"
3.1.1:
- "Added support for attaching Tailor Models to Boxes (see https://docs.boxes.offline.ch/use-cases/including-tailor-content.html#using-a-record-finder)"
3.1.3:
- "Fixed multisite scopes"
3.1.5:
- "Optimized queries that fetch revisions, added support for nested Boxes in page templates (thanks to @mtareiling)"
3.1.6:
- "Do not touch published state of page when switching between sites in the Boxes Editor"
3.1.7:
- "Fixed editing of disabled Sites"
3.1.8:
- "Fixed bug in mirror site command"
3.1.9:
- "Optimized path handling on Windows"
3.1.10:
- "Fixed handling of hidden menu items"
3.1.11:
- "Fixed editing issue when using the Plugin in Tailor"
3.1.12:
- "Fixed editing issue when using the Plugin in Tailor (2)"
3.1.13:
- "Added missing scope method to free version"
3.1.14:
- "Fixed page sorting in Boxes Editor"
3.1.15:
- "Fixed page sorting in Boxes Editor (2)"
3.1.16:
- "Fixed handling of menu generation when revisions feature is disabled"
3.1.17:
- "Fixed Turoblinks navigation mode in Backend"
3.1.18:
- "Fixed replication of nested boxes"
3.1.19:
- "Fixed moving of duplicated boxes"
3.1.20:
- "Introduced new boxes:heal-tree artisan command to heal broken Page tree structures"
3.1.21:
- "Handle nested Boxes correctly when running the new tree healing command"
3.2.0:
- "Implemented support for single file partials (see https://docs.boxes.offline.ch/concepts/box-configs.html#defining-the-box-config-inline)"
3.2.2:
- "Improved single fie partial support"
3.2.3:
- "Fixed Boxes free release"
3.3.0:
- "Added new BOXES_MULTISITE_MIRRORING_ENABLED config option to out-out of automatic Page mirroring when switching sites"
3.3.1:
- "Added child-theme support"
3.3.2:
- "Always use latest version when generating menu items"
3.3.3:
- "Fixed Migration-Error from Version Bump"
3.3.4:
- "Fixed Migration-Error from Version Bump 2"
3.3.5:
- "Minor multi-site bugs fixed"
3.3.6:
- "Fixed pages from appearing multiple times in SiteSearch results"
3.3.7:
- "Fixed loading of nested pages in menu structures"
3.3.9:
- "Added missing method to free version"
3.3.10:
- "Fixed replication of nested content boxes"
3.3.11:
- "Optimized handling of preview pages on multisite setups"
3.3.12:
- "Fixed handling of Boxes references when using symlinks"
3.3.13:
- "Improved handling of partials in child themes"
3.3.14:
- "Added two events to filter available partials (see https://docs.boxes.offline.ch/reference/events.html)"
3.3.15:
- "Added dutch translations (thanks to @RitsmG)"
3.3.16:
- "Fixed publishing of nested boxes"
3.3.17:
- "Fixed editing of file attachment details when using Boxes in a custom plugin form"
3.3.18:
- "Keep site_root_id when publishing Pages"
3.3.19:
- "Fixed revisions status query"
3.3.20:
- "Fixed offline.boxes.filterPartials event being ignored in partial selector"
3.3.21:
- "Fixed structure list in Backend Editor"
3.4.0:
- "You can now edit Boxes directly in the Editor section in the backend"
3.4.1:
- "Added support for repeater item models with file attachment support (see https://docs.boxes.offline.ch/use-cases/relations.html#repeater-relations-with-file-upload-support-since-v3-4)"
- create_repeater_items_table.php
3.4.2:
- "Fixed compatibility issue with October 3.6"
3.4.3:
- "Fixed 'Add Box' action when nested more than two levels deep"
3.4.4:
- "Fixed 'Add Box' action when nested more than two levels deep"
3.4.5:
- "Fixed eager-loading of disabled Boxes"
3.5.0:
- "Disabled automatic page mirroring when switching sites (see https://docs.boxes.offline.ch/getting-started/changelog.html)"
3.5.1:
- "Added offline.boxes.filterLayouts event"
3.5.2:
- "Optimized Boxes sorting"
3.5.3:
- "Minor UI tweak"
3.5.4:
- "Updated default rich editor styles"
3.5.5:
- "Introduced offline.boxes.extendBoxScaffoldingClasses Event to add custom css classes to the .oc-box elements"
3.5.6:
- "Ignore empty box scaffolding classes"
3.5.7:
- "Fixed passing scaffolding classes to children"
3.5.8:
- "Hide pending placeholder when clicking on an existing Box"
3.5.9:
- "Show all available Sites when copying a page"
3.5.11:
- "Fixed replication of Pages with eager-loaded Box data"
3.5.12:
- "Fixed duplication of nested Boxes"
3.5.13:
- "Fixed hidden in navigation state of nested pages"
3.6.0:
- "The revisions system is now configurable in the backend settings and is disabled by default for new installations"
- set_initial_revisions_setting.php
3.6.1:
- "Fixed settings migration for free version."
3.6.2:
- "Added support for repeaters on the Page form"
3.6.3:
- "Fixed sorting of nested Boxes in Editor sidebar"
3.6.4:
- "Fixed sorting of nested Boxes in Editor sidebar"
3.6.6:
- "Fixed missing 'Create page' action"
3.6.7:
- "Fixed page query when revisions are disabled"
3.6.8:
- "Fixed support for Site theme overrides in Editor preview"
3.6.9:
- "Fixed support for Site theme overrides in Editor preview"
3.6.10:
- "Optimized support for Site theme overrides in Editor preview"
3.6.11:
- "Added russian locale"
3.6.12:
- "Added reference to parent Box to context variable"
3.6.13:
- "Fixed page finder references when using mulitple Sites"
3.6.14:
- "Fixed saving of file upload descriptions before a Box was saved"
3.6.15:
- "Added fakerphp as direct dpendency"
- "Fixed labelFrom property"
3.6.16:
- "Respect allowed children contexts when moving Boxes"
3.6.17:
- "Fixed Boxes drag-and-drop reordering"
3.6.18:
- "Fixed nested Boxes display"
3.6.19:
- "Fixed bug where too many pages get deleted when using an old Boxes database"
3.6.20:
- "Added duplicate Box icon to sidebar"
3.6.21:
- "Added sections overview to Partial selection modal"
3.6.22:
- "Optimized page list"
3.6.23:
- "Optimized RainLab.Translate <2.0 Support"
3.6.24:
- "Added translation strings in BoxFinder form widget"
3.6.25:
- "Changed Boxes pagefinder prefix"
3.6.26:
- "Fixed copying page to other Site without existing parent"
3.6.27:
- "Optimized copying page to ohter Site"
3.6.28:
- "Optimized copying page to ohter Site"
3.6.29:
  - "Fixes selection in dropdown for site_rood_id"
3.6.30:
  - "Fixed invisible table popup when richeditor is in fullscreen mode"
3.6.31:
  - "Added support for Partial descriptions"
3.6.32:
  - "Added support for Phosphor icons"
3.6.33:
  - "Fixed repeater items relation"
3.6.34:
  - "Fixed repeater items relation"
3.6.35:
  - "Fixed repeater items relation"
3.6.36:
  - "Fixed repeater items relation"
3.6.37:
  - "Fixed isActive menu state for homepage"
3.6.38:
  - "Fixed copying to other Site with child themes"
3.6.40:
  - "Allow page templates to be applied to empty pages"
3.6.41:
  - "Added support for boxes.yaml in child-themes"
3.6.42:
  - "Fixed copying to other Site with child themes"
3.6.43:
  - "Improved SQLite support"
3.6.44:
  - "Improved SQLite support"
3.6.45:
  - "Include parent name in add child box link"
3.6.46:
  - "Added support for Mixins in Repeater Groups"
3.6.47:
  - "Fixed Tailor content integration"
3.6.48:
  - "Added option to limit the maximum of nested page levels"
3.6.49:
  - "Added order property to yaml schema, to manually sort partials in the selector"
3.6.50:
  - "Fixed bug where usage in Tailor initialized two Cotnent models"
3.6.51:
    - "Updated rendering of Page structure when References are used"
3.6.52:
    - "Display all sites in root page dropdown"
3.6.53:
    - "Fixed site context in Content form widget"
3.6.54:
    - "Allow references to pages on other sites"
3.6.55:
    - "Reference picker bugfix"
3.6.56:
    - "Fixed rendering of ajax partials"
3.6.57:
    - "Added quick toggle to enable Box"
3.6.58:
    - "Fixed sorting of Pages/Boxes"
3.6.59:
    - "Added middleware exceptions for preview page"
3.6.60:
    - "Made Editor breakpoints configurable (see https://docs.boxes.offline.ch/use-cases/editor-customization.html)"
3.6.61:
    - "Changed handling of Box focus"
3.6.62:
    - "Changed handling of Box focus"
3.6.63:
    - "Save current Box when parent model is saved"
3.6.64:
    - "Added offline.boxes.extendPartial event"
3.6.65:
    - "Override tag in mixin even if the mixin sets one as default"
3.6.66:
    - "Added change monitor"
3.6.67:
    - "Fixed change monitor"
3.6.68:
    - "Added alternate_locale_urls to menu items"
3.6.69:
    - "Fixed multisite scope"
3.6.70:
    - "Fixed handling of custom partial paths on Windows"
3.6.71:
    - "Fixed change monitor"
3.6.72:
    - "Added site prefix to menu items"
3.6.73:
    - "Added code property to menu items"
3.6.74:
    - "Set default modelType value for BoxesPage component"
3.6.75:
    - "Fixed change monitor when used in custom plugins"
3.6.76:
    - "Made pages list resizeable"
3.6.78:
    - "Fixed alternate locale URL generation"
