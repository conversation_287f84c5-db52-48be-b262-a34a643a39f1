<?php

namespace Instalweb\Ketels\Controllers;

use Artisan;
use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Flash;
use Instalweb\Ketels\Models\Thermostat;

class Thermostats extends Controller
{
    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public $listConfig = 'config_list.yaml';

    public $formConfig = 'config_form.yaml';

    public $reorderConfig = 'config_reorder.yaml';

    public $importExportConfig = 'config_import_export.yaml';

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Instalweb.Ketels', 'main-menu-item', 'side-menu-item3');
    }

    public function onDuplicate()
    {
        $checked_items_ids = input('checked');

        foreach ($checked_items_ids as $id) {
            $original = Thermostat::where('id', $id)->first();

            $clone = $original->replicate();
            $clone->title = $clone->title.' (Kopie)';
            $clone->id = Thermostat::withTrashed()->max('id') + 1;
            $clone->save();
        }

        Flash::success('Event cloned');

        return $this->listRefresh();
    }

    public function onSync()
    {
        // Voer de synchronisatie uit
        $result = Artisan::call('instalweb:ketel:sync-from-api-endpoint');

        // Toon een flash bericht
        Flash::success('Thermostaten zijn gesynchroniseerd.');

        // Herlaad de lijst
        return $this->listRefresh();
    }
}
