<?php

namespace Instalweb\Ketels;

use App\Console\Commands\KetelSyncFromApiEndpointCommand;
use Event;
use Illuminate\Support\Facades\Route;
use Instalweb\Ketels\Classes\KetelSyncController;
use Instalweb\Ketels\Models\Boiler;
use Instalweb\Ketels\Models\Brand;
use Instalweb\Ketels\Models\KetelSettings;
use System\Classes\PluginBase;

class Plugin extends PluginBase
{
    public function boot()
    {
        Route::get('/cv-ketels/api/sync/{resource}', [KetelSyncController::class, 'apiEndpoint']);
    }

    public function register()
    {
        $this->registerConsoleCommand('instalweb.ketel.sync', KetelSyncFromApiEndpointCommand::class);
    }

    public function registerSchedule($schedule)
    {
        $ketelSettings = KetelSettings::instance();

        if (
            isset(
                $ketelSettings['isApiEndpoint'],
                $ketelSettings['syncWithDomain'],
                $ketelSettings['authToken']
            )
            &&
            $ketelSettings['isApiEndpoint'] !== '1'
        ) {
            $schedule->command('instalweb:ketel:sync-from-api-endpoint')
                ->timezone('Europe/Amsterdam')
                ->daily();
        }
    }

    public function registerComponents()
    {
        return [
            'Instalweb\Ketels\Components\KetelList' => 'ketelList',
            'Instalweb\Ketels\Components\KetelDetail' => 'ketelDetail',
            'Instalweb\Ketels\Components\BrandList' => 'brandList',
            'Instalweb\Ketels\Components\ThermostatList' => 'thermostatList',
            'Instalweb\Ketels\Components\DownloadsList' => 'downloadsList',
        ];
    }

    public function registerSettings()
    {
        return [
            'settings' => [
                'label' => 'Ketel instellingen',
                'description' => 'Beheer ketel instellingen',
                'category' => 'CV Ketels',
                'icon' => 'icon-cog',
                'class' => KetelSettings::class,
            ],
        ];
    }

    public function registerMailTemplates()
    {
        return [
            'templates' => [
                'instalweb.ketels::mail.offer-consumer',
                'instalweb.ketels::mail.offer-company',
                'instalweb.ketels::mail.akkoord-consumer',
                'instalweb.ketels::mail.akkoord-company',
            ],
        ];
    }

    protected function registerPageFinder(): void
    {
        $listTypes = function () {
            return [
                'ketel-boiler' => 'CV Ketel',
                'all-ketel-boilers' => 'Alle cv-ketels',
                'ketel-brand' => 'Ketel merk',
                'all-ketel-brands' => 'Alle ketel merken',
            ];
        };

        $getTypeInfo = function ($type) {
            if ($type == 'ketel-boiler' || $type == 'all-ketel-boilers') {
                return Boiler::getMenuTypeInfo($type);
            }
            if ($type == 'ketel-brand' || $type == 'all-ketel-brands') {
                return Brand::getMenuTypeInfo($type);
            }

            return [];
        };

        $resolveItem = function ($type, $item, $url, $theme) {
            if ($type == 'ketel-post' || $type == 'all-ketel-posts') {
                return Boiler::resolveMenuItem($item, $url, $theme);
            }
            if ($type == 'ketel-category' || $type == 'all-ketel-categories') {
                return Brand::resolveMenuItem($item, $url, $theme);
            }

            return null;
        };

        Event::listen([
            'cms.pageLookup.listTypes',
            'pages.menuitem.listTypes',
        ], $listTypes);

        Event::listen([
            'cms.pageLookup.getTypeInfo',
            'pages.menuitem.getTypeInfo',
        ], $getTypeInfo);

        Event::listen([
            'cms.pageLookup.resolveItem',
            'pages.menuitem.resolveItem',
        ], $resolveItem);

    }
}
