<?php

namespace Instalweb\Ketels\Models;

use Model;
use October\Rain\Database\Traits\Nullable;
use October\Rain\Database\Traits\SimpleTree;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;

/**
 * Model
 */
class Thermostat extends Model
{
    use Nullable;
    use SimpleTree;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected $dates = ['deleted_at'];

    protected $fillable = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'sort_order',
        'merken_id',
        'title',
        'description',
        'image',
        'price',
        'brochure',
        'manual',
        'inactive',
        'boiler_id',
    ];

    protected $nullable = [
        'title',
        'price',
        'image',
        'description',
        'brochure',
        'manual',
    ];

    public $mediaAttributes = [
        'image',
        'brochure',
        'manual',
    ];

    public static $syncIgnoredAttributes = [
        'price',
        'inactive',
    ];

    /**
     * @var string The database table used by the model.
     */
    public $table = 'instalweb_ketels_thermostats';

    /**
     * @var array Validation rules
     */
    public $rules = [
    ];

    public $belongsTo = [
        'merken' => Brand::class,
    ];

    public $belongsToMany = [
        'ketels' => [
            Boiler::class,
            'table' => 'instalweb_ketels_b_t',
        ],
        'offers' => [
            Offer::class,
            'table' => 'instalweb_ketels_offer_therm',
        ],
    ];
}
