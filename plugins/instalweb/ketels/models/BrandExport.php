<?php namespace Instalweb\Ketels\Models;

use \Instalweb\Ketels\Models\Brand;

class BrandExport extends \Backend\Models\ExportModel
{
    public $rules = [];

    public function exportData($columns, $sessionKey = null)
    {
        $brands = Brand::all();

        $brands->each(function($brand) use ($columns) {
            $brand->addVisible($columns);
        });

        return $brands->toArray();
    }
}
