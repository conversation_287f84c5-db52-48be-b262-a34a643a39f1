<?php

namespace Instalweb\Ketels\Models;

use Cms\Classes\Controller;
use Cms\Classes\Page as CmsPage;
use Cms\Classes\Theme;
use Model;
use October\Rain\Database\Traits\Nullable;
use October\Rain\Database\Traits\SimpleTree;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;

class Brand extends Model
{
    use Nullable;
    use SimpleTree;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected $dates = ['deleted_at'];

    protected $jsonable = ['seo'];

    protected $slugs = ['slug' => 'title'];

    protected $fillable = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'sort_order',
        'parent_id',
        'title',
        'slug',
        'description',
        'inactive',
        'special',
        'meta_title',
        'meta_description',
        'logo',
        'type',
        'seo',
    ];

    protected $nullable = [
        'title',
        'slug',
        'type',
        'inactive',
        'special',
        'description',
        'logo',
        'seo',
    ];

    public $mediaAttributes = [
        'logo',
    ];

    public static $syncIgnoredAttributes = [
        'inactive',
        'sort_order',
        'special',
        'description',
    ];

    /**
     * @var string The database table used by the model.
     */
    public $table = 'instalweb_ketels_merken';

    /**
     * @var array Validation rules
     */
    public $rules = [
        'title' => ['required'],
        'slug' => ['required'],
    ];

    public $hasMany = [
        'ketels' => [
            Boiler::class,
            'order' => 'sort_order',
        ],
        'thermostats' => [
            Thermostat::class,
            'order' => 'sort_order',
        ],
    ];

    public static function getMenuTypeInfo($type)
    {
        $result = [];

        if ($type == 'ketel-brand') {
            $references = [];

            $posts = self::orderBy('title')->get();
            foreach ($posts as $post) {
                $references[$post->id] = $post->title;
            }

            $result = [
                'references' => $references,
                'nesting' => false,
                'dynamicItems' => false,
            ];
        }

        if ($type == 'all-ketel-brands') {
            $result = [
                'nesting' => true,
                'dynamicItems' => true,
            ];
        }

        if ($result) {
            $theme = Theme::getActiveTheme();

            $pages = CmsPage::listInTheme($theme, true);
            $cmsPages = [];

            foreach ($pages as $page) {
                if (! $page->hasComponent('brandList')) {
                    continue;
                }

                $properties = $page->getComponentProperties('brandList');
                if (! preg_match('/{{\s*:/', $properties['slug'])) {
                    continue;
                }

                $cmsPages[] = $page;
            }

            $result['cmsPages'] = $cmsPages;
        }

        return $result;
    }

    public static function resolveMenuItem($item, $url, $theme)
    {
        $result = null;

        if ($item->type == 'ketel-brand') {
            $model = Brand::find($item->reference);

            if (! $model) {
                return;
            }

            $controller = new Controller($theme);
            $pageUrl = $controller->pageUrl($item->cmsPage, [
                'id' => $model->id,
                'slug' => $model->slug,
            ]);

            $result = [
                'url' => $pageUrl,
                'isActive' => $pageUrl == $url,
                'title' => $model->title,
                'mtime' => $model->updated_at,
            ];

            return $result;
        } elseif ($item->type == 'all-ketel-brands') {
            $result = [
                'items' => [],
            ];

            $posts = self::orderBy('title')->get();
            $controller = new Controller($theme);

            foreach ($posts as $post) {
                $pageUrl = $controller->pageUrl($item->cmsPage, [
                    'id' => $post->id,
                    'slug' => $post->slug,
                ]);

                $postItem = [
                    'title' => $post->title,
                    'url' => $pageUrl,
                    'mtime' => $post->updated_at,
                ];

                $postItem['isActive'] = $postItem['url'] == $url;

                $result['items'][] = $postItem;
            }
        }

        return $result;
    }
}
