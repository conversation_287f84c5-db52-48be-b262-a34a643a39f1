<?php namespace Instalweb\Ketels\Models;

use \Instalweb\Ketels\Models\Thermostat;

class ThermostatExport extends \Backend\Models\ExportModel
{
    public $rules = [];

    public function exportData($columns, $sessionKey = null)
    {
        $thermostats = Thermostat::all();

        $thermostats->each(function($thermostat) use ($columns) {
            $thermostat->addVisible($columns);
        });

        return $thermostats->toArray();
    }
}
