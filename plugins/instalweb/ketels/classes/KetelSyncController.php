<?php

namespace Instalweb\Ketels\Classes;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Instalweb\Ketels\Models\Boiler;
use Instalweb\Ketels\Models\Brand;
use Instalweb\Ketels\Models\KetelSettings;
use Instalweb\Ketels\Models\Thermostat;

class KetelSyncController
{
    public function apiEndpoint(Request $request, string $resource): JsonResponse
    {
        $authKey = $request->bearerToken();

        $data = [
            'success' => false,
        ];

        $settings = KetelSettings::instance();

        if (
            ! isset($authKey)
            ||
            ! $this->checkAuthKey($settings, $authKey)
        ) {
            $data['message'] = 'Authorization failed';

            return response()->json($data, 401);
        }

        if (! $this->maySendData($settings)) {
            $data['message'] = 'Endpoint disabled';

            return response()->json($data, 404);
        }

        $resourceData = match ($resource) {
            'brands' => $this->getBrands(),
            'boilers' => $this->getBoilers(),
            'thermostats' => $this->getThermostats(),
        };

        $data['resources'] = $resourceData;
        $data['resource'] = $resource;
        $data['success'] = true;

        return response()->json($data);
    }

    private function checkAuthKey(KetelSettings $settings, string $authKey): bool
    {
        return isset($settings['authToken']) && $settings['authToken'] === $authKey;
    }

    private function maySendData(KetelSettings $settings): bool
    {
        return isset($settings['isApiEndpoint']) && $settings['isApiEndpoint'] = '1';
    }

    private function getBrands(): array
    {
        return Brand::withTrashed()
            ->get()
            ->toArray();
    }

    private function getBoilers(): array
    {
        $boilers = Boiler::withTrashed()
            ->get();

        $data = [];

        foreach ($boilers as $boiler) {
            $data[$boiler->id] = [
                'brand' => $boiler->merken?->toArray(),
                'boiler' => $boiler->toArray(),
                'thermostats' => $boiler->thermostats?->toArray(),
            ];
        }

        return $data;
    }

    private function getThermostats(): array
    {
        $thermostats = Thermostat::withTrashed()
            ->get();

        $data = [];

        foreach ($thermostats as $thermostat) {
            $data[$thermostat->id] = [
                'brand' => $thermostat->merken?->toArray(),
                'thermostat' => $thermostat->toArray(),
            ];
        }

        return $data;
    }
}
