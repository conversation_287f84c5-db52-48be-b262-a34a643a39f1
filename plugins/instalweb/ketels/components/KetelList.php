<?php namespace Instalweb\Ketels\Components;

use Cms\Classes\ComponentBase;
use Db;
use Instalweb\Ketels\Models\Boiler;
use Instalweb\Ketels\Models\Brand;

class KetelList extends ComponentBase
{
    /**
     * A collection of records to display
     * @var \October\Rain\Database\Collection
     */
    public $brands;
    public $boilers;



    public function componentDetails()
    {
      return [
        'name'        => 'Ketel overzicht',
        'description' => 'CV Ketel overzichtpagina'
      ];
    }

    //
    // Properties
    //

    public function defineProperties()
    {
      return [];
    }
    //
    // Rendering and processing
    //

    public function onRun()
    {
      $this->brands = Brand::where('inactive', false)->where('type', 'boiler')->orderBy('sort_order')->get();
      $this->boilers = $this->page['boilers'] = $this->getBoilers();
    }

    public function onChangeFilter()
    {
        $oppervlak = post('oppervlak');
        $waterbehoefte = post('waterbehoefte');

        if ( $waterbehoefte == 'tweeDouches' || $waterbehoefte == 'badDoucheTegelijk' ) {
            $this->boilers = $this->page['boilers'] = false;
        } else {

            if ( $oppervlak == 'oppervlakteExtraKlein' || $oppervlak == 'oppervlakteKlein' ) {
                $this->boilers = $this->page['boilers'] = Boiler::orderBy('sort_order')->where('comfort_level', '4')->get();

                if ( $waterbehoefte ) {
                    if ( $waterbehoefte == 'stortDouche' || $waterbehoefte == 'grootBad' ) {
                        $this->boilers = $this->page['boilers'] = Boiler::orderBy('sort_order')->where('comfort_level', '4')->get();
                    }
                }
            }

            if ( $oppervlak == 'oppervlakteNormaal' ) {
                $this->boilers = $this->page['boilers'] = Boiler::orderBy('sort_order')->where('comfort_level', '4')->get();

                if ( $waterbehoefte ) {
                    if ( $waterbehoefte == 'stortdouche' || $waterbehoefte == 'grootBad' ) {
                        $this->boilers = $this->page['boilers'] = Boiler::orderBy('sort_order')->where('comfort_level', '5')->get();
                    }
                }
            }

            if ( $oppervlak == 'oppervlakteMedium' || $oppervlak == 'oppervlakteGroot' ) {
                $this->boilers = $this->page['boilers'] = Boiler::orderBy('sort_order')->where('comfort_level', '5')->get();

                if ( $waterbehoefte ) {
                    if ( $waterbehoefte == 'stortdouche' || $waterbehoefte == 'grootBad' ) {
                        $this->boilers = $this->page['boilers'] = Boiler::orderBy('sort_order')->where('comfort_level', '5')->get();
                    }
                }
            }
        }
    }

    public function onChangeSorting()
    {
        $this->page['sorting'] = post('sorting');
    }

    public function getBoilers()
    {
        return Boiler::withoutTrashed()
            ->join('instalweb_ketels_merken', 'instalweb_ketels_ketels.merken_id', '=', 'instalweb_ketels_merken.id')
            ->orderBy('instalweb_ketels_merken.sort_order')
            ->orderBy('instalweb_ketels_ketels.sort_order')
            ->select('instalweb_ketels_ketels.*')
            ->get();
    }

}
