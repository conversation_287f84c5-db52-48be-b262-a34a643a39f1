<div class="flex flex-col">
    <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8 -mt-4">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <table class="min-w-full divide-y divide-gray-300">
          <tbody class="divide-y divide-gray-200">
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">{{ 'Merk'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ ketel.merken.title }}</td>
            </tr>
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">{{ 'Serie'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ ketel.series|default('-') }}</td>
            </tr>
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">{{ 'Type'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ ketel.type|default('-') }}</td>
            </tr>
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">
                {{ 'Comfort waarde'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ ketel.comfort_level|default('-') }}</td>
            </tr>
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">
                {{ 'Tapcapaciteit bij 60°C'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ ketel.tap_capacity_60|default('-') }} {{ ketel.tap_capacity_60 ? 'liter/min'|_ }}</td>
            </tr>
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">
                {{ 'Tapcapaciteit bij 40°C'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ ketel.tap_capacity_40|default('-') }} {{ ketel.tap_capacity_40 ? 'liter/min'|_ }}</td>
            </tr>
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">
                {{ 'Capaciteit CV'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ ketel.capacity|default('-') }}{{ ketel.capacity ? 'kW'|_ }}</td>
            </tr>
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">{{ 'Gewicht'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ ketel.weight|default('-') }}{{ ketel.weight ? 'kg'|_ }}</td>
            </tr>
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">{{ 'Afmeting
                (lxbxh)'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ ketel.size|default('-') }}{{ ketel.size ? 'cm'|_ }}</td>
            </tr>
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">{{ 'Rendement'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ ketel.efficiency|default('-') }}</td>
            </tr>
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">
                {{ 'A-label pomp'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">
                {% if ketel.a_label_pump %}
                {{ 'Ja'|_ }}
                {% else %}
                {{ 'Nee'|_ }}
                {% endif %}
              </td>
            </tr>
            <tr>
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">
                {{ 'Garantie warmtewisselaar'|_ }}</td>
              <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500">{{ ketel.warranty_heat_exchanger|default('-') }} {{ ketel.warranty_heat_exchanger ? 'jaar'|_ }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
