{% set ketel = __SELF__.boiler %}
{% macro euro(number) %}
	&euro; {{ number | number_format(2, ',', '.') }}
{% endmacro %}
{% import _self as format %}

{% put scripts %}
<script>
    let euro = Intl.NumberFormat('nl-NL', {
        style: 'currency',
        currency: 'EUR',
    });

    calculateOffer();

    $('.addon_field').on('click', function(){
		var input = $(this).find('input');
		var addon_key = 'offer_overview_addon_' + input.val();
		var overview_item = $('body').find('#' + addon_key);

		if ( input.is(':checked' ) ) {
			input.prop("checked", false).attr('checked', false);
			overview_item.hide().removeClass('countItem');
            overview_item.find('.addonItem').removeClass('countItem')
            input.trigger('change');
		} else {
			input.prop("checked", true).attr('checked', true);
			overview_item.show();
            overview_item.find('.addonItem').addClass('countItem')
            input.trigger('change');
		}
    });

    $('.thermostat-option').on('click', function(){
        var price = $(this).data('price');
        var ovItem = $('#offer_overview .thermostatItem');
        ovItem.data('price', price);
    });

    $('form').on('change', 'input', function(){
        calculateOffer();
    });

    function calculateOffer() {
        var ov = $('#offer_overview');
        var countItems = ov.find('.countItem');
        var totalprice = 0;
        var totalpriceText = ov.find('.offer-totalprice');

        countItems.each(function(){
            var price = $(this).data('price');
            totalprice = (totalprice * 1) + (price * 1);
        });

		$('input[name="totalprice"]').val(totalprice);
        totalpriceText.text(euro.format(totalprice));
    }
</script>
{% endput %}

<div
	class="bg-gray-100"
	id="ketelofferte"
	x-data="{
		thermostat_type: null,
        thermostat_price: null,
        thermostat_cleanprice: 0,
        addons: []
	}"
>
	<div class="mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:max-w-7xl lg:px-8">

		<div class="mb-16">
			<h2 class="mb-6">Offerte aanvragen</h2>
			<form
				data-request="onSubmitOffer"
				data-request-success="$('.form-section').hide(); $('#message').show(); $('#message').animate({ scrollTop: 0 }, 'fast');"
				class="md:grid md:grid-cols-12 md:gap-8 lg:gap-12"
			>
				<input type="hidden" name="boiler" value="{{ ketel.id }}">
				<input type="hidden" name="boilername" value="{{ ketel.merken.title }} {{ ketel.title }}">
				<input type="hidden" name="companyname" value="{{ company.name ? company.name : this.theme.company.name }}">
				<input type="hidden" name="offerlink" value="{{ url('/') }}/ketel-offerte?offer=">
				<input type="hidden" name="totalprice" value="">

				<div class="col-span-12" id="message" style="display: none;">
					<div class="px-5 py-3.5 bg-lime-600 text-white rounded-md text-lg font-semibold">
						<p><b>Uw offerte is succesvol verstuurd!</b> Bedankt voor uw offerte aanvraag. U heeft een mail ontvangen met daarin een link naar uw offerte.</p>
					</div>
				</div>

				<div class="col-span-8 form-section">

					<fieldset class="">
						<h3 class="mb-4">Kies je thermostaat</h3>

						<div class="md:grid md:grid-cols-4 md:gap-4">

							{% for item in ketel.thermostats %}
								{% partial '@thermostat-offer' item = item %}
							{% endfor %}

							{% partial '@thermostat-offer' emptyOption = "true" %}

						</div>
					</fieldset>

					<hr class="my-12 border-gray-400 border-dashed">

					<fieldset class="">
						<h3 class="mb-6">Kies je extra pakketten</h3>

						{% for item in __SELF__.addons.where('is_active', '=', '1') %}
							{% partial '@addon-offer' item = item %}
						{% endfor %}
					</fieldset>

					<hr class="my-12 border-gray-400 border-dashed">

					<fieldset class="">
						<h3 class="mb-6">Foto's uploaden</h3>

						<label for="cover-photo" class="block text-sm font-medium leading-6 text-gray-900">Voeg foto's bij van uw huidige cv-ketel situatie</label>
						<div class="w-1/3">
							<label class="mt-2 bg-white flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10 cursor-pointer hover:border-gray-900/50 group">
								<div class="text-center">
									<svg class="mx-auto h-12 w-12 text-gray-300" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
										<path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 012.25-2.25h16.5A2.25 2.25 0 0122.5 6v12a2.25 2.25 0 01-2.25 2.25H3.75A2.25 2.25 0 011.5 18V6zM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0021 18v-1.94l-2.69-2.689a1.5 1.5 0 00-2.12 0l-.88.879.97.97a.75.75 0 11-1.06 1.06l-5.16-5.159a1.5 1.5 0 00-2.12 0L3 16.061zm10.125-7.81a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0z" clip-rule="evenodd" />
									</svg>
									<div class="mt-4 block text-center text-sm leading-6 text-gray-600">
										<label for="huidige_situatie" class="relative text-center cursor-pointer rounded-md bg-white font-semibold text-primary-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-600 focus-within:ring-offset-2 group-hover:text-primary-700">
											<span>Upload een foto</span>
											<input id="huidige_situatie" name="huidige_situatie" type="file" class="sr-only">
										</label>
									</div>
									<p class="text-xs leading-5 text-gray-600">PNG, JPG, GIF tot max. 10MB</p>
								</div>
							</label>
						</div>
					</fieldset>

					<hr class="my-12 border-gray-400 border-dashed">

					<fieldset class="">
						<h3 class="mb-6">Uw gegevens</h3>

						<div class="md:grid md:grid-cols-12 -mx-4">
							<div class="md:col-span-3">
								{% partial '@field_select_aanhef' name="aanhef" label="Aanhef" %}
							</div>
							<div class="md:col-span-4">
								{% partial '@field_text' name="voornaam" label="Voornaam" %}
							</div>
							<div class="md:col-span-5">
								{% partial '@field_text' name="achternaam" label="Achternaam" required="true" %}
							</div>

							<div class="md:col-span-6">
								{% partial '@field_text' name="straatnaam" label="Straatnaam" required="true" %}
							</div>
							<div class="md:col-span-3">
								{% partial '@field_text' name="huisnummer" label="Huisnummer" required="true" %}
							</div>
							<div class="md:col-span-3">
								{% partial '@field_text' name="toevoeging" label="Toevoeging" %}
							</div>

							<div class="md:col-span-4">
								{% partial '@field_text' name="postcode" label="Postcode" required="true" %}
							</div>
							<div class="md:col-span-8">
								{% partial '@field_text' name="woonplaats" label="Woonplaats" required="true" %}
							</div>

							<div class="md:col-span-6">
								{% partial '@field_text' name="emailadres" label="E-mailadres" required="true" %}
							</div>
							<div class="md:col-span-6">
								{% partial '@field_text' name="telefoonnummer" label="Telefoonnummer" %}
							</div>

							<div class="col-span-12">
								<div class="form-field-wrapper">
									<button type="submit" class="py-3.5 px-8 rounded bg-primary-500 text-white font-medium">Offerte aanvragen</button>
								</div>
							</div>

						</div>


					</fieldset>
				</div>
				<div class="col-span-4 form-section">
					<div class="top-8 sticky rounded-lg bg-primary-500 text-white p-6 shadow-xl" id="offer_overview">
						<div class="text-xl font-bold mb-2">Uw offerte:</div>

						<table class="text-sm text-left w-full font-medium">
							<tbody>
								<tr>
									<th class="py-1.5">{{ ketel.merken.title }} {{ ketel.title }}</th>
									<td class="py-1.5 text-right countItem" data-price="{{ ketel.price }}">{{ format.euro(ketel.price) }}</td>
								</tr>
								<tr x-show="thermostat_type">
									<th class="py-1.5"><span x-text="thermostat_type"></span></th>
									<td class="py-1.5 text-right countItem thermostatItem" data-price="0"><span class="before:content-['€'] before:mr-1" x-text="thermostat_price"></span></td>
								</tr>
								{% for item in __SELF__.addons.where('is_active', '=', '1').where('is_required', '=', '1') %}
									<tr>
										<th class="py-1.5">{{ item.title }}</th>
										<td class="py-1.5 text-right countItem" data-price="{{ item.price }}">{{ format.euro(item.price) }}</td>
									</tr>
								{% endfor %}
								{% for item in __SELF__.addons.where('is_active', '=', '1').where('is_required', '=', '0') %}
									<tr id="offer_overview_addon_{{ item.id }}" style="display: none;">
										<th class="py-1.5">{{ item.title }}</th>
										<td class="py-1.5 text-right addonItem" data-price="{{ item.price }}">{{ format.euro(item.price) }}</td>
									</tr>
								{% endfor %}
							</tbody>
							<tfoot class="">
								<tr>
									<th colspan="2">
										<div class="w-full my-3 border-t border-white"></div>
									</th>
								</tr>
								<tr>
									<th class="pt-1.5 text-right text-lg">Totaal:</th>
									<th class="pt-1.5 text-right text-lg"><span class="offer-totalprice">{{ format.euro(ketel.price) }}</span></th>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
