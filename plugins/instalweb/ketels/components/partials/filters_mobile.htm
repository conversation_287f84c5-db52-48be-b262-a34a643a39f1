<div class="fixed inset-0 flex z-40 lg:hidden" role="dialog" aria-modal="true" x-show="open" x-cloak>
  <div
    class="fixed inset-0 bg-black bg-opacity-25"
    aria-hidden="true"
    x-show="open"
    x-transition:enter="transition-opacity ease-linear duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition-opacity ease-linear duration-300"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    :aria-hidden="!(open)"
  >
  </div>

  <div
    class="ml-auto relative max-w-xs w-full h-full bg-white shadow-xl py-4 pb-12 flex flex-col overflow-y-auto"
    x-show="open"
    x-transition:enter="transition ease-in-out duration-300 transform"
    x-transition:enter-start="translate-x-full"
    x-transition:enter-end="translate-x-0"
    x-transition:leave="transition ease-in-out duration-300 transform"
    x-transition:leave-start="translate-x-0"
    x-transition:leave-end="translate-x-full"
    @click.away="close"
  >
    <div class="px-4 flex items-center justify-between">
        <h2 class="text-lg font-medium text-gray-900">{{ 'Filters'|_ }}</h2>
        <button type="button" class="-mr-2 w-10 h-10 bg-white p-2 rounded-md flex items-center justify-center text-gray-400" @click="close">
            <span class="sr-only">{{ 'Close menu'|_ }}</span>
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>

    <!-- Filters -->
    <form class="mt-4 border-t border-gray-200">
        <h3 class="sr-only">{{ 'Merken'|_ }}</h3>
        <ul role="list" class="font-medium text-gray-900 px-2 py-3">
          {% for item in brands %}
            <li>
                <a href="/cv-ketels/{{ item.slug }}" class="block px-2 py-3">
                    {{ item.title }}
                </a>
            </li>
          {% endfor %}
        </ul>

        <div class="border-t border-gray-200 px-4 py-6" x-data="openClose">
            <h3 class="-mx-2 -my-3 flow-root">
              <!-- Expand/collapse section button -->
              <button type="button" class="px-2 py-3 bg-white w-full flex items-center justify-between text-gray-400 hover:text-gray-500" aria-controls="filter-section-mobile-0" aria-expanded="false" @click="toggle">
                <span class="font-medium text-gray-900">
                  {{ 'In welk type woning woont u?'|_ }}
                </span>
                <span class="ml-6 flex items-center">
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" x-show="!(open)">
                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                  </svg>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" x-show="open">
                    <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd" />
                  </svg>
                </span>
              </button>
            </h3>
            <!-- Filter section, show/hide based on section state. -->
            <div class="pt-6" id="filter-section-mobile-0" x-show="open">
                <div class="space-y-6">
                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-woningtype-0" name="woningtype" value="appartement" noRequest="true" %}
                        <label for="mobile-woningtype-0" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ 'Appartement'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-woningtype-1" name="woningtype" value="tussenwoning" noRequest="true" %}
                        <label for="mobile-woningtype-1" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ 'Tussenwoning'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-woningtype-2" name="woningtype" value="tweeOnderEenKap" noRequest="true" %}
                        <label for="mobile-woningtype-2" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ 'Hoekwoning'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-woningtype-3" name="woningtype" value="tweeOnderEenKap" noRequest="true" %}
                        <label for="mobile-woningtype-3" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ '2 onder 1 kap'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-woningtype-4" name="woningtype" value="vrijstaand" noRequest="true" %}
                        <label for="mobile-woningtype-4" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ 'Vrijstaande woning'|_ }}
                        </label>
                    </div>


                </div>
            </div>
        </div>

        <div class="border-t border-gray-200 px-4 py-6" x-data="openClose">
            <h3 class="-mx-2 -my-3 flow-root">
              <!-- Expand/collapse section button -->
              <button type="button" class="px-2 py-3 bg-white w-full flex items-center justify-between text-gray-400 hover:text-gray-500" aria-controls="filter-section-mobile-0" aria-expanded="false" @click="toggle">
                <span class="font-medium text-gray-900">
                  {{ 'Wat is uw woningoppervlakte?'|_ }}
                </span>
                <span class="ml-6 flex items-center">
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" x-show="!(open)">
                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                  </svg>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" x-show="open">
                    <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd" />
                  </svg>
                </span>
              </button>
            </h3>
            <!-- Filter section, show/hide based on section state. -->
            <div class="pt-6" id="filter-section-mobile-0" x-show="open">
                <div class="space-y-6">
                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-oppervlak-0" name="oppervlak" value="oppervlakteExtraKlein" %}
                        <label for="mobile-oppervlak-0" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ '< 80 m2'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-oppervlak-1" name="oppervlak" value="oppervlakteKlein" %}
                        <label for="mobile-oppervlak-1" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ '80 m2 - 120 m2'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-oppervlak-2" name="oppervlak" value="oppervlakteNormaal" %}
                        <label for="mobile-oppervlak-2" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ '120 m2 - 160 m2'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-oppervlak-3" name="oppervlak" value="oppervlakteMedium" %}
                        <label for="mobile-oppervlak-3" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ '160 m2 - 200 m2'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-oppervlak-4" name="oppervlak" value="oppervlakteGroot" %}
                        <label for="mobile-oppervlak-4" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ '200 m2 - 240 m2'|_ }}
                        </label>
                    </div>


                </div>
            </div>
        </div>

        <div class="border-t border-gray-200 px-4 py-6" x-data="openClose">
            <h3 class="-mx-2 -my-3 flow-root">
              <!-- Expand/collapse section button -->
              <button type="button" class="px-2 py-3 bg-white w-full flex items-center justify-between text-gray-400 hover:text-gray-500" aria-controls="filter-section-mobile-0" aria-expanded="false" @click="toggle">
                <span class="font-medium text-gray-900">
                  {{ 'Warmwaterbehoefte'|_ }}
                </span>
                <span class="ml-6 flex items-center">
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" x-show="!(open)">
                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                  </svg>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" x-show="open">
                    <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd" />
                  </svg>
                </span>
              </button>
            </h3>
            <!-- Filter section, show/hide based on section state. -->
            <div class="pt-6" id="filter-section-mobile-0" x-show="open">
                <div class="space-y-6">
                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-waterbehoefte-0" name="waterbehoefte" value="douche" %}
                        <label for="mobile-waterbehoefte-0" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ 'Douche'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-waterbehoefte-1" name="waterbehoefte" value="stortdouche" %}
                        <label for="mobile-waterbehoefte-1" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ 'Stort douche'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-waterbehoefte-2" name="waterbehoefte" value="bad" %}
                        <label for="mobile-waterbehoefte-2" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ 'Bad'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-waterbehoefte-3" name="waterbehoefte" value="grootBad" %}
                        <label for="mobile-waterbehoefte-3" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ 'Groot bad'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-waterbehoefte-4" name="waterbehoefte" value="tweeDouches" %}
                        <label for="mobile-waterbehoefte-4" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ '2 douches'|_ }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        {% partial '@radio' id="mobile-waterbehoefte-4" name="waterbehoefte" value="badDoucheTegelijk" %}
                        <label for="mobile-waterbehoefte-4" class="ml-3 min-w-0 flex-1 text-gray-500">
                            {{ 'Bad & douche tegelijk'|_ }}
                        </label>
                    </div>


                </div>
            </div>
        </div>

    </form>
  </div>
</div>
