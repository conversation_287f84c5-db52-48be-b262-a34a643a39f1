{% macro euro(number) %}
    &euro; {{ number | number_format(2, ',', '.') }}
{% endmacro %}
{% import _self as format %}
{% set price = format.euro(item.price) %}

<div
    class="{{ not item.is_required ? 'addon_field' }}" x-data="{ checked: {{ item.is_required ? 'true' : 'false'  }}, disabled: {{ item.is_required ? 'true' : 'false' }} }"
>
    <div>
        <div class="bg-white block rounded-lg shadow-sm border {{ item.is_required ? 'cursor-not-allowed pointer-events-none' : 'cursor-pointer' }} {{ not loop.last ? 'mb-2' }}" @click="checked = !(checked)">
            <div class="flex gap-x-8 py-3 px-6">
                <div>
                    <div
                        class=""
                    >
                        <input
                            type="checkbox"
                            value="{{ item.id }}"
                            name="addons[]"
                            class="addon_checkbox sr-only"
                            id="addon_{{ item.id }}"
                            data-title="{{ item.title }}"
                            data-price="{{ price }}"
                            data-cleanprice="{{ item.price }}"
                            {{ item.is_required ? 'checked' : '' }}
                        >
                        <!-- Button -->
                        <div
                            :class="disabled ? 'bg-gray-300' : checked ? 'bg-primary-600' : 'bg-gray-400'"
                            class="relative inline-flex w-10 rounded-full py-1 transition"
                            {{ item.is_required ? 'disabled' : ''  }}
                        >
                            <span
                                :class="checked ? 'translate-x-5' : 'translate-x-1'"
                                class="bg-white h-4 w-4 rounded-full transition shadow-md"
                                aria-hidden="true"
                            ></span>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="font-semibold flex items-center gap-x-4">
                        <div>{{ item.title }}</div>
                        <div class="text-primary-500 text-sm">{{ format.euro(item.price) }}</div>
                    </div>
                    {% if item.description %}<div class="text-sm text-gray-600 mt-1">{{ item.description | content }}</div>{% endif %}
                </div>
            </div>
        </div>
    </div>
</div>