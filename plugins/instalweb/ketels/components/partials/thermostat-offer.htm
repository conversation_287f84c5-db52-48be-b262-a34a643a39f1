{% macro euro(number) %}
    &euro; {{ number | number_format(2, ',', '.') }}
{% endmacro %}
{% import _self as format %}

{% set thermostat_price = format.euro(item.price) %}

<label
    class="thermostat-option flex flex-col bg-white rounded-lg shadow-lg p-4 text-center cursor-pointer hover:shadow-xl transition-all border"
    @click="thermostat_type = '{{ item.title }}', thermostat_price = '{{ item.price| number_format(2, ',', '.') }}', thermostat_cleanprice = '{{ emptyOption ? 0 : item.price > 0 ? item.price : 0 }}'"
    data-price="{{ emptyOption ? 0 : item.price > 0 ? item.price : 0 }}"
>
    <div class="text-center">
        {% if emptyOption %}
            <div class="text-center"><input type="radio" id="thermostat-empty" name="thermostaat" checked="true" value="0"></div>
        {% else %}
            <div class="text-center"><input type="radio" id="thermostat-{{ item.id }}" name="thermostaat" value="{{ item.id }}"></div>
        {% endif %}

    </div>
    <div class="w-3/4 my-8 mx-auto">
        {% if emptyOption %}
            <div class="aspect-square flex items-center justify-center text-gray-300 text-5xl">
                <i class="fa-solid fa-ban"></i>
            </div>
        {% else %}
            <div class="aspect-square">
                <img src="{{ item.image | media | resize(200) }}" alt="" class="">
            </div>
        {% endif %}
    </div>

    <div class="font-semibold text-center mb-2">
        {% if emptyOption %}
            Geen thermostaat
        {% else %}
            {{ item.merken.title }} {{ item.title }}
        {% endif %}
    </div>
    <div class="font-medium text-sm text-center text-primary-500 mt-auto">
        {% if emptyOption %}
            {{ format.euro(0) }}
        {% else %}
            {{ format.euro(item.price) }}
        {% endif %}
    </div>

</label>
