<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInstalwebKetelsKT extends Migration
{
    public function up()
    {
        Schema::create('instalweb_ketels_k_t', function($table)
        {
            $table->engine = 'InnoDB';
            $table->integer('ketels_id')->unsigned();
            $table->integer('thermostats_id')->unsigned();
            $table->primary(['ketels_id','thermostats_id']);
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('instalweb_ketels_k_t');
    }
}
