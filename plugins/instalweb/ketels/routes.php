<?php

use \Instalweb\Ketels\Models\Boiler;
use \Instalweb\Ketels\Models\Thermostat;

Route::get('/cv-ketels/get_thermostats/{boiler}', function($boilerId) {
  $boiler = Boiler::find($boilerId);
  $thermostats = $boiler->thermostats;

  foreach ($thermostats as $key => $item) {
    $thermostats[$key]['merknaam'] = $item->merken->title;
  }

  return json_encode($thermostats);

  // $thermostats = Thermostat::whereHas('boilers', function ($query) use ($boilerId) {
  //   $query->where('ketels.id', $boilerId);
  // })->join('merken', 'ketel.merken_id', '=' , 'merken.id')
  // ->select([
  //     'thermostats.title',
  //     'thermostats.merkend_id',
  //     'thermostats.description',
  //     'merken.title',
  // ])
  // ->get();

  // return json_encode($thermostats);
});