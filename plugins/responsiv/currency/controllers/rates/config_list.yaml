# ===================================
#  List Behavior Config
# ===================================

# Model List Column configuration
list: $/responsiv/currency/models/exchangerate/columns.yaml

# Model Class name
modelClass: Responsiv\Currency\Models\ExchangeRate

# List Title
title: Manage Rates

# Link URL for each record
recordUrl: responsiv/currency/rates/update/:id

# Message to display if the list is empty
noRecordsMessage: backend::lang.list.no_records

# Records to display per page
recordsPerPage: 20

# Display page numbers with pagination, disable to improve performance
showPageNumbers: true

# Displays the list column set up button
showSetup: true

# Displays the sorting link on each column
showSorting: true

# Default sorting column
defaultSort:
    column: id
    direction: asc

# Display checkboxes next to each record
showCheckboxes: true

# Toolbar widget configuration
toolbar:
    # Partial for toolbar buttons
    buttons: list_toolbar

    # Search widget configuration
    search:
        prompt: backend::lang.list.search_prompt
