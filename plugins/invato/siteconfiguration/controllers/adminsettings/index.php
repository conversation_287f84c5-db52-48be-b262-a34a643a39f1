<?php Block::put('breadcrumb') ?>
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= Backend::url('system/settings') ?>">System</a></li>
        <li class="breadcrumb-item active" aria-current="page">Admin Settings</li>
    </ol>
<?php Block::endPut() ?>

<?php if (!$this->fatalError): ?>

    <?= Form::open(['class' => 'layout']) ?>

        <div class="layout-row">
            <?= $this->formRender() ?>
        </div>

        <div class="form-buttons">
            <div class="loading-indicator-container">
                <button
                    type="submit"
                    data-request="onSave"
                    data-request-data="redirect:0"
                    data-hotkey="ctrl+s, cmd+s"
                    data-load-indicator="Saving Admin Settings..."
                    class="btn btn-primary">
                    Save settings
                </button>
                <button
                    type="button"
                    data-request="onSave"
                    data-request-data="redirect:1"
                    class="btn btn-default">
                    Save and Close
                </button>
                <span class="btn-text">
                    or <a href="<?= Backend::url('system/settings') ?>">Cancel</a>
                </span>
            </div>
        </div>

    <?= Form::close() ?>

<?php else: ?>

    <p class="flash-message static error"><?= e($this->fatalError) ?></p>
    <p><a href="<?= Backend::url('system/settings') ?>" class="btn btn-default">Return to settings</a></p>

<?php endif ?>
