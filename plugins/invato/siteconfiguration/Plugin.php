<?php

namespace Invato\SiteConfiguration;

use Backend;
use Event;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Invato\SiteConfiguration\Models\SiteAdminSettings;
use Invato\SiteConfiguration\Models\SiteConfigSettings;
use Invato\SiteConfiguration\Models\SiteThemeSettings;
use OFFLINE\Boxes\Classes\Events;
use System\Classes\PluginBase;
use View;

/**
 * Plugin Information File
 *
 * @link https://docs.octobercms.com/3.x/extend/system/plugins.html
 */
class Plugin extends PluginBase
{
    public function boot()
    {
        Event::listen(
            Events::FILTER_PARTIALS,

            function (Collection $partials) {
                $configuredPartials = SiteAdminSettings::get('settings.boxes_list');

                $partialsLoop = $partials;

                if (! empty($configuredPartials)) {
                    foreach ($partialsLoop as $partial) {
                        if (! in_array($partial->config->handle, $configuredPartials)) {
                            $partials->forget($partial->config->handle);
                        }
                    }

                    return $partials;
                }
            }
        );

        Event::listen(
            Events::FILTER_LAYOUTS,
            static function (Collection $layouts) {
                $selectedLayouts = SiteAdminSettings::get('settings.layouts_list');

                if (empty($selectedLayouts)) {
                    return $layouts;
                }

                foreach ($layouts->keys() as $key) {
                    if (! in_array($key, $selectedLayouts, true)) {
                        $layouts->forget($key);
                    }
                }

                return $layouts;
            }
        );

        if (! App::runningInConsole()) {
            $siteConfigSettings = SiteConfigSettings::instance();
            View::share('appUrl', secure_url(config('app.url')));
            View::share('companyName', $siteConfigSettings['company']['name'] ?? config('app.name'));
            View::share('companyData', $siteConfigSettings['company'] ?? null);

            // first set main to use as fallback
            if (isset($siteConfigSettings['logo']['primary'])) {
                $companyLogoUrl = secure_url(config('app.url')).'/storage/app/media/'.$siteConfigSettings['logo']['primary'];
            }

            // check if special email logo exists and set the url
            if (isset($siteConfigSettings['logo']['email'])) {
                $companyLogoUrl = secure_url(config('app.url')).'/storage/app/media/'.$siteConfigSettings['logo']['email'];
            }

            View::share('companyLogo', $companyLogoUrl ?? null);
        }

        Event::listen('backend.brand.getPalettePresets', function (&$presets) {
            $colorPalette = [
                'name' => 'OCMS',
                'light' => [
                    'primary' => '#238dcb',
                    'secondary' => '#72809d',
                    'selection' => '#6bc48d',
                    'link_color' => '#3498db',
                    'mainnav_color' => '#ffffff',
                    'mainnav_bg' => '#2d3134',
                    'sidebar_color' => '#536061',
                    'sidebar_bg' => '#e9edf3',
                    'sidebar_active_color' => '#333333',
                    'sidebar_active_bg' => '#ffffff',
                    'sidebar_hover_bg' => '#ffffff',
                    'settings_color' => '#536061',
                    'settings_bg' => '#f0f4f8',
                    'settings_item' => '#ffffff',
                    'settings_active_color' => '#ffffff',
                    'settings_active_bg' => '#6bc48d',
                    'settings_hover_bg' => '#dfe7ee',
                ],
                'dark' => [
                    'link_color' => '#a6a7fa',
                    'sidebar_color' => '#d7e1eA',
                    'sidebar_bg' => '#292a2d',
                    'sidebar_active_color' => '#ffffff',
                    'sidebar_active_bg' => '#424242',
                    'sidebar_hover_bg' => '#424242',
                    'settings_color' => '#adb5bd',
                    'settings_bg' => '#1b1f22',
                    'settings_item' => '#212529',
                    'settings_active_color' => '#ffffff',
                    'settings_active_bg' => '#2b3442',
                    'settings_hover_bg' => '#2b3442',
                ],
            ];
            $presets['ocms'] = $colorPalette;
        });
    }

    /**
     * pluginDetails about this plugin.
     */
    public function pluginDetails()
    {
        return [
            'name' => 'SiteConfiguration',
            'description' => 'No description provided yet...',
            'author' => 'Invato',
            'icon' => 'icon-leaf',
        ];
    }

    public function registerComponents()
    {
        return [
            'Invato\SiteConfiguration\Components\SiteConfig' => 'SiteConfig',
        ];
    }

    /**
     * registerPermissions used by the backend.
     */
    public function registerPermissions()
    {

        return [
            'invato.siteconfiguration.manage_admin' => [
                'tab' => 'Site Configuration',
                'label' => 'Manage admin settings',
            ],
        ];
    }

    /**
     * registerNavigation used by the backend.
     */
    public function registerNavigation()
    {
        return [
            'siteconfiguration' => [
                'label' => 'invato.siteconfiguration::lang.plugin.name',
                'url' => Backend::url('system/settings/update/invato/siteconfiguration/settings'),
                'icon' => 'icon-id-card-1',
                'iconSvg' => 'plugins/invato/siteconfiguration/assets/images/invato-siteconfig.svg',
                'order' => 800,
                'sideMenu' => [
                    'companysettings' => [
                        'label' => 'invato.siteconfiguration::lang.navigation.companysettings',
                        'icon' => 'icon-id-card-1',
                        'url' => Backend::url('system/settings/update/invato/siteconfiguration/settings'),
                    ],
                    'themesettings' => [
                        'label' => 'invato.siteconfiguration::lang.navigation.themesettings',
                        'icon' => 'icon-text-format-ul',
                        'url' => Backend::url('system/settings/update/invato/siteconfiguration/themesettings'),
                    ],
                    'adminsettings' => [
                        'label' => 'invato.siteconfiguration::lang.navigation.adminsettings',
                        'icon' => 'icon-cog',
                        'url' => Backend::url('invato/siteconfiguration/adminsettings'),
                        'permissions' => ['invato.siteconfiguration.manage_admin'],
                    ],
                ],
            ],
        ];
    }

    public function registerSettings()
    {
        return [
            'settings' => [
                'label' => 'invato.siteconfiguration::lang.settings.label',
                'description' => 'invato.siteconfiguration::lang.settings.description',
                'category' => 'Site Configuratie',
                'icon' => 'icon-cog',
                'size' => 'adaptive',
                'class' => SiteConfigSettings::class,
            ],
            'themesettings' => [
                'label' => 'invato.siteconfiguration::lang.themesettings.label',
                'description' => 'invato.siteconfiguration::lang.themesettings.description',
                'category' => 'Site Configuratie',
                'icon' => 'icon-cog',
                'size' => 'adaptive',
                'class' => SiteThemeSettings::class,
            ],
            'adminsettings' => [
                'label' => 'invato.siteconfiguration::lang.admin.label',
                'description' => 'invato.siteconfiguration::lang.admin.description',
                'category' => 'Site Configuratie',
                'permissions' => ['invato.siteconfiguration.manage_admin'],
                'icon' => 'icon-cog',
                'size' => 'adaptive',
                'class' => SiteAdminSettings::class,
            ],
        ];
    }
}
