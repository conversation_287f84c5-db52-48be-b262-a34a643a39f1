<?php
    use Illuminate\Support\Facades\File;
    use October\Rain\Support\Collection;

    $listFiles = File::allFiles( './plugins/invato/siteconfiguration/assets/img/navbars' );

    $items = [];
    foreach ($listFiles as $item) {
        $filename = $item->getFilename();
        $items[] = str_replace('.png', '', $filename);
    }
    sort($items, SORT_NATURAL | SORT_FLAG_CASE);

    $navbarList = new Collection($items);
    $screenshotDir = '/plugins/invato/siteconfiguration/assets/img/navbars/';
?>

<?php $navbarList->each( function($item, $key) use ($field, $value, $screenshotDir) { ?>
    <?php $num = str_replace('navbar-', "", $item); ?>
    <div class="config-option card position-relative">
        <label for="<?= $item ?>" class="">
            <input type="radio" name="<?= $field->getName() ?>" id="<?= $item; ?>" value="<?= $item ?>" <?php if ( $value === $item ) { ?>checked  <?php } ?> >

            <div class="config-option-screenshot">
                <img src="<?= $screenshotDir ?><?= $item ?>.png" alt="<?= $item ?>" style="max-width: 100%;" class="">
            </div>
        </label>
        <span class="position-absolute top-50 start-50 start-100 translate-middle badge rounded-pill bg-danger">
            <?= $num ?>
        </span>

    </div>

<?php });?>

<style>
    .config-option { margin-bottom: 16px; }
    .config-option input { display: none; }
    .config-option-screenshot { display: inline-block; border-radius: 6px; opacity: .6; transform: scale(80%); transition: all .1s ease-in-out; cursor: pointer; }
    .config-option-screenshot:hover { opacity: 1; transform: scale(90%); }
    input:checked ~ .config-option-screenshot { opacity: 1; outline: 3px solid var(--bs-primary); transform: scale(100%); }
</style>
