<?php namespace Invato\SiteConfiguration\Models;

use Invato\SiteConfiguration\Classes\BoxesPartialReaderExtender;
use Invato\SiteConfiguration\Classes\BoxesFilteredLayouts;
use System\Models\SettingModel;

class SiteAdminSettings extends SettingModel
{
    public $settingsCode = 'invato_siteadmin_settings';

    public $settingsFields = 'fields.yaml';

    public $rules = [];

    public function initSettingsData()
    {
        $colorList = ["transparent","#ffffff","#f3f4f6","#d1d5db","#6b7280","#374151","#18181b","#000000"];
        $this->color_list = $colorList;
        $this->footer_dev_link = 1;
    }

    public function getBoxesPartials()
    {
        return (new BoxesPartialReaderExtender())->getAllPartialHandlesArray();
    }

    public function getCMSLayouts()
    {
        return (new BoxesFilteredLayouts())->getAllLayoutsArray();
    }
}
