<?php namespace Invato\Landingpages\Components;

use Cms\Classes\ComponentBase;
use Invato\Landingpages\Models\LandingPage;

/**
 * RenderLandingPage Component
 *
 * @link https://docs.octobercms.com/3.x/extend/cms-components.html
 */
class RenderLandingPage extends ComponentBase
{
    public $landingpage;

    public function componentDetails()
    {
        return [
            'name' => 'Render Landing Page',
            'description' => 'Render a single landing page'
        ];
    }

    /**
     * @link https://docs.octobercms.com/3.x/element/inspector-types.html
     */
    public function defineProperties()
    {
        return [
            'slug' => [
                'title'       => 'Landingpagina slug',
                'description' => 'Voer landingpagina slug in',
                'default'     => '{{ :slug }}',
                'type'        => 'string',
            ]
        ];
    }

    public function onRun()
    {
        $this->landingpage = $this->page['landingpage'] = $this->getLandingPage();
    }

    protected function getLandingPage()
    {
        $slug = $this->property('slug');
        $landingpage = new LandingPage();
        $query = $landingpage->query();
        $query->where('slug', $slug);
        $landingpage = $query->first();

        return $landingpage;
    }


}
