<?php namespace Invato\Notifications\Updates;

use October\Rain\Database\Updates\Migration;
use Schema;

class BuilderTableCreateInvatoNotificationsPopups extends Migration
{
    public function up()
    {
        Schema::create('invato_notifications_popups', function($table)
        {
            $table->increments('id')->unsigned();
            $table->string('title', 255);
            $table->text('popup');
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->string('show_always', 255)->default('default');
            $table->boolean('is_active')->default(0);
            $table->smallInteger('sort_order')->default(0);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
        });
    }

    public function down()
    {
        Schema::dropIfExists('invato_notifications_popups');
    }
}
