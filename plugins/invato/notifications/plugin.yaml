plugin:
    name: 'invato.notifications::lang.plugin.name'
    description: 'invato.notifications::lang.plugin.description'
    author: Invato
    icon: oc-icon-exclamation-circle
    homepage: ''
navigation:
    main-menu-item:
        label: 'invato.notifications::lang.plugin.name'
        url: /
        icon: icon-exclamation-circle
        iconSvg: plugins/invato/notifications/assets/images/invato-notifications.svg
        permissions:
            - 'invato.notifications.manage_notifications'
            - 'invato.notifications.manage_popups'
        sideMenu:
            side-menu-item:
                label: 'invato.notifications::lang.plugin.name'
                url: invato/notifications/notifications
                icon: icon-exclamation-triangle
                permissions:
                    - 'invato.notifications.manage_notifications'
            side-menu-item2:
                label: 'invato.notifications::lang.plugin.popup'
                url: invato/notifications/popups
                icon: icon-sitemap
                permissions:
                    - 'invato.notifications.manage_popups'
permissions:
    'invato.notifications.manage_notifications':
        tab: 'invato.notifications::lang.plugin.name'
        label: 'invato.notifications::lang.manage.notifications'
    'invato.notifications.manage_popups':
        tab: 'invato.notifications::lang.plugin.name'
        label: 'invato.notifications::lang.manage.popups'
