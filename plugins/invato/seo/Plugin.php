<?php

namespace Invato\Seo;

use Event;
use Invato\Seo\traits\HasSeoableTrait;
use System\Classes\PluginBase;

class Plugin extends PluginBase
{
    public function pluginDetails()
    {
        return [
            'name' => 'SEO',
            'description' => 'Add SEO fields to pages',
            'author' => 'Invato',
            'icon' => 'icon-search-plus',
        ];
    }

    public function registerSettings()
    {
        return [
            'options' => [
                'label' => 'Robots.txt',
                'description' => 'zen.robots::lang.plugin.description',
                'icon' => 'icon-search',
                'permissions' => ['zen.robots'],
                'class' => 'Zen\Robots\Models\Settings',
                'order' => 600,
                'category' => 'system::lang.system.categories.cms',
            ],
        ];
    }

    public function boot()
    {
        Event::listen('backend.form.extendFields', function ($widget) {
            if ($widget->model && in_array(HasSeoableTrait::class, class_uses_recursive($widget->model))) {
                $widget->addTabFields($widget->model->seoFields());

                $widget->model->bindEvent('model.beforeSave', function () use ($widget) {
                    unset($widget->model->seo);
                });

                $widget->model->bindEvent('model.afterSave', function () use ($widget) {
                    $data = request()?->get(class_basename($widget->model))['seo'] ?? null;
                    if (isset($data)) {
                        $seoData = $widget->model->seoData;
                        $seoData->data = $data;
                        $seoData->save();
                    }
                });
            }
        });
    }
}
