<?php namespace Invato\Seo\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class AddCareersSeoField extends Migration
{
    public function up()
    {
        if ( Schema::hasTable('invato_careers_vacancies') ) {
            if ( !Schema::hasColumn('invato_careers_vacancies', 'seo') ) {
                Schema::table('invato_careers_vacancies', function($table)
                {
                    $table->text('seo')->nullable();
                });
            }
        }
    }

    public function down()
    {
        if ( Schema::hasTable('invato_careers_vacancies') ) {
            if ( Schema::hasColumn('invato_careers_vacancies', 'seo') ) {
                Schema::table('invato_careers_vacancies', function($table)
                {
                    $table->dropColumn('seo');
                });
            }
        }
    }
}
