<?php

namespace Invato\Seo\updates;

use Invato\Careers\Models\Vacancy;
use October\Rain\Database\Updates\Migration;
use System\Classes\PluginManager;

return new class extends Migration
{
    public function up()
    {
        if (PluginManager::instance()->hasPlugin('Invato.Careers')) {
            // Careers vacancies
            Vacancy::withTrashed()
                ->get()
                ->each(function ($record) {
                    $record->seo;
                });
        }
    }
};
