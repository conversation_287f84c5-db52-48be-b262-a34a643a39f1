<?php

namespace Invato\SocialFeed\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Model;
use October\Rain\Database\Traits\SoftDelete;

/**
 * FeedItem Model
 *
 * @link https://docs.octobercms.com/3.x/extend/system/models.html
 */
class FeedItem extends Model
{
    use SoftDelete;
    /**
     * @var string table name
     */
    public $table = 'invato_socialfeed_feed_items';

    protected $fillable = [
        'external_id',
        'type',
        'message',
        'data',
        'posted_at',
        'synced_at',
    ];

    protected $casts = [
        'external_id' => 'string',
        'type' => 'string',
        'message' => 'string',
        'data' => 'array',
        'posted_at' => 'datetime',
        'synced_at' => 'datetime',
    ];

    /**
     * How-to use example
     * - $feedItems = FeedItem::getInstagramItems(
     * -     partial: 'leerlingPaginaWidget', -> default = 'default'
     * -     hashtags: ['trotsoponzeleerlingen'], -> nullable
     * -     limit: 5 -> nullable (will get whole table);
     *
     * @return mixed
     */
    public static function getInstagramItems(string $partial = 'default', array $hashtags = [], int $limit = null)
    {
        $key = 'invato_socialfeed_feed_items_instagram_items';
        $key .= 'ht'.implode('', $hashtags);
        $key .= 'p'.$partial;
        $key .= 'l'.$limit;

        $ttl = App::environment('local') ? 0 : 60 * 60;

        return Cache::remember(md5($key), $ttl, static function () use ($limit, $hashtags) {
            $query = self::query()
                ->where('type', '=', 'instagram');

            if (count($hashtags) > 0) {
                $query->where(function (Builder $builder) use ($hashtags) {
                    foreach ($hashtags as $hashtag) {
                        $builder->orWhere('message', 'LIKE', '%'.'#'.$hashtag.'%');
                    }
                });
            }

            if ($limit) {
                $query->limit($limit);
            }

            $query->orderBy('posted_at', 'DESC');

            return $query->get();
        });
    }
}
