<?php

namespace Invato\SocialFeed;

use Invato\SocialFeed\Console\GetInstagramFeedCommand;
use Invato\SocialFeed\Models\InstagramSettings;
use System\Classes\PluginBase;

/**
 * Plugin class
 */
class Plugin extends PluginBase
{
    /**
     * register method, called when the plugin is first registered.
     */
    public function register()
    {
        $this->registerConsoleCommand('socialfeed.instagram', GetInstagramFeedCommand::class);
    }

    /**
     * boot method, called right before the request route.
     */
    public function boot()
    {
    }

    /**
     * registerComponents used by the frontend.
     */
    public function registerComponents()
    {
        return [
            'Invato\SocialFeed\Components\InstagramFeed' => 'InstagramFeed',
        ];
    }

    /**
     * registerSettings used by the backend.
     */
    public function registerSettings()
    {
        return [
            'settings' => [
                'label' => 'Instagram Settings',
                'description' => 'Beheer de Instagram instellingen',
                'category' => 'Plugins',
                'icon' => 'icon-cog',
                'class' => InstagramSettings::class,
                'permissions' => ['invato.socialfeed.manage_settings'],
            ],
        ];
    }

    public function registerSchedule($schedule)
    {
        if (! empty(InstagramSettings::get('long_lived_access_token'))) {
            $schedule->command('socialfeed:instagram-feed-items')
                ->timezone('Europe/Amsterdam')
                ->hourly();
        }
    }
}
