plugin:
    name: 'invato.redirects::lang.plugin.name'
    description: 'invato.redirects::lang.plugin.description'
    author: Invato
    icon: oc-icon-link
    homepage: ''
permissions:
    invato.redirects.manage_redirects:
        tab: 'invato.redirects::lang.plugin.name'
        label: 'invato.redirects::lang.permissions.manage_redirects'
navigation:
    main-menu-item:
        label: 'invato.redirects::lang.plugin.name'
        url: /
        icon: icon-link
        iconSvg: plugins/invato/redirects/assets/images/invato-redirects.svg
        sideMenu:
            side-menu-item:
                label: 'invato.redirects::lang.permissions.manage_redirects'
                url: invato/redirects/redirects
                icon: icon-random
                permissions:
                    - invato.redirects.manage_redirects
