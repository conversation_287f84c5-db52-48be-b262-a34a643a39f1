<?php

namespace Invato\Redirects\Models;

use Backend\Models\ExportModel;

class RedirectExport extends ExportModel
{
    /**
     * @var array rules to be applied to the data.
     */
    public $rules = [];

    public function exportData($columns, $sessionKey = null)
    {
        $redirects = Redirect::all();

        $redirects->each(function ($redirect) use ($columns) {
            $redirect->addVisible($columns);
        });

        return $redirects->toArray();
    }
}
