plugin:
    name: 'invato.catalog::lang.plugin.name'
    description: 'invato.catalog::lang.plugin.description'
    author: Invato
    icon: oc-icon-shopping-cart
    homepage: ''
permissions:
    'invato.catalog.manage_plugin':
        tab: 'invato.catalog::lang.plugin.name'
        label: 'invato.catalog::lang.permissions.manage_plugin'
    'invato.catalog.import_products':
        tab: 'invato.catalog::lang.plugin.name'
        label: 'invato.catalog::lang.permissions.import_products'
    'invato.catalog.export_products':
        tab: 'invato.catalog::lang.plugin.name'
        label: 'invato.catalog::lang.permissions.export_products'
    'invato.catalog.import_categories':
        tab: 'invato.catalog::lang.plugin.name'
        label: 'invato.catalog::lang.permissions.import_categories'
    'invato.catalog.export_categories':
        tab: 'invato.catalog::lang.plugin.name'
        label: 'invato.catalog::lang.permissions.export_categories'
navigation:
    catalog:
        label: 'invato.catalog::lang.plugin.name'
        url: /
        icon: icon-shopping-cart
        iconSvg: plugins/invato/catalog/assets/images/invato-catalog.svg
        permissions:
            - 'invato.catalog.manage_plugin'
        sideMenu:
            content-section:
                label: 'Content'
                itemType: section

            categories:
                label: 'invato.catalog::lang.menu.categories'
                url: invato/catalog/categories
                icon: icon-sitemap
                permissions:
                    - 'invato.catalog.manage_plugin'
            products:
                label: 'invato.catalog::lang.menu.products'
                url: invato/catalog/products
                icon: icon-shopping-cart
                permissions:
                    - 'invato.catalog.manage_plugin'

            settings-section:
                label: 'Settings'
                itemType: section

            settings:
                label: 'General'
                url: system/settings/update/invato/catalog/settings
                icon: icon-cogs
                permissions:
                    - 'invato.catalog.manage_plugin'

            documentation-section:
                label: 'Documentation'
                itemType: section

            readme:
                label: 'Readme'
                url: invato/catalog/readme
                icon: icon-book
                permissions:
                    - 'superusers.view_readme'
            manual:
                label: 'Manual'
                url: invato/catalog/manual
                icon: icon-book
                permissions:
                    - 'invato.catalog.manage_plugin'




