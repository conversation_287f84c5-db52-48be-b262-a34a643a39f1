<?php namespace Invato\Catalog\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoCatalogProducts extends Migration
{
    public function up()
    {
        Schema::table('invato_catalog_products', function($table)
        {
            $table->integer('sort_order')->default(0);
        });
    }

    public function down()
    {
        Schema::table('invato_catalog_products', function($table)
        {
            $table->dropColumn('sort_order');
        });
    }
}
