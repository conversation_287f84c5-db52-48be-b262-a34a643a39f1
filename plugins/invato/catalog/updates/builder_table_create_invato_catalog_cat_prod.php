<?php namespace Invato\Catalog\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInvatoCatalogCatProd extends Migration
{
    public function up()
    {
        Schema::create('invato_catalog_cat_prod', function($table)
        {
            $table->integer('category_id')->unsigned();
            $table->integer('product_id')->unsigned();
            $table->primary(['category_id', 'product_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('invato_catalog_cat_prod');
    }
}
