<fieldset class="form-fs" x-data="{ fieldset: 1, valid: false }" x-show="fieldset == current" x-cloak>
  <legend class="sr-only">{{ title }}</legend>

  <div
    x-data="radioButtonGroup('{{ selected }}')"
    @keydown.down.stop.prevent="selectNext"
    @keydown.right.stop.prevent="selectNext"
    @keydown.up.stop.prevent="selectPrevious"
    @keydown.left.stop.prevent="selectPrevious"
    role="radiogroup"
    :aria-labelledby="$id('radio-group-label')"
    :id="$id('radio-group-label')"
    x-id="['radio-group-label']"
    x-effect="value ? valid = true : valid = false"
  >
    {% partial '@e-title' num=num title=title %}

    <div class="fs-body">

      <div class="fs-field-grid">

        {% set fields = [
          {
            'value': 'Voor 1985',
            'label': 'Voor 1985'
          },
          {
            'value': 'Na 1985',
            'label': 'Na 1985'
          }
        ] %}

        {% for item in fields %}
          {% partial '@fields/radio' name=name value=item.value label=item.label %}
        {% endfor %}

      </div>

      {% partial '@e-buttons' name=name %}

    </div>

  </div>

</fieldset>
