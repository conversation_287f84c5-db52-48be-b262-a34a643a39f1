<?php

namespace Invato\Woningscan;

use Invato\Woningscan\Models\WoningscanSettings;
use System\Classes\PluginBase;
use Invato\Traits\SettingsMenuContextTrait;

class Plugin extends PluginBase
{
    use SettingsMenuContextTrait;

    public function pluginDetails()
    {
        return [
            'name' => 'Woningscan',
            'description' => 'No description provided yet...',
            'author' => 'Invato',
            'icon' => 'icon-leaf',
        ];
    }

    public function boot()
    {
        $this->setupSettingsMenuContext('settings');
    }

    public function registerComponents()
    {
        return [
            'Invato\Woningscan\Components\Woningscan' => 'Woningscan',
        ];
    }

    public function registerPageSnippets()
    {
        return [
            'Invato\Woningscan\Components\Woningscan' => 'Woningscan',
        ];
    }

    public function registerPermissions()
    {
        return [
            'invato.woningscan.manage_plugin' => [
                'tab' => 'Invato',
                'label' => 'Beheer Woningscan',
            ],
        ];
    }

    public function registerSettings()
    {
        return [
            'settings' => [
                'label' => 'Woningscan instellingen',
                'description' => 'Beheer Woningscan instellingen',
                'category' => 'Plugins',
                'icon' => 'ph ph-house-line',
                'size' => 'adaptive',
                'class' => WoningscanSettings::class,
            ],
        ];
    }
}
