<?php

namespace Invato\Property\Updates;

use Illuminate\Database\Schema\Blueprint;
use October\Rain\Database\Updates\Migration;
use Schema;

class CreatePropertyItemsTable extends Migration
{
    protected string $table = 'invato_property_items';

    public function up()
    {
        Schema::create($this->table, static function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->increments('id')->unsigned();
            $table->string('external_id');
            $table->string('type');
            $table->json('data');
            $table->timestamp('online_at');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists($this->table);
    }
}
