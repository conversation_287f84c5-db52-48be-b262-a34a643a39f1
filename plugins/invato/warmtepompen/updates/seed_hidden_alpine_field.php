<?php

namespace Invato\Warmtepompen\updates;

use Illuminate\Support\Facades\File;
use October\Rain\Database\Updates\Seeder;
use Renatio\FormBuilder\Models\FieldType;

class SeedAlpineHiddenField extends Seeder
{
    public function run()
    {
        $path = __DIR__.'/fields/';
        $fieldtypes = FieldType::all();
        $alpineHidden = $fieldtypes->where('code', 'hidden_alpine')->first();

        if (! $alpineHidden) {
            FieldType::create([
                'name' => 'Hidden (Alpine.js)',
                'code' => 'hidden_alpine',
                'markup' => File::get($path.'_hidden_alpine.htm'),
                'description' => 'Verborgen veld tbv Alpine.js data',
            ]);
        }

    }
}
