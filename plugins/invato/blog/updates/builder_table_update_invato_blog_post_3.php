<?php namespace Invato\Blog\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoBlogPost3 extends Migration
{
    public function up()
    {
        Schema::table('invato_blog_post', function($table)
        {
            $table->string('thumbnail_image')->nullable()->after('image');
        });
    }
    
    public function down()
    {
        Schema::table('invato_blog_post', function($table)
        {
            $table->dropColumn('thumbnail_image');
        });
    }
}
