<?php namespace Invato\Blog\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInvatoBlogPost extends Migration
{
    public function up()
    {
        Schema::create('invato_blog_post', function($table)
        {
            $table->increments('id')->unsigned();
            $table->integer('author_id')->nullable();
            $table->string('title');
            $table->string('slug');
            $table->string('status');
            $table->dateTime('publication_date')->nullable();
            $table->string('title_short')->nullable();
            $table->text('excerpt')->nullable();
            $table->text('content')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('invato_blog_post');
    }
}
