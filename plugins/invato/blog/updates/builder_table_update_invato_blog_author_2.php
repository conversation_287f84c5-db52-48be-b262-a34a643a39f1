<?php namespace Invato\Blog\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoBlogAuthor2 extends Migration
{
    public function up()
    {
        Schema::table('invato_blog_author', function($table)
        {
            $table->string('last_name', 255)->nullable()->change();
        });
    }
    
    public function down()
    {
        Schema::table('invato_blog_author', function($table)
        {
            $table->string('last_name', 255)->nullable(false)->change();
        });
    }
}
