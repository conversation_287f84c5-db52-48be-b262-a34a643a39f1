<?php namespace Invato\Blog\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoBlogPost extends Migration
{
    public function up()
    {
        Schema::table('invato_blog_post', function($table)
        {
            $table->dropColumn('content');
        });
    }
    
    public function down()
    {
        Schema::table('invato_blog_post', function($table)
        {
            $table->text('content')->nullable();
        });
    }
}
