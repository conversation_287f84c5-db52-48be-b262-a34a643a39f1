<?php

namespace App\Console\Commands;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Invato\Blog\Models\Author;
use Invato\Blog\Models\BlogSetting;
use Invato\Blog\Models\Category;
use Invato\Blog\Models\Post;
use JsonException;

class BlogSyncFromApiEndpointCommand extends Command
{
    protected $signature = 'invato:blog:sync-from-api-endpoint';

    protected $description = 'Sync all from endpoint';

    protected $syncWithDomain;

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function handle(): void
    {
        $blogSettings = BlogSetting::instance();

        if (! isset($blogSettings['syncWithDomain'], $blogSettings['authToken'])) {
            return;
        }

        $this->syncWithDomain = $blogSettings['syncWithDomain'];

        $client = new Client([
            'base_uri' => "https://{$blogSettings->syncWithDomain}/api/blog/sync/",
            'headers' => [
                'Authorization' => 'Bearer '.$blogSettings['authToken'],
            ],
        ]);

        $this->syncCategories($client);
        $this->syncAuthors($client);
        $this->syncPosts($client);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    private function get(Client $client, string $url)
    {
        $response = $client->request('GET', $url);

        $decodedResponse = json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);

        if ($decodedResponse['success']) {
            return $decodedResponse['resources'];
        }
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    private function syncPosts(Client $client): void
    {
        $resources = $this->get($client, 'posts');

        foreach ($resources as $resource) {
            if (isset($resource['author'])) {
                Author::withTrashed()
                    ->updateOrCreate([
                        'id' => $resource['author']['id'],
                    ], $resource['author']);
            }

            if (isset($resource['categories'])) {
                $categoriesIds = [];
                foreach ($resource['categories'] as $categoryData) {
                    unset($categoryData['pivot']);
                    $category = Category::withTrashed()
                        ->updateOrCreate([
                            'id' => $categoryData['id'],
                        ], $categoryData);

                    foreach ($category->mediaAttributes as $mediaAttribute) {
                        $this->getMedia($category->$mediaAttribute);
                    }
                }
            }

            unset(
                $resource['post']['categories'],
                $resource['post']['author']
            );

            $post = Post::withTrashed()
                ->updateOrCreate([
                    'id' => $resource['post']['id'],
                ], $resource['post']);

            $post->categories()->sync($categoriesIds);

            foreach ($post->mediaAttributes as $mediaAttribute) {
                $this->getMedia($post->$mediaAttribute);
            }
        }
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    private function syncAuthors(Client $client): void
    {
        $resources = $this->get($client, 'authors');

        foreach ($resources as $resource) {
            $author = Author::withTrashed()
                ->updateOrCreate([
                    'id' => $resource['id'],
                ], $resource);

            foreach ($author->mediaAttributes as $mediaAttribute) {
                $this->getMedia($author->$mediaAttribute);
            }
        }
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    private function syncCategories(Client $client): void
    {
        $resources = $this->get($client, 'categories');

        foreach ($resources as $resource) {
            unset($resource['pivot']);
            $category = Category::withTrashed()
                ->updateOrCreate([
                    'id' => $resource['id'],
                ], $resource);

            foreach ($category->mediaAttributes as $mediaAttribute) {
                $this->getMedia($category->$mediaAttribute);
            }
        }
    }

    private function getMedia(?string $path = null): void
    {
        if (! isset($path)) {
            return;
        }

        if (! Storage::disk('media')->exists($path)) {
            $url = "https://{$this->syncWithDomain}/storage/app/media{$path}";
            $file = Http::get($url);

            if ($file->failed()) {
                return;
            }

            Storage::disk('media')->put($path, $file->body());
        }
    }
}
