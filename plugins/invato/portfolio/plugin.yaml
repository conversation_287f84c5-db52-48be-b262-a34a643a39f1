plugin:
    name: 'invato.portfolio::lang.plugin.name'
    description: 'invato.portfolio::lang.plugin.description'
    author: Invato
    icon: oc-icon-image
    homepage: ''
permissions:
    invato.portfolio.manage_categories:
        tab: 'invato.portfolio::lang.plugin.portfolio'
        label: 'invato.portfolio::lang.permissions.manage_categories'
    invato.portfolio.manage_projects:
        tab: 'invato.portfolio::lang.plugin.portfolio'
        label: 'invato.portfolio::lang.permissions.manage_projects'
    invato.portfolio.manage_plugin:
        tab: 'invato.portfolio::lang.plugin.portfolio'
        label: 'invato.portfolio::lang.manage_plugin'
navigation:
    main-menu-item:
        label: 'invato.portfolio::lang.plugin.name'
        url: /
        icon: icon-image
        iconSvg: plugins/invato/portfolio/assets/images/invato-portfolio.svg
        permissions:
            - invato.portfolio.manage_plugin
        sideMenu:
            side-menu-item:
                label: 'invato.portfolio::lang.global.categories'
                url: invato/portfolio/categories
                icon: icon-sitemap
                permissions:
                    - invato.portfolio.manage_plugin
                    - invato.portfolio.manage_categories
            side-menu-item2:
                label: 'invato.portfolio::lang.global.projects'
                url: invato/portfolio/projects
                icon: icon-folder-open-o
                permissions:
                    - invato.portfolio.manage_plugin
                    - invato.portfolio.manage_projects
