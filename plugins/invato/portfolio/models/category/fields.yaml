fields:
    title:
        label: 'invato.portfolio::lang.global.title'
        span: auto
        required: 1
        type: text
    slug:
        label: 'invato.portfolio::lang.global.slug'
        span: auto
        required: 1
        defaultFrom: title
        preset:
            field: title
            type: slug
        type: text
    overview_image:
        label: 'invato.portfolio::lang.global.overview_image'
        mode: image
        thumbOptions:
            mode: crop
            extension: auto
        span: full
        type: mediafinder
    description:
        label: 'invato.portfolio::lang.global.description'
        size: huge
        span: full
        required: 1
        type: richeditor
