tabs:
    fields:
        gallery_type:
            label: 'invato.portfolio::lang.project.type'
            options:
                images: Images
                folder: Folder
            span: full
            default: images
            type: balloon-selector
            tab: 'invato.portfolio::lang.project.content'
        images:
            label: 'invato.portfolio::lang.project.images'
            mode: image
            maxItems: '32'
            thumbOptions:
                mode: crop
                extension: auto
            span: full
            trigger:
                action: show
                field: gallery_type
                condition: 'value[images]'
            type: mediafinder
            tab: 'invato.portfolio::lang.project.content'
        image_folder:
            label: 'invato.portfolio::lang.project.image_folder'
            mode: folder
            thumbOptions:
                mode: crop
                extension: auto
            span: full
            trigger:
                action: show
                field: gallery_type
                condition: 'value[folder]'
            type: mediafinder
            tab: 'invato.portfolio::lang.project.content'
fields:
    title:
        label: 'invato.portfolio::lang.global.title'
        span: auto
        required: 1
        type: text
    slug:
        label: 'invato.portfolio::lang.global.slug'
        span: auto
        required: 1
        defaultFrom: title
        preset:
            field: title
            type: slug
        type: text
    overview_image:
        label: 'invato.portfolio::lang.global.overview_image'
        mode: image
        thumbOptions:
            mode: crop
            extension: auto
        span: auto
        type: mediafinder
    excerpt:
        label: 'invato.portfolio::lang.project.excerpt'
        size: small
        span: full
        type: richeditor
    description:
        label: 'invato.portfolio::lang.global.description'
        size: huge
        span: full
        type: richeditor
    details:
        label: Details
        prompt: 'Nieuwe regel'
        displayMode: accordion
        showReorder: 1
        showDuplicate: 1
        span: full
        type: repeater
        form:
            fields:
                label:
                    label: Label
                    span: auto
                    type: text
                text:
                    label: Tekst
                    span: auto
                    type: text
    categories:
        label: 'invato.portfolio::lang.global.categories'
        nameFrom: title
        descriptionFrom: description
        span: full
        type: relation
