<?php namespace Invato\Portfolio\Models;

use Cms\Classes\Page;
use Model;

/**
 * PortfolioSettings Model
 *
 * @link https://docs.octobercms.com/3.x/extend/system/models.html
 */
class PortfolioSettings extends \System\Models\SettingModel
{
    public $settingsCode = 'invato_portfolio_settings';

    public $settingsFields = 'fields.yaml';

    public $rules = [];

    public function getPortfolioPageOptions()
    {
        return Page::sortBy('baseFileName')->lists('baseFileName', 'baseFileName');
    }

    public function getCategoryPageOptions()
    {
        return Page::sortBy('baseFileName')->lists('baseFileName', 'baseFileName');
    }

    public function getProjectPageOptions()
    {
        return Page::sortBy('baseFileName')->lists('baseFileName', 'baseFileName');
    }
}
