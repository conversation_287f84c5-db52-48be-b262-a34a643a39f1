<?php

namespace Invato\Portfolio\Models;

use Cms\Classes\Controller;
use Cms\Classes\Page as CmsPage;
use Cms\Classes\Theme;
use Invato\Seo\traits\HasSeoableTrait;
use Model;
use October\Rain\Database\Traits\SimpleTree;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;
use RainLab\Translate\Behaviors\TranslatableModel;

/**
 * Model
 */
class Category extends Model
{
    use HasSeoableTrait;
    use SimpleTree;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    public $implement = [
        TranslatableModel::class,
    ];

    public $translatable = ['title', 'description'];

    protected $dates = ['deleted_at'];

    protected $slugs = ['slug' => 'title'];

    public $table = 'invato_portfolio_categories';

    public $rules = [
        'title' => ['required'],
        'slug' => ['required'],
    ];

    public $belongsToMany = [
        'projects' => [Project::class, 'table' => 'invato_portfolio_cat_proj'],
    ];

    public static function getMenuTypeInfo($type)
    {
        $result = [];

        if ($type == 'portfolio-category') {
            $references = [];

            $posts = self::orderBy('title')->get();
            foreach ($posts as $post) {
                $references[$post->id] = $post->title;
            }

            $result = [
                'references' => $references,
                'nesting' => false,
                'dynamicItems' => false,
            ];
        }

        if ($type == 'all-portfolio-categories') {
            $result = [
                'nesting' => true,
                'dynamicItems' => true,
            ];
        }

        if ($result) {
            $theme = Theme::getActiveTheme();

            $pages = CmsPage::listInTheme($theme, true);
            $cmsPages = [];

            foreach ($pages as $page) {
                if (! $page->hasComponent('PortfolioCategoryDetail')) {
                    continue;
                }

                $properties = $page->getComponentProperties('PortfolioCategoryDetail');
                if (! preg_match('/{{\s*:/', $properties['slug'])) {
                    continue;
                }

                $cmsPages[] = $page;
            }

            $result['cmsPages'] = $cmsPages;
        }

        return $result;
    }

    public static function resolveMenuItem($item, $url, $theme)
    {
        $result = null;

        if ($item->type == 'portfolio-category') {
            $model = Category::find($item->reference);

            if (! $model) {
                return;
            }

            $controller = new Controller($theme);
            $pageUrl = $controller->pageUrl($item->cmsPage, [
                'id' => $model->id,
                'slug' => $model->slug,
            ]);

            $result = [
                'url' => $pageUrl,
                'isActive' => $pageUrl == $url,
                'title' => $model->title,
                'mtime' => $model->updated_at,
            ];

            return $result;
        } elseif ($item->type == 'all-portfolio-categories') {
            $result = [
                'items' => [],
            ];

            $posts = self::orderBy('title')->get();
            $controller = new Controller($theme);

            foreach ($posts as $post) {
                $pageUrl = $controller->pageUrl($item->cmsPage, [
                    'id' => $post->id,
                    'slug' => $post->slug,
                ]);

                $postItem = [
                    'title' => $post->title,
                    'url' => $pageUrl,
                    'mtime' => $post->updated_at,
                ];

                $postItem['isActive'] = $postItem['url'] == $url;

                $result['items'][] = $postItem;
            }
        }

        return $result;
    }
}
