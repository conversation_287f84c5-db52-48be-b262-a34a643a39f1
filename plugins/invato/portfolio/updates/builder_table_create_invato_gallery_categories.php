<?php namespace Invato\Portfolio\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class CreateCategoryTable extends Migration
{
    public function up()
    {
        Schema::create('invato_portfolio_categories', function($table)
        {
            $table->increments('id')->unsigned();
            $table->string('title');
            $table->string('slug');
            $table->text('description')->nullable();
            $table->string('overview_image')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
            $table->integer('sort_order')->default(0);
        });
    }

    public function down()
    {
        Schema::dropIfExists('invato_portfolio_categories');
    }
}
