<?php namespace Invato\Courses\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInvatoCoursesInstructorLocation extends Migration
{
    public function up()
    {
        Schema::create('invato_courses_instructor_location', function($table)
        {
            $table->integer('instructor_id')->unsigned();
            $table->integer('location_id')->unsigned();
            $table->primary(['instructor_id', 'location_id']);
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('invato_courses_instructor_location');
    }
}
