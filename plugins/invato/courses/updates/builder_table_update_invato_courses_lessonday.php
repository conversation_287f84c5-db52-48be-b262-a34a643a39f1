<?php namespace Invato\Courses\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoCoursesLessonday extends Migration
{
    public function up()
    {
        Schema::table('invato_courses_lessonday', function($table)
        {
            $table->integer('category_id')->nullable()->after('location_id');
        });
    }
    
    public function down()
    {
        Schema::table('invato_courses_lessonday', function($table)
        {
            $table->dropColumn('category_id');
        });
    }
}
