<?php namespace Invato\Courses\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoCoursesCategories2 extends Migration
{
    public function up()
    {
        Schema::table('invato_courses_categories', function($table)
        {
            $table->integer('parent_id')->nullable()->unsigned();
        });
    }
    
    public function down()
    {
        Schema::table('invato_courses_categories', function($table)
        {
            $table->dropColumn('parent_id');
        });
    }
}
