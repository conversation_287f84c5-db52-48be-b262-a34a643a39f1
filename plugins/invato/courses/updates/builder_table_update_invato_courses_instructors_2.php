<?php namespace Invato\Courses\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoCoursesInstructors2 extends Migration
{
    public function up()
    {
        Schema::table('invato_courses_instructors', function($table)
        {
            $table->string('landingpage', 255)->nullable()->after('bio');
        });
    }
    
    public function down()
    {
        Schema::table('invato_courses_instructors', function($table)
        {
            $table->dropColumn('landingpage');
        });
    }
}
