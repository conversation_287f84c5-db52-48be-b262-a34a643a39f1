<?php

namespace Invato\Courses\Controllers;

use Backend\Behaviors\FormController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;

class Instructors extends Controller
{
    public $implement = [
        FormController::class,
        ListController::class,
    ];

    public $formConfig = 'config_form.yaml';

    public $listConfig = 'config_list.yaml';

    public $requiredPermissions = [
        'invato.courses.manage_plugin',
        'invato.courses.manage_instructors',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Courses', 'main-menu-item', 'side-menu-item2');
    }
}
