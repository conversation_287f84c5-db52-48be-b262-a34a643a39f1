<?php namespace Invato\Courses\Models;

use Model;

/**
 * Model
 */
class Course extends Model
{
    use \October\Rain\Database\Traits\Validation;
    use \October\Rain\Database\Traits\SoftDelete;
    use \October\Rain\Database\Traits\Sortable;

    /**
     * @var array dates to cast from the database.
     */
    protected $dates = ['deleted_at'];

    /**
     * @var string table in the database used by the model.
     */
    public $table = 'invato_courses_courses';

    /**
     * @var array rules for validation.
     */
    public $rules = [
    ];

    public $belongsToMany = [
        'instructors' => [
            \Invato\Courses\Models\Instructor::class,
            'table' => 'invato_courses_course_instructor',
        ],
        'locations' => [
            \Invato\Courses\Models\Location::class,
            'table' => 'invato_courses_course_location',
        ]
    ];

    public $hasMany = [
        'lessons' => \Invato\Courses\Models\Lesson::class
    ];

}
