plugin:
    name: 'invato.teams::lang.plugin.name'
    description: 'invato.teams::lang.plugin.description'
    author: Invato
    icon: oc-icon-users
    homepage: ''
permissions:
    'invato.teams.manage_teams':
        tab: 'invato.teams::lang.plugin.name'
        label: 'invato.teams::lang.permissions.teams'
    'invato.teams.manage_departments':
        tab: 'invato.teams::lang.plugin.name'
        label: 'invato.teams::lang.permissions.departments'
    'invato.teams.manage_members':
        tab: 'invato.teams::lang.plugin.name'
        label: 'invato.teams::lang.permissions.members'
navigation:
    main-menu-item:
        label: 'invato.teams::lang.plugin.name'
        url: /
        icon: icon-users
        iconSvg: plugins/invato/teams/assets/images/invato-teams.svg
        permissions:
            - 'invato.teams.manage_teams'
        sideMenu:
            side-menu-item:
                label: 'invato.teams::lang.plugin.departments'
                url: invato/teams/departments
                icon: icon-sitemap
                permissions:
                    - 'invato.teams.manage_teams'
                    - 'invato.teams.manage_departments'
            side-menu-item2:
                label: 'invato.teams::lang.plugin.members'
                url: invato/teams/members
                icon: icon-user
                permissions:
                    - 'invato.teams.manage_teams'
                    - 'invato.teams.manage_members'
