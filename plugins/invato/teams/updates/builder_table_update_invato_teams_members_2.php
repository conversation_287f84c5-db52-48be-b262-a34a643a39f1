<?php namespace Invato\Teams\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoTeamsMembers2 extends Migration
{
    public function up()
    {
        Schema::table('invato_teams_members', function($table)
        {
            $table->integer('parent_id')->default(0)->after('sort_order');
        });
    }
    
    public function down()
    {
        Schema::table('invato_teams_members', function($table)
        {
            $table->dropColumn('parent_id');
        });
    }
}
