<?php namespace Invato\Teams\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoTeamsMembers extends Migration
{
    public function up()
    {
        Schema::table('invato_teams_members', function($table)
        {
            $table->string('phonenumber', 255)->nullable()->after('title');
            $table->string('email', 255)->nullable()->after('phonenumber');
        });
    }
    
    public function down()
    {
        Schema::table('invato_teams_members', function($table)
        {
            $table->dropColumn('phonenumber');
            $table->dropColumn('email');
        });
    }
}
