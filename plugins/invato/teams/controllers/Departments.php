<?php

namespace Invato\Teams\Controllers;

use Backend\Behaviors\FormController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;

class Departments extends Controller
{
    public $implement = [
        FormController::class,
        ListController::class,
    ];

    public $formConfig = 'config_form.yaml';

    public $listConfig = 'config_list.yaml';

    public $requiredPermissions = [
        'invato.teams.manage_teams',
        'invato.teams.manage_departments',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Teams', 'main-menu-item', 'side-menu-item');
    }
}
