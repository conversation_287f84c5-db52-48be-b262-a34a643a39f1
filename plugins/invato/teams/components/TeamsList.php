<?php namespace Invato\Teams\Components;

use Cms\Classes\ComponentBase;
use Invato\Teams\Models\Department;
use Invato\Teams\Models\Member;
use Invato\Teams\Models\TeamsSettings;

/**
 * TeamsPage Component
 *
 * @link https://docs.octobercms.com/3.x/extend/cms-components.html
 */
class TeamsList extends ComponentBase
{
    public $departments;
    public $members;
    public $teamPage;
    public $departmentPage;
    public $memberPage;

    public function componentDetails()
    {
        return [
            'name' => 'TeamsPage Component',
            'description' => 'No description provided yet...'
        ];
    }

    /**
     * @link https://docs.octobercms.com/3.x/element/inspector-types.html
     */
    public function defineProperties()
    {
        return [];
    }

    public function onRun()
    {
        $this->departments = $this->page['Departments'] = Department::all();
        $this->members = $this->page['Members'] = Member::all();
        $this->teamPage = $this->page['teamPage'] = TeamsSettings::get('teamPage');
        $this->departmentPage = $this->page['departmentPage'] = TeamsSettings::get('departmentPage');
        $this->memberPage = $this->page['memberPage'] = TeamsSettings::get('memberPage');
    }
}
