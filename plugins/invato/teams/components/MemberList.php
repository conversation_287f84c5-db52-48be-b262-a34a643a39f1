<?php namespace Invato\Teams\Components;

use Cms\Classes\ComponentBase;
use Invato\Teams\Models\Department;
use Invato\Teams\Models\Member;
use Invato\Teams\Models\TeamsSettings;

/**
 * MemberPage Component
 *
 * @link https://docs.octobercms.com/3.x/extend/cms-components.html
 */
class MemberList extends ComponentBase
{
    public $members;
    public $department;
    public $departments;
    public $teamPage;
    public $departmentPage;
    public $memberPage;

    public function componentDetails()
    {
        return [
            'name' => 'MemberPage Component',
            'description' => 'No description provided yet...'
        ];
    }

    /**
     * @link https://docs.octobercms.com/3.x/element/inspector-types.html
     */
    public function defineProperties()
    {
        return [
            'department' => [
                'title' => 'Department',
                'type' => 'dropdown',
                'comment' => 'List Members from this department'
            ],
            'pagination' => [
                'title' => 'Pagination',
                'type' => 'checkbox',
                'comment' => 'Show pagination',
                'default' => 'true'
            ],
            'itemsPerPage' => [
                'title' => 'Items per page',
                'type' => 'string',
                'comment' => '# of items per page to show'
            ],
        ];
    }

    public function onRun()
    {
        $this->teamPage = $this->page['teamPage'] = TeamsSettings::get('teamPage');
        $this->departmentPage = $this->page['departmentPage'] = TeamsSettings::get('departmentPage');
        $this->memberPage = $this->page['memberPage'] = TeamsSettings::get('memberPage');

        $this->members = $this->getMembers();
        $this->department = $this->getDepartment();
        $this->departments = Department::all();
    }

    public function getDepartment()
    {

        if (!$this->property('department')) {
            return;
        }

        $department = new Department;

        $departmentQuery = $department->query();
        $departmentQuery->where('slug', $this->property('department'));
        $department = $departmentQuery->first();

        return $department;

    }

    public function getMembers()
    {
        if ($this->property('department')) {
            $department = new Department;

            $departmentQuery = $department->query();
            $departmentQuery->where('slug', $this->property('department'));
            $department = $departmentQuery->first();
            $memberQuery = $department->members();

            if ($this->property('pagination')) {

                return $memberQuery->paginate($this->property('itemsPerPage'), ['*'], 'members');
            }
            $memberQuery = $memberQuery->get();
            return $memberQuery;
        }

        $members = new Member();
        $memberQuery = $members->query();

        if ($this->property('pagination')) {
            return $memberQuery->paginate($this->property('itemsPerPage'), ['*'], 'members');
        }

        $memberQuery = $memberQuery->get();
        return $memberQuery;
    }

}
