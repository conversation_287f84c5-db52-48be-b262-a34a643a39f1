<?php namespace Invato\Teams\Components;

use Cms\Classes\ComponentBase;
use Invato\Teams\Models\Department;
use Invato\Teams\Models\Member;
use Invato\Teams\Models\TeamsSettings;

/**
 * MemberPage Component
 *
 * @link https://docs.octobercms.com/3.x/extend/cms-components.html
 */
class MemberList extends ComponentBase
{
    public $members;
    public $department;
    public $departments;
    public $teamPage;
    public $departmentPage;
    public $memberPage;

    public function componentDetails()
    {
        return [
            'name' => 'MemberPage Component',
            'description' => 'No description provided yet...'
        ];
    }

    /**
     * @link https://docs.octobercms.com/3.x/element/inspector-types.html
     */
    public function defineProperties()
    {
        return [
            'department' => [
                'title' => 'Department',
                'type' => 'dropdown',
                'comment' => 'List Members from this department'
            ],
            'pagination' => [
                'title' => 'Pagination',
                'type' => 'checkbox',
                'comment' => 'Show pagination',
                'default' => 'true'
            ],
            'itemsPerPage' => [
                'title' => 'Items per page',
                'type' => 'string',
                'comment' => '# of items per page to show'
            ],
        ];
    }

    public function onRun()
    {
        $this->teamPage = $this->page['teamPage'] = TeamsSettings::get('teamPage');
        $this->departmentPage = $this->page['departmentPage'] = TeamsSettings::get('departmentPage');
        $this->memberPage = $this->page['memberPage'] = TeamsSettings::get('memberPage');

        $this->members = $this->getMembers();
        $this->department = $this->getDepartment();
        $this->departments = Department::all();
    }

    public function getDepartment()
    {
        if (!$this->property('department')) {
            return null;
        }

        // The department property contains an ID, not a slug
        return Department::find($this->property('department'));
    }

    public function getMembers()
    {
        if ($this->property('department')) {
            $department = $this->getDepartment();

            // Debug: Log what we found
            \Log::info('Department property: ' . $this->property('department'));
            \Log::info('Department found: ' . ($department ? 'Yes (ID: ' . $department->id . ')' : 'No'));

            // Check if department was found
            if (!$department) {
                \Log::info('Department not found, returning empty collection');
                // Return empty collection if department not found
                return collect();
            }

            \Log::info('Department members count: ' . $department->members->count());

            // For pagination, we need to use the relationship query builder
            if ($this->property('pagination')) {
                $memberQuery = $department->members();
                return $memberQuery->paginate($this->property('itemsPerPage'), ['*'], 'members');
            }

            // For non-paginated results, use the relationship property
            return $department->members;
        }

        $members = new Member();
        $memberQuery = $members->query();

        if ($this->property('pagination')) {
            return $memberQuery->paginate($this->property('itemsPerPage'), ['*'], 'members');
        }

        return $memberQuery->get();
    }

}
