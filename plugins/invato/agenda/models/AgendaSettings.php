<?php namespace Invato\Agenda\Models;

use Cms\Classes\Page;
use Model;

class AgendaSettings  extends \System\Models\SettingModel
{
    public $settingsCode = 'invato_agenda_settings';

    public $settingsFields = 'fields.yaml';

    public function initSettingsData()
    {
        $this->eventPage = 'agenda\/evenement';
    }


    public function getAgendaPageOptions()
    {
        return Page::sortBy('baseFileName')->lists('baseFileName', 'baseFileName');
    }

    public function getEventPageOptions()
    {
        return Page::sortBy('baseFileName')->lists('baseFileName', 'baseFileName');
    }

}
