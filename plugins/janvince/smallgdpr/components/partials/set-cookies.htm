var date = new Date();

date.setDate(date.getDate() + {{ cookiesSettingsGet('set_cookies_lifetime_days') ? cookiesSettingsGet('set_cookies_lifetime_days') : 365 }});

document.cookie = "{{ 'sg-cookies'~(sgCookiesLocalePrefix ? sgCookiesLocalePrefix)~'-consent=1; path=/; expires='}}" + date.toGMTString();            

{% if cookiesSettingsGet('cookies',null)|length %}

    {% for cookie in cookiesSettingsGet('cookies') %}

        {% if setOnlyRequiredCookies|default(false) %}

            {% if cookie.required %}

                document.cookie = "{{ 'sg-cookies'~(sgCookiesLocalePrefix ? sgCookiesLocalePrefix)~'-'~( cookie.slug ? cookie.slug : loop.index)~'=1; path=/; expires='}}" + date.toGMTString();    
                
            {% endif %}

        {% else %}

            {% if setOnlyManageCookies|default(false) %}

                var item = document.getElementById('sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix }}-{{cookie.slug}}{{customId|default('custom')}}');

                if( item.checked == true ) {
                    document.cookie = 'sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix }}-{{cookie.slug}}=1; path=/; expires=' + date.toGMTString();    
                } else {
                    document.cookie = 'sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix}}-{{cookie.slug}}=0; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';    
                }

            {% else %}

                document.cookie = "{{ 'sg-cookies'~(sgCookiesLocalePrefix ? sgCookiesLocalePrefix)~'-'~( cookie.slug ? cookie.slug : loop.index)~'=1; path=/; expires='}}" + date.toGMTString();    

            {% endif %}

        {% endif %}

    {% endfor %}

{% endif %}