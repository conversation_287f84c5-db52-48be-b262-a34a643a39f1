{"navigation": [{"title": "Introduction", "description": "explains how to get started using the User plugin", "children": [{"title": "Events", "description": "lists all the available events found in this plugin", "slug": "events"}], "slug": "introduction"}, {"title": "Components", "description": "lists all the available components found in this plugin", "children": [{"title": "Session", "description": "checks the user session and includes the user object on the page", "slug": "component-session"}, {"title": "Account", "description": "user management form for updating profile and security details", "slug": "component-account"}, {"title": "Authentication", "description": "provides services for logging a user in", "slug": "component-authentication"}, {"title": "Registration", "description": "provides services for registering a user", "slug": "component-registration"}, {"title": "Reset Password", "description": "confirms and resets the user with a new password", "slug": "component-reset-password"}]}, {"title": "Services", "children": [{"title": "Auth Manager", "description": "services for managing the user session", "slug": "auth-manager"}, {"title": "Impersonation", "description": "extra services for impersonating users", "slug": "auth-impersonation"}, {"title": "<PERSON><PERSON>", "description": "extra services for JWT authentication", "slug": "auth-bearer-tokens"}]}], "content": {"introduction": "# Introduction\n\nThe User plugin brings frontend users to the CMS, allowing your users to register and sign in to their account.\n\nTo get started, we recommend installing this plugin with the `RainLab.Vanilla` theme to demonstrate its functionality.\n\n- https://github.com/rainlab/vanilla-theme\n", "events": "# Events\n\nThis plugin will fire some global events that can be useful for interacting with other plugins.\n\nEvents | Description\n------ | ---------------\n**rainlab.user.beforeAuthenticate** | Before the user is attempting to authenticate using the Account component.\n**rainlab.user.authenticate** | Provides custom response logic after authentication.\n**rainlab.user.login** | The user has successfully signed in.\n**rainlab.user.logout** | The user has successfully signed out.\n**rainlab.user.lockout** | Provides custom logic when a login attempt has been rate limited.\n**rainlab.user.activate** | The user has verified their email address.\n**rainlab.user.deactivate** | The user has opted-out of the site by deactivating their account. This should be used to disable any content the user may want removed.\n**rainlab.user.beforeRegister** | Before the user's registration is processed. Passed the `$input` variable by reference to enable direct modifications to the user input.\n**rainlab.user.register** | Provides custom response logic after registration.\n**rainlab.user.passwordReset** | Provides custom logic for resetting a user password.\n**rainlab.user.beforeUpdate** | Before the user updates their profile from the Account component.\n**rainlab.user.update** | The user has updated their profile information.\n**rainlab.user.canDeleteUser** | Triggered before a user is deleted. This event should return true if the user has dependencies and should be soft deleted to retain those relationships and allow the user to be restored. Otherwise, it will be deleted forever.\n**rainlab.user.getNotificationVars** | Fires when sending a user notification to enable passing more variables to the email templates. Passes the `$user` model the template will be for.\n**rainlab.user.view.extendListToolbar** | Fires when the user listing page's toolbar is rendered.\n**rainlab.user.view.extendPreviewToolbar** | Fires when the user preview page's toolbar is rendered.\n**rainlab.user.view.extendPreviewTabs** | Provides an opportunity to add tabs to the user preview page in the admin panel. The event should return an array of `[Tab Name => ~/path/to/partial.php]`\n\nHere is an example of hooking an event:\n\n```php\nEvent::listen('rainlab.user.deactivate', function($user) {\n    // Hide all posts by the user\n});\n```\n\nA common requirement is to adapt another to a legacy authentication system. In the example below, the `WordPressLogin::check` method would check the user password using an alternative hashing method, and if successful, update to the new one used by October.\n\n```php\nEvent::listen('rainlab.user.beforeAuthenticate', function($component, $credentials) {\n    $email = $credentials['email'] ?? null;\n    $password = $credentials['password'] ?? null;\n\n    // Check that the user exists with the provided email\n    $user = Auth::getProvider()->retrieveByCredentials(['email' => $email]);\n    if (!$user) {\n        return;\n    }\n\n    // The user is logging in with their old WordPress account\n    // for the first time. Rehash their password using the new\n    // October system.\n    if (WordPressLogin::check($user->password, $password)) {\n        $user->password = $user->password_confirmation = $password;\n        $user->forceSave();\n    }\n});\n```\n", "component-session": "# Session Component\n\nThe session component should be added to a layout that has registered users. It has no default markup.\n\n## User Variable\n\nYou can check the logged in user by accessing the **{{ user }}** Twig variable:\n\n```twig\n{% if user %}\n    <p>Hello {{ user.first_name }}</p>\n{% else %}\n    <p>Nobody is logged in</p>\n{% endif %}\n```\n\n## Signing Out\n\nThe Session component allows a user to sign out of their session.\n\n```html\n<a data-request=\"onLogout\" data-request-data=\"{ redirect: '/good-bye' }\">Sign out</a>\n```\n\n## Page Restriction\n\nThe Session component allows the restriction of a page or layout by allowing only signed in users, only guests or no restriction. This example shows how to restrict a page to users only:\n\n```ini\ntitle = \"Restricted page\"\nurl = \"/users-only\"\n\n[session]\nsecurity = \"user\"\nredirect = \"home\"\n```\n\nThe `security` property can be user, guest or all. The `redirect` property refers to a page name to redirect to when access is restricted.\n\n## Route Restriction\n\nAccess to routes can be restricted by applying the `AuthMiddleware`.\n\n```php\nRoute::group(['middleware' => \\RainLab\\User\\Classes\\AuthMiddleware::class], function () {\n    // All routes here will require authentication\n});\n```\n\n## Token Variable\n\nThe `token` Twig variable can be used for generating a new bearer token for the signed in user.\n\n```twig\n{% do response(\n    ajaxHandler('onLogin').withVars({\n        token: session.token\n    })\n) %}\n```\n\nThe `checkToken` property of the component is used to verify a supplied token in the request headers `(Authorization: Bearer TOKEN)`.\n\n```ini\n[session]\ncheckToken = 1\n```\n", "component-account": "# Account Component\n\nThe account component provides a method to update the logged in user profile, verify email address, enable two-factor authentication, clear browser sessions and delete their account.\n\n```ini\ntitle = \"Account\"\nurl = \"/account/:code?\"\n\n[account]\nisDefault = 1\n==\n...\n```\n\nFor displaying and clearing other browser sessions for the user, the session driver must be set to `database`. Open the **config/session.php** file and change the driver, this can also be set in the **.env** file with the `SESSION_DRIVER` variable.\n\n```php\n'driver' => env('SESSION_DRIVER', 'database'),\n```\n\n## API\n\nThese AJAX handlers are available.\n\nHandler | Description\n------- | -------------\n**onUpdateProfile** | Updates the user profile\n**onVerifyEmail** | Verifies the user email address\n**onEnableTwoFactor** | Enables two-factor authentication\n**onConfirmTwoFactor** | Confirms two-factor authentication using a valid code\n**onShowTwoFactorRecoveryCodes** | Displays the two-factor recovery codes\n**onRegenerateTwoFactorRecoveryCodes** | Deletes and recreates the recovery codes\n**onDisableTwoFactor** | Disables two-factor authentication\n**onDeleteOtherSessions** | Logs out other user sessions\n**onDeleteUser** | Deletes the user account\n\nThese variables are available on the component object.\n\nVariable | Description\n-------- | -------------\n`user` | returns the logged in user\n`sessions` | returns browser sessions for the user\n`twoFactorEnabled` | returns true if the user has two factor enabled\n`twoFactorRecoveryCodes` | returns an array of recovery codes, if available\n\n## Examples\n\nThe following example shows how to update the user profile using the `onUpdateProfile` handler.\n\n```html\n<form\n    method=\"post\"\n    class=\"account-editor\"\n    data-request=\"onUpdateProfile\"\n    data-request-flash>\n\n    <input\n        name=\"first_name\"\n        type=\"text\"\n        value=\"{{ user.first_name }}\"\n        class=\"form-control\"\n        placeholder=\"First name\"\n    />\n\n    <input\n        name=\"last_name\"\n        type=\"text\"\n        value=\"{{ user.last_name }}\"\n        class=\"form-control\"\n        placeholder=\"Last name\"\n    />\n\n    <button\n        class=\"btn btn-primary\"\n        data-attach-loading\n        type=\"submit\">\n        Save\n    </button>\n</form>\n```\n", "component-authentication": "# Authentication Component\n\n## Overriding Functionality\n\nHere is how you would override the `onLogin()` handler to log any error messages. Inside the page code, define this method:\n\n```php\nfunction onLogin()\n{\n    try {\n        return $this->account->onLogin();\n    }\n    catch (Exception $ex) {\n        Log::error($ex);\n    }\n}\n```\n\nHere the local handler method will take priority over the **account** component's event handler. Then we simply inherit the logic by calling the parent handler manually, via the component object (`$this->account`).\n", "component-registration": "# Registration Component\n\n## Using a Login Name\n\nBy default the User plugin will use the email address as the login name. To switch to using a user defined login name, navigate to the backend under System > Users > User Settings and change the Login attribute under the Sign in tab to be **Username**. Then simply ask for a username upon registration by adding the username field:\n\n```twig\n<form data-request=\"onRegister\">\n    <label>Full Name</label>\n    <input name=\"name\" type=\"text\" placeholder=\"Enter your full name\">\n\n    <label>Email</label>\n    <input name=\"email\" type=\"email\" placeholder=\"Enter your email\">\n\n    <label>Username</label>\n    <input name=\"username\" placeholder=\"Pick a login name\">\n\n    <label>Password</label>\n    <input name=\"password\" type=\"password\" placeholder=\"Choose a password\">\n\n    <button type=\"submit\">Register</button>\n</form>\n```\n\nWe can add any other additional fields here too, such as `phone`, `company`, etc.\n\n\n\n## Password Requirements\n\n### Password Length\n\nBy default, the User plugin requires a minimum password length of 8 characters for all users when registering or changing their password. You can change this length requirement by going to backend and navigating to System > Users > User Settings. Inside the Registration tab, a **Minimum password length** field is provided, allowing you to increase or decrease this limit to your preferred length.\n\n### Password Confirmation\n\nThe `password_confirmation` field can be used to prompt the user to enter their password a second time. This input name is optional, if it is found in the postback data, then it will be validated. The following is an example.\n\n```twig\n<input name=\"password\" type=\"password\" placeholder=\"Choose a password\" />\n<input name=\"password_confirmation\" type=\"password\" placeholder=\"Repeat password\" />\n```\n\n## Error Handling\n\n### Flash Messages\n\nThis plugin makes use of October's [`Flash API`](https://octobercms.com/docs/markup/tag-flash). In order to display the error messages, you need to place the following snippet in your layout or page.\n\n```twig\n{% flash %}\n    <div class=\"alert alert-{{ type == 'error' ? 'danger' : type }}\">{{ message }}</div>\n{% endflash %}\n```\n\n### AJAX Errors\n\nThe User plugin displays AJAX error messages in a simple ``alert()``-box by default. However, this might scare non-technical users. You can change the default behavior of an AJAX error from displaying an ``alert()`` message, like this:\n\n```js\n<script>\n    $(window).on('ajaxErrorMessage', function (event, message){\n\n        // This can be any custom JavaScript you want\n        alert('Something bad happened, mate, here it is: ' + message);\n\n        // This will stop the default alert() message\n        event.preventDefault();\n\n    })\n</script>\n```\n\n### Checking Email/Username Availability\n\nHere is a simple example of how you can quickly check if an email address / username is available in your registration forms. First, inside the page code, define the following AJAX handler to check the login name, here we are using the email address:\n\n```php\npublic function onCheckEmail()\n{\n    $user = Auth::getProvider()->retrieveByCredentials([\n        'email' => post('email')\n    ]);\n\n    return ['isTaken' => $user ? 1 : 0];\n}\n```\n\nFor the email input we use the `data-request` and `data-track-input` attributes to call the `onCheckEmail` handler any time the field is updated. The `data-request-success` attribute will call some jQuery code to toggle the alert box.\n\n```html\n<div class=\"form-group\">\n    <label>Email address</label>\n    <input\n        name=\"email\"\n        type=\"email\"\n        class=\"form-control\"\n        data-request=\"onCheckEmail\"\n        data-request-success=\"$('#loginTaken').toggle(!!data.isTaken)\"\n        data-track-input />\n</div>\n\n<div id=\"loginTaken\" class=\"alert alert-danger\" style=\"display: none\">\n    Sorry, that login name is already taken.\n</div>\n```\n", "component-reset-password": "# Reset Password Component\n\nThe reset password component allows a user to reset their password if they have forgotten it.\n\n```ini\ntitle = \"Forgotten your password?\"\nurl = \"/forgot-password/:code?\"\n\n[resetPassword]\nparamCode = \"code\"\n==\n{% component 'resetPassword' %}\n```\n\nThis will display the initial restoration request form and also the password reset form used after the verification email has been received by the user. The `paramCode` is the URL routing code used for resetting the password.\n", "auth-manager": "# Auth Manager\n\nThere is an `Auth` facade you may use for common tasks, it primarily inherits the `October\\Rain\\Auth\\Manager` class for functionality.\n\nYou may use `create` method on the `User` model to register an account.\n\n```php\n$user = \\RainLab\\User\\Models\\User::create([\n    'first_name' => 'Some',\n    'last_name' => 'User',\n    'email' => '<EMAIL>',\n    'password' => 'ChangeMe888',\n    'password_confirmation' => 'ChangeMe888',\n]);\n```\n\nThe `markEmailAsVerified` method can be used to activate an existing user.\n\n```php\n// Auto activate this user\n$user->markEmailAsVerified();\n```\n\nThe `check` method is a quick way to check if the user is signed in.\n\n```php\n// Returns true if signed in.\n$loggedIn = Auth::check();\n```\n\nTo return the user model that is signed in, use `user` method instead.\n\n```php\n// Returns the signed in user\n$user = Auth::user();\n```\n\nYou may authenticate a user by providing their login and password with the `attempt` method.\n\n```php\n// Authenticate user by credentials\n$user = Auth::attempt([\n    'email' => post('email'),\n    'password' => post('password')\n]);\n```\n\nThe second argument is used to store a non-expire cookie for the user.\n\n```php\n$user = Auth::attempt([...], true);\n```\n\nYou can also authenticate as a user simply by passing the user model along with the `login` method.\n\n```php\n// Sign in as a specific user\nAuth::login($user);\n```\n\nThe second argument will store the non-expire cookie for the user.\n\n```php\n// Sign in and remember the user\nAuth::login($user, true);\n```\n\nYou may look up a user by their email or login name using the `retrieveByCredentials` method via the provider class.\n\n```php\n$user = Auth::getProvider()->retrieveByCredentials([\n    'email' => '<EMAIL>'\n]);\n```\n\n## Guest Users\n\nCreating a guest user allows the registration process to be deferred. For example, making a purchase without needing to register first. Guest users are not able to sign in and will be added to the user group with the code `guest`.\n\n> **Note**: If you are upgrading from an older version of this plugin, to enable guest users you may need to remove the UNIQUE index on the `email` column in the `users` table.\n\nUse the `is_guest` attribute to create a guest user, it will return a user object and can be called multiple times. The unique identifier is the email address, which is a required field.\n\n```php\n$user = \\RainLab\\User\\Models\\User::create([\n    'first_name' => 'Some',\n    'last_name' => 'User',\n    'email' => '<EMAIL>',\n    'is_guest' => true\n]);\n```\n\nWhen a user registers with the same email address using the `create` method, another account is created and they will not inherit the existing guest user account.\n\n```php\n// This will not throw an \"Email already taken\" error\n$user = \\RainLab\\User\\Models\\User::create([\n    'first_name' => 'Some',\n    'last_name' => 'User',\n    'email' => '<EMAIL>',\n    'password' => 'ChangeMe888',\n    'password_confirmation' => 'ChangeMe888',\n]);\n```\n\nYou may convert a guest to a registered user with the `convertToRegistered` method. This will send them an invitation using the `user:invite_email` template to set up a new password. When a user is converted they will be added to the user group with the code `registered`.\n\n```php\nUser::where('email', '<EMAIL>')->first();\n$user->convertToRegistered();\n```\n\nTo disable the notification and password reset, pass the first argument as false.\n\n```php\n$user->convertToRegistered(false);\n```\n", "auth-impersonation": "# Auth Impersonation\n\nThe `Auth` facade supports user impersonation. Use the `impersonate` method to impersonate another user.\n\n```php\nAuth::impersonate($user);\n```\n\nTo stop impersonating, use the `stopImpersonate` method. This will restore the account that was previously logged in, if applicable.\n\n```php\nAuth::stopImpersonate();\n```\n\nThe `isImpersonator` method can be used to check if the user is currently impersonating.\n\n```php\nif (Auth::isImpersonator()) {\n    // User is currently impersonating another user\n}\n```\n\nUse the `getRealUser` method to return the underlying user they are impersonating someone else, or it will return the active user if they are not impersonating.\n\n```php\n$user = Auth::getRealUser();\n```\n", "auth-bearer-tokens": "# Auth Bearer Tokens\n\nThe `Auth` implements a native bearer token implementation (JWT).\n\n## Generating a Token\n\nWhen working with authentication via bearer tokens, the `getBearerToken` method can be used to obtain a bearer token (JWT) for the current user. It expires after 1 hour by default.\n\n```php\n$token = Auth::getBearerToken();\n```\n\nYou may also pass a user to this method to get a token for a specified user.\n\n```php\n$token = Auth::getBearerToken($user);\n```\n\nWhen using the [Session component](./component-session.md), the `token` variable is available on this object.\n\n```twig\n{{ session.key}}\n```\n\n## Verifying a Token\n\nWhen verifying a token, use the `checkBearerToken` method that will return a valid user who is associated token, or false if the token is invalid or that user is no longer found.\n\n```php\n$user = Auth::checkBearerToken($token);\n```\n\nThe `loginUsingBearerToken` method is used to verify a supplied token and authenticate the user. The method returns the user if the verification was successful.\n\n```php\nif ($jwtToken = Request::bearerToken()) {\n    Auth::loginUsingBearerToken($jwtToken);\n}\n```\n\n> **Note**: Further configuration for this functionality can be found in the **rainlab.user::config.bearer_token** configuration value.\n\n## Working with APIs\n\nWhen [building API endpoints using CMS pages](https://docs.octobercms.com/3.x/cms/resources/building-apis.html) it can be useful to use a page for handling the authentication logic. The following is a simple example that includes various API endpoints.\n\n```twig\ntitle = \"User API Page\"\nurl = \"/api/user/:action\"\n\n[resetPassword]\n[account]\n[session]\ncheckToken = 1\n==\n{% if this.param.action == 'signin' %}\n    {% do response(\n        ajaxHandler('onLogin').withVars({\n            token: session.token()\n        })\n    ) %}\n{% endif %}\n\n{% if this.param.action == 'register' %}\n    {% do response(ajaxHandler('onRegister')) %}\n{% endif %}\n\n{% if this.param.action == 'logout' %}\n    {% do response(ajaxHandler('onLogout')) %}\n{% endif %}\n\n{% if this.param.action == 'refresh' %}\n    {% do response({ data: {\n        token: session.token()\n    }}) %}\n{% endif %}\n```\n\nAn API layout to verify the user can be used for other API endpoints.\n\n```twig\ndescription = \"Auth API Layout\"\nis_priority = 1\n\n[session]\ncheckToken = 1\n==\n{% if session.user %}\n    {% page %}\n{% else %}\n    {% do abort(403, 'Access Denied') %}\n{% endif %}\n```\n\n### Email Verification Example\n\nThe following example shows how to implement the email verification process as an API, with the supported actions of **request** (default) and **confirm**.\n\nThe `setUrlForEmailVerification` method call overrides the verification URL in the email sent to the user. The `verify` code in the URL acts as a one-time bearer token that authenticates the user (`session.user`) for a single page cycle. The standard redirect is disabled by adding `redirect=0` to the verification URL.\n\n```twig\ntitle = \"User Email Verification API\"\nurl = \"/api/user/verify/:action?request\"\n\n[account]\n[session]\ncheckToken = 1\n==\n{% if not session.user %}\n    {% do response({\n        error: 'Access Denied'\n    }, 403) %}\n{% endif %}\n\n{% if this.param.action == 'request' %}\n    {% do session.user.setUrlForEmailVerification(\n        this|page({ action: 'confirm' }) ~ '?redirect=0'\n    ) %}\n\n    {% do response(ajaxHandler('onVerifyEmail')) %}\n{% endif %}\n\n{% if this.param.action == 'confirm' %}\n    {% if session.user.hasVerifiedEmail %}\n        {% do response({\n            success: \"Thank you for verifying your email.\"\n        }, 201) %}\n    {% else %}\n        {% do response({\n            error: \"The provided email verification code was invalid.\"\n        }, 400) %}\n    {% endif %}\n{% endif %}\n```\n"}}