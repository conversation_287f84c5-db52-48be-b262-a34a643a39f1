<div class="layout control-scrollpanel" id="pages-side-panel">
    <div class="layout-cell">
        <div class="layout-relative fix-button-container">
            <?php if (in_array('pageList', $activeWidgets)) : ?>
                <form class="layout <?= !($activeWidgets[0] === 'pageList') ? 'oc-hide hide' : ''; ?>" data-content-id="pages" data-object-type="page" data-type-icon="oc-icon-files-o" onsubmit="return false">
                    <?= $this->widget->pageList->render() ?>
                </form>
            <?php endif; ?>

            <?php if (in_array('menuList', $activeWidgets)) : ?>
                <form class="layout <?= !($activeWidgets[0] === 'menuList') ? 'oc-hide hide' : ''; ?>" data-content-id="menus" data-object-type="menu" data-type-icon="oc-icon-sitemap" onsubmit="return false">
                    <?= $this->widget->menuList->render() ?>
                </form>
            <?php endif; ?>

            <?php if (in_array('contentList', $activeWidgets)) : ?>
                <form class="layout <?= !($activeWidgets[0] === 'contentList') ? 'oc-hide hide' : ''; ?>" data-content-id="content" data-object-type="content" data-type-icon="oc-icon-file-text-o" onsubmit="return false">
                    <?= $this->widget->contentList->render() ?>
                </form>
            <?php endif; ?>

            <?php if (in_array('snippetList', $activeWidgets)) : ?>
                <form class="layout <?= !($activeWidgets[0] === 'snippetList') ? 'oc-hide hide' : ''; ?>" data-content-id="snippet" data-object-type="snippet" data-type-icon="icon-newspaper-o" onsubmit="return false">
                    <?= $this->widget->snippetList->render() ?>
                </form>
            <?php endif; ?>
        </div>
    </div>
</div>
