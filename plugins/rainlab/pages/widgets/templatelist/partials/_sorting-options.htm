<?php foreach ($this->sortingProperties as $propertyName=>$propertyTitle): ?>
    <li role="presentation" <?php if ($this->getSortingProperty() == $propertyName): ?>class="active"<?php endif ?>>
        <a role="menuitem" tabindex="-1" href="javascript:;" data-stripe-load-indicator data-request="<?= $this->getEventHandler('onApplySorting') ?>" data-request-data="sortProperty: '<?= e($propertyName) ?>'">
            <?= e(trans($propertyTitle)) ?>
        </a>
    </li>
<?php endforeach ?>
