<?php return [
  'plugin' => [
    'add' => 'Vytvořit plugin',
    'no_records' => 'Žádný plugin nenalezen',
    'no_description' => 'Tento plugin nemá žádný popisek',
    'no_name' => 'Bez jména',
    'search' => 'Vyhledávání...',
    'filter_description' => 'Zobrazit všechny pluginy, nebo pouze vaše.',
    'settings' => 'Nastavení',
    'entity_name' => 'Plugin',
    'field_name' => 'Název',
    'field_author' => 'Autor',
    'field_description' => 'Popis',
    'field_icon' => 'Ikona pluginu',
    'field_plugin_namespace' => 'Jmenný prostor pluginu',
    'field_author_namespace' => 'Jmenný prostor autora',
    'field_namespace_description' => 'Jmenný prostor může obsahovat pouze znaky, číslice a měl by začínat písmenem. Například Blog.',
    'field_author_namespace_description' => 'Zadaný jmenný prostor nebude možno přes Builder poté změnit. Příklad jmenného prostoru: JohnSmith.',
    'tab_general' => 'Základní parametry',
    'tab_description' => 'Popis',
    'field_homepage' => 'Domovská URL pluginu',
    'error_settings_not_editable' => 'Nastavení tohoto pluginu nelze přes Builder měnit, protože nemá soubor plugin.yaml.',
    'update_hint' => 'Překlad názvu a popisku můžete měnit v menu Lokalizace.',
    'manage_plugins' => 'Tvorba a úprava pluginů.',
  ],
  'author_name' => [
    'title' => 'Jméno autora',
    'description' => 'Výchozí jméno autora pro nově vytvořené pluginy. Toto jméno však můžete změnit při vytváření nového pluginu, nebo poté v editaci.',
  ],
  'author_namespace' => [
    'title' => 'Jmenný prostor autora',
    'description' => 'Pokud budete chtít plugin umístit na stránkách OctoberCMS, jmenný prostor by se měl být pro všechny vaše pluginy shodný. Více detailů najdete v dokumentaci publikace pluginů.',
  ],
  'database' => [
    'menu_label' => 'Databáze',
    'no_records' => 'Žádné tabulky nebyly nalezeny',
    'search' => 'Vyhledávání...',
    'confirmation_delete_multiple' => 'Opravdu chcete odstranit vybrané tabulky?',
    'field_name' => 'Název databázové tabulky',
    'tab_columns' => 'Sloupce',
    'column_name_name' => 'Sloupec',
    'column_name_required' => 'Zadejte prosím název sloupce',
    'column_name_type' => 'Typ',
    'column_type_required' => 'Vyberte prosím typ sloupce',
    'column_name_length' => 'Délka',
    'column_validation_length' => 'Délka by měla být zadaná číselně, nebo zadaná jako číslo a přesnost (10,2) pro desetinná čísla. Mezery nejsou povolené.',
    'column_validation_title' => 'V názvu sloupce mohou být pouze čísla, malá písmena a podtržítko.',
    'column_name_unsigned' => 'Bez znaménka',
    'column_name_nullable' => 'Nulový',
    'column_auto_increment' => 'AUTOINCR',
    'column_default' => 'Výchozí',
    'column_auto_primary_key' => 'PK',
    'tab_new_table' => 'Nová tabulka',
    'btn_add_column' => 'Přidat sloupec',
    'btn_delete_column' => 'Smazat sloupec',
    'confirm_delete' => 'Opravdu chcete smazat tuto tabulku?',
    'error_enum_not_supported' => 'Tabulka obsahuje sloupce s typem "enum" které Builder aktuálně nepodporuje.',
    'error_table_name_invalid_prefix' => 'Název tabulky by měl začínat prefixem pluginu: \':prefix\'.',
    'error_table_name_invalid_characters' => 'Formát názvu tabulky není správný, měl by obsahovat pouze písmena, číslice a nebo podtržítka. Název by měl začínat písmenem a neměl by obsahovat mezery.',
    'error_table_duplicate_column' => 'Takový název sloupce již existuje: \':column\'.',
    'error_table_auto_increment_in_compound_pk' => 'An auto-increment column cannot be a part of a compound primary key.',
    'error_table_mutliple_auto_increment' => 'Tabulka nemůže obsahovat více sloupců s auto-increment vlastností.',
    'error_table_auto_increment_non_integer' => 'Auto-increment sloupec by měl mít číselný typ.',
    'error_table_decimal_length' => 'Zápis délky pro typ :type by měl být ve formátu \'10,2\', bez mezer.',
    'error_table_length' => 'Zápis délky pro typ :type by měl být zadaný jako číslo.',
    'error_unsigned_type_not_int' => 'Chyba ve sloupci \':column\'. Přiznak \'bez znaménka\' můžete použít pouze pro číselné typy.',
    'error_integer_default_value' => 'Chybná výchozí hodnota pro číselný sloupec \':column\'. Povolené formáty jsou \'10\', \'-10\'.',
    'error_decimal_default_value' => 'Chybná výchozí hodnota pro desetinný sloupec \':column\'. Povolené formáty jsou \'1.00\', \'-1.00\'.',
    'error_boolean_default_value' => 'Chybná výchozí hodnota pro pravdivostní sloupec \':column\'. Povolené hodnoty jsou \'0\' and \'1\'.',
    'error_unsigned_negative_value' => 'Výchozí hodnota pro sloupec bez znaménka \':column\' nemůže být záporná.',
    'error_table_already_exists' => 'Tabulka \':name\' již v databázi existuje.',
  ],
  'model' => [
    'menu_label' => 'Modely',
    'entity_name' => 'Model',
    'no_records' => 'Žádný model nebyl nalezen',
    'search' => 'Vyhledávání...',
    'add' => 'Přidat...',
    'forms' => 'Formuláře',
    'lists' => 'Listování',
    'field_class_name' => 'Název třídy',
    'field_database_table' => 'Databazová tabulka',
    'error_class_name_exists' => 'Soubor modelu pro tuto třídu již existuje: :path',
    'add_form' => 'Přidat formulář',
    'add_list' => 'Přidat listování',
  ],
  'form' => [
    'saved' => 'Formulář byl úspěšně uložen.',
    'confirm_delete' => 'Opravdu chcete smazat tento formulář?',
    'tab_new_form' => 'Nový formulář',
    'property_label_title' => 'Popisek',
    'property_label_required' => 'Zadejte prosím popisek pole.',
    'property_span_title' => 'Zarovnání',
    'property_comment_title' => 'Komentář',
    'property_comment_above_title' => 'Komentář nad',
    'property_default_title' => 'Výchozí',
    'property_checked_default_title' => 'Ve výchozím stavu zaškrtnuto',
    'property_css_class_title' => 'CSS třída',
    'property_css_class_description' => 'Volitelná CSS třída která se přiřadí ke kontejneru pole.',
    'property_disabled_title' => 'Neaktivní',
    'property_hidden_title' => 'Skrytý',
    'property_required_title' => 'Povinný',
    'property_field_name_title' => 'Název pole',
    'property_placeholder_title' => 'Zástupný text',
    'property_default_from_title' => 'Default from',
    'property_stretch_title' => 'Stretch',
    'property_stretch_description' => 'Definuje jestli se toto pole zmenší tak, aby se vešlo to rodičovského prvku na výšku.',
    'property_context_title' => 'Kontext',
    'property_context_description' => 'Definuje jaký kontext bude zobrazen při zobrazení tohoto pole.',
    'property_context_create' => 'Vytvořit',
    'property_context_update' => 'Upravit',
    'property_context_preview' => 'Náhled',
    'property_dependson_title' => 'Závisí na',
    'property_trigger_action' => 'Akce',
    'property_trigger_show' => 'Zobrazit',
    'property_trigger_hide' => 'Schovat',
    'property_trigger_enable' => 'Aktivní',
    'property_trigger_disable' => 'Neaktivní',
    'property_trigger_empty' => 'Prázdný',
    'property_trigger_field' => 'Pole',
    'property_trigger_field_description' => 'Defines the other field name that will trigger the action.',
    'property_trigger_condition' => 'Podmínka',
    'property_trigger_condition_description' => 'Determines the condition the specified field should satisfy for the condition to be considered "true". Supported values: checked, unchecked, value[somevalue].',
    'property_trigger_condition_checked' => 'Zaškrtnuté',
    'property_trigger_condition_unchecked' => 'Nezaškrtnuté',
    'property_trigger_condition_somevalue' => 'value[enter-the-value-here]',
    'property_preset_title' => 'Preset',
    'property_preset_description' => 'Allows the field value to be initially set by the value of another field, converted using the input preset converter.',
    'property_preset_field' => 'Pole',
    'property_preset_field_description' => 'Defines the other field name to source the value from.',
    'property_preset_type' => 'Typ',
    'property_preset_type_description' => 'Specifies the conversion type',
    'property_attributes_title' => 'Atributy',
    'property_attributes_description' => 'Custom HTML attributes to add to the form field element.',
    'property_container_attributes_title' => 'Kontejnér atributů',
    'property_container_attributes_description' => 'Custom HTML attributes to add to the form field container element.',
    'property_group_advanced' => 'Pokročilé',
    'property_dependson_description' => 'A list of other field names this field depends on, when the other fields are modified, this field will update. One field per line.',
    'property_trigger_title' => 'Trigger',
    'property_trigger_description' => 'Allows to change elements attributes such as visibility or value, based on another elements\' state.',
    'property_default_from_description' => 'Takes the default value from the value of another field.',
    'property_field_name_required' => 'Název pole je povinný',
    'property_field_name_regex' => 'Název pole může obsahovat pouze písmena, číslice, podtržítka, pomlčky a hranaté závorky.',
    'property_attributes_size' => 'Velikost',
    'property_attributes_size_tiny' => 'Nejmenší',
    'property_attributes_size_small' => 'Malý',
    'property_attributes_size_large' => 'Normální',
    'property_attributes_size_huge' => 'Veliký',
    'property_attributes_size_giant' => 'Obrovský',
    'property_comment_position' => 'Zobrazit komentář',
    'property_comment_position_above' => 'Nad prvkem',
    'property_comment_position_below' => 'Pod prvkem',
    'property_hint_path' => 'Hint partial path',
    'property_hint_path_description' => 'Path to a partial file that contains the hint text. Use the $ symbol to refer the plugins root directory, for example: $/acme/blog/partials/_hint.htm',
    'property_hint_path_required' => 'Please enter the hint partial path',
    'property_partial_path' => 'Cesta k dílčímu souboru',
    'property_partial_path_description' => 'Path to a partial file. Use the $ symbol to refer the plugins root directory, for example: $/acme/blog/partials/_partial.htm',
    'property_partial_path_required' => 'Prosím zadejte cestu k dílčímu souboru',
    'property_code_language' => 'Jazyk',
    'property_code_theme' => 'Téma',
    'property_theme_use_default' => 'Použít výchozí téma',
    'property_group_code_editor' => 'Editor kódu',
    'property_gutter' => 'Výplň',
    'property_gutter_show' => 'Viditelný',
    'property_gutter_hide' => 'Skrytý',
    'property_wordwrap' => 'Word wrap',
    'property_wordwrap_wrap' => 'Wrap',
    'property_wordwrap_nowrap' => 'Don\'t wrap',
    'property_fontsize' => 'Velikost písma',
    'property_codefolding' => 'Code folding',
    'property_codefolding_manual' => 'Manual',
    'property_codefolding_markbegin' => 'Mark begin',
    'property_codefolding_markbeginend' => 'Mark begin and end',
    'property_autoclosing' => 'Automatické zavírání',
    'property_enabled' => 'Aktivní',
    'property_disabled' => 'Neaktivní',
    'property_soft_tabs' => 'Soft tabs',
    'property_tab_size' => 'Velikost záložky',
    'property_readonly' => 'Pouze pro čtení',
    'property_use_default' => 'Use default settings',
    'property_options' => 'Volby',
    'property_prompt' => 'Prompt',
    'property_prompt_description' => 'Text to display for the create button.',
    'property_prompt_default' => 'Přidat nový prvek',
    'property_available_colors' => 'Dostupné barvy',
    'property_available_colors_description' => 'List of available colors in hex format (#FF0000). Leave empty for the default color set. Enter one value per line.',
    'property_datepicker_mode' => 'Mód',
    'property_datepicker_mode_date' => 'Datum',
    'property_datepicker_mode_datetime' => 'Datum a čas',
    'property_datepicker_mode_time' => 'Čas',
    'property_datepicker_min_date' => 'Min datum',
    'property_datepicker_max_date' => 'Max datum',
    'property_fileupload_mode' => 'Mód',
    'property_fileupload_mode_file' => 'Soubor',
    'property_fileupload_mode_image' => 'Obrázek',
    'property_group_fileupload' => 'Nahrávání obrázků',
    'property_fileupload_image_width' => 'Šířka obrázku',
    'property_fileupload_image_width_description' => 'Optional parameter - images will be resized to this width. Applies to Image mode only.',
    'property_fileupload_invalid_dimension' => 'Invalid dimension value - please enter a number.',
    'property_fileupload_image_height' => 'Výška obrázku',
    'property_fileupload_image_height_description' => 'Optional parameter - images will be resized to this height. Applies to Image mode only.',
    'property_fileupload_file_types' => 'Typy souborů',
    'property_fileupload_file_types_description' => 'Optional comma separated list of file extensions that are accepted by the uploader. Eg: zip,txt',
    'property_fileupload_mime_types' => 'MIME typy',
    'property_fileupload_mime_types_description' => 'Optional comma separated list of MIME types that are accepted by the uploader, either as file extensions or fully qualified names. Eg: bin,txt',
    'property_fileupload_use_caption' => 'Použít popisek',
    'property_fileupload_use_caption_description' => 'Allows a title and description to be set for the file.',
    'property_fileupload_thumb_options' => 'Volby náhledu',
    'property_fileupload_thumb_options_description' => 'Manages options for the automatically generated thumbnails. Applies only for the Image mode.',
    'property_fileupload_thumb_mode' => 'Mód',
    'property_fileupload_thumb_auto' => 'Auto',
    'property_fileupload_thumb_exact' => 'Přesně',
    'property_fileupload_thumb_portrait' => 'Portrét',
    'property_fileupload_thumb_landscape' => 'Krajina',
    'property_fileupload_thumb_crop' => 'Crop',
    'property_fileupload_thumb_extension' => 'Přípona souboru',
    'property_name_from' => 'Name column',
    'property_name_from_description' => 'Relation column name to use for displaying a name.',
    'property_description_from' => 'Description column',
    'property_description_from_description' => 'Relation column name to use for displaying a description.',
    'property_recordfinder_prompt' => 'Prompt',
    'property_recordfinder_prompt_description' => 'Text to display when there is no record selected. The %s character represents the search icon. Leave empty for the default prompt.',
    'property_recordfinder_list' => 'List configuration',
    'property_recordfinder_list_description' => 'A reference to a list column definition file. Use the $ symbol to refer the plugins root directory, for example: $/acme/blog/lists/_list.yaml',
    'property_recordfinder_list_required' => 'Please provide a path to the list YAML file',
    'property_group_recordfinder' => 'Record finder',
    'property_mediafinder_mode' => 'Mód',
    'property_mediafinder_mode_file' => 'Soubor',
    'property_mediafinder_mode_image' => 'Obrázek',
    'property_group_relation' => 'Relace',
    'property_relation_prompt' => 'Prompt',
    'property_relation_prompt_description' => 'Text to display when there is no available selections.',
    'control_group_standard' => 'Standardní',
    'control_group_widgets' => 'Widgets',
    'click_to_add_control' => 'Přidat prvek',
    'loading' => 'Načítám...',
    'control_text' => 'Text',
    'control_text_description' => 'Single line text box',
    'control_password' => 'Heslo',
    'control_password_description' => 'Single line password text field',
    'control_checkbox' => 'Checkbox',
    'control_checkbox_description' => 'Single checkbox',
    'control_switch' => 'Přepínač',
    'control_switch_description' => 'Single switchbox, an alternative for checkbox',
    'control_textarea' => 'Víceřádkové textové pole',
    'control_textarea_description' => 'Multiline text box with controllable height',
    'control_dropdown' => 'Dropdown',
    'control_dropdown_description' => 'Dropdown list with static or dynamic options',
    'control_unknown' => 'Unknown control type: :type',
    'control_repeater' => 'Repeater',
    'control_repeater_description' => 'Outputs a repeating set of form controls',
    'control_number' => 'Číslo',
    'control_number_description' => 'Single line text box that takes numbers only',
    'control_hint' => 'Hint',
    'control_hint_description' => 'Outputs a partial contents in a box that can be hidden by the user',
    'control_partial' => 'Partial',
    'control_partial_description' => 'Outputs a partial contents',
    'control_section' => 'Sekce',
    'control_section_description' => 'Displays a form section with heading and subheading',
    'control_radio' => 'Radio list',
    'control_radio_description' => 'A list of radio options, where only one item can be selected at a time',
    'control_radio_option_1' => 'Volba 1',
    'control_radio_option_2' => 'Volba 2',
    'control_checkboxlist' => 'Checkbox list',
    'control_checkboxlist_description' => 'A list of checkboxes, where multiple items can be selected',
    'control_codeeditor' => 'Editor kódu',
    'control_codeeditor_description' => 'Plaintext editor for formatted code or markup',
    'control_colorpicker' => 'Výběr barvy',
    'control_colorpicker_description' => 'A field for selecting a hexadecimal color value',
    'control_datepicker' => 'Výběr data',
    'control_datepicker_description' => 'Text field used for selecting date and times',
    'control_richeditor' => 'Rich editor',
    'control_richeditor_description' => 'Visual editor for rich formatted text, also known as a WYSIWYG editor',
    'control_markdown' => 'Markdown editor',
    'control_markdown_description' => 'Basic editor for Markdown formatted text',
    'control_fileupload' => 'Nahrávání souborů',
    'control_fileupload_description' => 'File uploader for images or regular files',
    'control_recordfinder' => 'Record finder',
    'control_recordfinder_description' => 'Field with details of a related record with the record search feature',
    'control_mediafinder' => 'Media finder',
    'control_mediafinder_description' => 'Field for selecting an item from the Media Manager library',
    'control_relation' => 'Relace',
    'control_relation_description' => 'Displays either a dropdown or checkbox list for selecting a related record',
    'error_file_name_required' => 'Please enter the form file name.',
    'error_file_name_invalid' => 'The file name can contain only Latin letters, digits, underscores, dots and hashes.',
    'span_left' => 'Doleva',
    'span_right' => 'Doprava',
    'span_full' => 'Plná šířka',
    'span_auto' => 'Automaticky',
    'empty_tab' => 'Prázdná záložka',
    'confirm_close_tab' => 'The tab contains controls which will be deleted. Continue?',
    'tab' => 'Form tab',
    'tab_title' => 'Název',
    'controls' => 'Prvky formuláře',
    'property_tab_title_required' => 'Název záložky je povinný.',
    'tabs_primary' => 'Primární záložka',
    'tabs_secondary' => 'Vedlejší záložka',
    'tab_stretch' => 'Stretch',
    'tab_stretch_description' => 'Specifies if this tabs container stretches to fit the parent height.',
    'tab_css_class' => 'CSS třída',
    'tab_css_class_description' => 'Přiřadí CSS třídu kontejneru záložky.',
    'tab_name_template' => 'Záložka %s',
    'tab_already_exists' => 'Záložka s tímto názvem již existuje.',
  ],
  'list' => [
    'tab_new_list' => 'Nový list',
    'saved' => 'List byl úspěšně uložen.',
    'confirm_delete' => 'Opravdu chcete smazat tento list?',
    'tab_columns' => 'Sloupce',
    'btn_add_column' => 'Přidat sloupec',
    'btn_delete_column' => 'Smazat sloupec',
    'column_dbfield_label' => 'Field',
    'column_dbfield_required' => 'Please enter the model field',
    'column_name_label' => 'Popisek',
    'column_label_required' => 'Zadejte prosím popisek sloupce',
    'column_type_label' => 'Type',
    'column_type_required' => 'Zadejte prosím typ sloupce',
    'column_type_text' => 'Text',
    'column_type_number' => 'Číslo',
    'column_type_switch' => 'Switch',
    'column_type_datetime' => 'Datum a čas',
    'column_type_date' => 'Datum',
    'column_type_time' => 'Čas',
    'column_type_timesince' => 'Čas od',
    'column_type_timetense' => 'Čas do',
    'column_type_select' => 'Select',
    'column_type_partial' => 'Partial',
    'column_label_default' => 'Výchozí',
    'column_label_searchable' => 'Vyhledávání',
    'column_label_sortable' => 'Řazení',
    'column_label_invisible' => 'Neviditelný',
    'column_label_select' => 'Výběr',
    'column_label_relation' => 'Relace',
    'column_label_css_class' => 'CSS class',
    'column_label_width' => 'Šířka',
    'column_label_path' => 'Cesta',
    'column_label_format' => 'Formát',
    'column_label_value_from' => 'Hodnota od',
    'error_duplicate_column' => 'Duplicitní pole sloupce: \':column\'.',
  ],
  'controller' => [
    'menu_label' => 'Kontroléry',
    'no_records' => 'Žádné kontrolery nebyly nalezeny',
    'controller' => 'Kontrolér',
    'behaviors' => 'Chování',
    'new_controller' => 'Nový kontrolér',
    'error_controller_has_no_behaviors' => 'The controller doesn\'t have configurable behaviors.',
    'error_invalid_yaml_configuration' => 'Error loading behavior configuration file: :file',
    'behavior_form_controller' => 'Možnost vytvářet a měnit záznamy',
    'behavior_form_controller_description' => 'Přidá možnost vytvářet a měnit záznamy pomocí formulářů. Toto chování vytvoří tři pohledy pro tvorbu položky, úpravu a náhled.',
    'property_behavior_form_placeholder' => '--vyberte formulář--',
    'property_behavior_form_name' => 'Název',
    'property_behavior_form_name_description' => 'The name of the object being managed by this form',
    'property_behavior_form_name_required' => 'Please enter the form name',
    'property_behavior_form_file' => 'Form configuration',
    'property_behavior_form_file_description' => 'Reference to a form field definition file',
    'property_behavior_form_file_required' => 'Please enter a path to the form configuration file',
    'property_behavior_form_model_class' => 'Modelová třída',
    'property_behavior_form_model_class_description' => 'A model class name, the form data is loaded and saved against this model.',
    'property_behavior_form_model_class_required' => 'Please select a model class',
    'property_behavior_form_default_redirect' => 'Výchozí přesměrování',
    'property_behavior_form_default_redirect_description' => 'A page to redirect to by default when the form is saved or cancelled.',
    'property_behavior_form_create' => 'Create record page',
    'property_behavior_form_redirect' => 'Přesměrování',
    'property_behavior_form_redirect_description' => 'A page to redirect to when a record is created.',
    'property_behavior_form_redirect_close' => 'Close redirect',
    'property_behavior_form_redirect_close_description' => 'A page to redirect to when a record is created and the close post variable is sent with the request.',
    'property_behavior_form_flash_save' => 'Save flash message',
    'property_behavior_form_flash_save_description' => 'Flash message to display when record is saved.',
    'property_behavior_form_page_title' => 'Page title',
    'property_behavior_form_update' => 'Update record page',
    'property_behavior_form_update_redirect' => 'Přesměrování',
    'property_behavior_form_create_redirect_description' => 'A page to redirect to when a record is saved.',
    'property_behavior_form_flash_delete' => 'Delete flash message',
    'property_behavior_form_flash_delete_description' => 'Flash message to display when record is deleted.',
    'property_behavior_form_preview' => 'Preview record page',
    'behavior_list_controller' => 'Možnost listování záznamy',
    'behavior_list_controller_description' => 'Vytvoří tabulku s řazením a vyhledávání s možností definovat odkaz na detail jednotlivého záznamu. Chování automaticky vytvoří akci kontroléru "index".',
    'property_behavior_list_title' => 'List title',
    'property_behavior_list_title_required' => 'Please enter the list title',
    'property_behavior_list_placeholder' => '--select list--',
    'property_behavior_list_model_class' => 'Modelová třída',
    'property_behavior_list_model_class_description' => 'A model class name, the list data is loaded from this model.',
    'property_behavior_form_model_class_placeholder' => '--select model--',
    'property_behavior_list_model_class_required' => 'Please select a model class',
    'property_behavior_list_model_placeholder' => '--select model--',
    'property_behavior_list_file' => 'List configuration file',
    'property_behavior_list_file_description' => 'Reference to a list definition file',
    'property_behavior_list_file_required' => 'Please enter a path to the list configuration file',
    'property_behavior_list_record_url' => 'Record URL',
    'property_behavior_list_record_url_description' => 'Link each list record to another page. Eg: users/update:id. The :id part is replaced with the record identifier.',
    'property_behavior_list_no_records_message' => 'No records message',
    'property_behavior_list_no_records_message_description' => 'A message to display when no records are found',
    'property_behavior_list_recs_per_page' => 'Records per page',
    'property_behavior_list_recs_per_page_description' => 'Records to display per page, use 0 for no pages. Default: 0',
    'property_behavior_list_recs_per_page_regex' => 'Records per page should be an integer value',
    'property_behavior_list_show_setup' => 'Show setup button',
    'property_behavior_list_show_sorting' => 'Show sorting',
    'property_behavior_list_default_sort' => 'Default sorting',
    'property_behavior_form_ds_column' => 'Column',
    'property_behavior_form_ds_direction' => 'Direction',
    'property_behavior_form_ds_asc' => 'Ascending',
    'property_behavior_form_ds_desc' => 'Descending',
    'property_behavior_list_show_checkboxes' => 'Show checkboxes',
    'property_behavior_list_onclick' => 'On click handler',
    'property_behavior_list_onclick_description' => 'Custom JavaScript code to execute when clicking on a record.',
    'property_behavior_list_show_tree' => 'Show tree',
    'property_behavior_list_show_tree_description' => 'Displays a tree hierarchy for parent/child records.',
    'property_behavior_list_tree_expanded' => 'Tree expanded',
    'property_behavior_list_tree_expanded_description' => 'Determines if tree nodes should be expanded by default.',
    'property_behavior_list_toolbar' => 'Toolbar',
    'property_behavior_list_toolbar_buttons' => 'Buttons partial',
    'property_behavior_list_toolbar_buttons_description' => 'Reference to a controller partial file with the toolbar buttons. Eg: list_toolbar',
    'property_behavior_list_search' => 'Search',
    'property_behavior_list_search_prompt' => 'Search prompt',
    'property_behavior_list_filter' => 'Filter configuration',
    'error_controller_not_found' => 'Original controller file is not found.',
    'error_invalid_config_file_name' => 'The behavior :class configuration file name (:file) contains invalid characters and cannot be loaded.',
    'error_file_not_yaml' => 'The behavior :class configuration file (:file) is not a YAML file. Only YAML configuration files are supported.',
    'saved' => 'Kontrolér byl úspěšně uložen.',
    'controller_name' => 'Název kontroléru',
    'controller_name_description' => 'Název kontroléru definuje název třídy a URL kontroléru v administraci. Použijte prosím standardní pojmenování PHP tříd - první symbol je velkým písmenem a zbytek normálně, například: Categories, Posts, Products.',
    'base_model_class' => 'Rodičovská třída',
    'base_model_class_description' => 'Vyberte třídu modelu ze které bude tento kontrolér dědit. Chování kontroléru můžete nastavit později.',
    'base_model_class_placeholder' => '--vyberte model--',
    'controller_behaviors' => 'Chování',
    'controller_behaviors_description' => 'Vyberte chování, které má kontrolér implementovat. Builder automaticky vytvoří požadované soubory.',
    'controller_permissions' => 'Oprávnění',
    'controller_permissions_description' => 'Vyberte uživatelská práva potřebná pro tento kontrolér. Práva můžete nastavit v sekci Oprávnění v levém menu. Toto nastavení můžete později změnit v PHP skriptu.',
    'controller_permissions_no_permissions' => 'Plugin nemá vytvořena žádná oprávnění.',
    'menu_item' => 'Aktivní položka menu',
    'menu_item_description' => 'Vyberte položku menu, která bude aktivní pro tento kontrolér. Toto nastavení můžete kdykoli změnit v PHP skriptu.',
    'menu_item_placeholder' => '--vyberte položku menu--',
    'error_unknown_behavior' => 'Třída chování :class není registrovaná v knihovně všech chování.',
    'error_behavior_view_conflict' => 'The selected behaviors provide conflicting views (:view) and cannot be used together in a controller.',
    'error_behavior_config_conflict' => 'The selected behaviors provide conflicting configuration files (:file) and cannot be used together in a controller.',
    'error_behavior_view_file_not_found' => 'View template :view of the behavior :class cannot be found.',
    'error_behavior_config_file_not_found' => 'Configuration template :file of the behavior :class cannot be found.',
    'error_controller_exists' => 'Controller file already exists: :file.',
    'error_controller_name_invalid' => 'Invalid controller name format. The name can only contain digits and Latin letters. The first symbol should be a capital Latin letter.',
    'error_behavior_view_file_exists' => 'Controller view file already exists: :view.',
    'error_behavior_config_file_exists' => 'Behavior configuration file already exists: :file.',
    'error_save_file' => 'Error saving controller file: :file',
    'error_behavior_requires_base_model' => 'Behavior :behavior requires a base model class to be selected.',
    'error_model_doesnt_have_lists' => 'The selected model doesn\'t have any lists. Please create a list first.',
    'error_model_doesnt_have_forms' => 'The selected model doesn\'t have any forms. Please create a form first.',
  ],
  'version' => [
    'menu_label' => 'Verze',
    'no_records' => 'Žádné verze pluginu',
    'search' => 'Vyhledávání...',
    'tab' => 'Verze',
    'saved' => 'Verze byla úspěšně uložena.',
    'confirm_delete' => 'Opravdu chcete smazat vybranou verzi?',
    'tab_new_version' => 'Nová verze',
    'migration' => 'Migraci',
    'seeder' => 'Seeder',
    'custom' => 'Novou verzi',
    'apply_version' => 'Aplikovat tuto verzi',
    'applying' => 'Aplikování verze...',
    'rollback_version' => 'Vrátit na tuto verzi',
    'rolling_back' => 'Vracení zpět...',
    'applied' => 'Verze byla úspěšně aplikována.',
    'rolled_back' => 'Verze byla úspěšně vrácena zpět.',
    'hint_save_unapplied' => 'Byla uložena neaplikovaná verze. Neaplikované verze mohou být automaticky aplikovány po přihlášení do administrace jakýmkoli uživatelem a nebo pokud je databázová tabulka uložena v sekci Databáze.',
    'hint_rollback' => 'Vracení verze zpět rovněž vrátí zpět všechny novější verze. Neaplikované verze mohou být automaticky aplikovány po přihlášení do administrace jakýmkoli uživatelem a nebo pokud je databázová tabulka uložena v sekci Databáze.',
    'hint_apply' => 'Vracení verze zpět rovněž vrátí zpět všechny starší neaplikované verze.',
    'dont_show_again' => 'Znovu nezobrazovat',
    'save_unapplied_version' => 'Uložit neaplikovanou verzi',
  ],
  'menu' => [
    'menu_label' => 'Menu administrace',
    'tab' => 'Menu',
    'items' => 'Položky menu',
    'saved' => 'Menu byla úspěšně uložena.',
    'add_main_menu_item' => 'Přidat položku menu',
    'new_menu_item' => 'Položka menu',
    'add_side_menu_item' => 'Přidat pod-položku',
    'side_menu_item' => 'Side menu item',
    'property_label' => 'Popisek',
    'property_label_required' => 'Zadejte prosím popisek položky menu.',
    'property_url_required' => 'Zadejte prosím URL položky menu.',
    'property_url' => 'URL',
    'property_icon' => 'Ikona',
    'property_icon_required' => 'Vyberte prosím ikonu',
    'property_permissions' => 'Oprávnění',
    'property_order' => 'Pořadí',
    'property_order_invalid' => 'Zadejte prosím pořadí položky menu jako číslo.',
    'property_order_description' => 'Pořadí položek určuje jejich posloupnost v menu. Pokud není pořadí zadáno, automaticky se umístí položka nakonec. Výchozí hodnoty pořadí jsou násobky 100.',
    'property_attributes' => 'HTML attributy',
    'property_code' => 'Kód',
    'property_code_invalid' => 'Kód položky může obsahovat pouze písmena a číslice',
    'property_code_required' => 'Zadejte prosím kód položky menu.',
    'error_duplicate_main_menu_code' => 'Kód položky hlavního menu je duplicitní: \':code\'.',
    'error_duplicate_side_menu_code' => 'Kód položky postranního menu je duplicitní: \':code\'.',
  ],
  'localization' => [
    'menu_label' => 'Lokalizace',
    'language' => 'Zkratka jazyka',
    'strings' => 'Řetězce',
    'confirm_delete' => 'Opravdu chcete smazat soubor s překladem?',
    'tab_new_language' => 'Nový jazyk',
    'no_records' => 'Žádné jazyky nenalezeny',
    'saved' => 'Soubor s překladem byl úspěšně uložen.',
    'error_cant_load_file' => 'Nepodařilo se načíst požadovaný soubor protože neexistuje.',
    'error_bad_localization_file_contents' => 'Nepodařilo se načíst požadovaný soubor. Soubory s překladem mohou obsahovat pouze pole definující překlady řetězců.',
    'error_file_not_array' => 'Nepodařilo se načíst požadovaný soubor. Překladový soubor musí vrátit pole.',
    'save_error' => 'Chyba ukládání souboru \':name\'. Zkontrolujte prosím práva k zápisu.',
    'error_delete_file' => 'Chyba mazání překladového souboru.',
    'add_missing_strings' => 'Přidat chybějící řetězce',
    'copy' => 'Kopírovat',
    'add_missing_strings_label' => 'Vyberte jazyk ze kterého se zkopírují chybějící řetězce',
    'no_languages_to_copy_from' => 'Nejsou definovány žádné jazyky ze kterých bychom mohli zkopírovat řetězce.',
    'new_string_warning' => 'Nový řetězec nebo sekce',
    'structure_mismatch' => 'Struktura zdrojového jazykového souboru neodpovídá struktuře editovaného souboru. Některé nezávislé řetězce v editovaném souboru odpovídají sekcím ve zdrojovém souboru (nebo naopak) a nemohou být automaticky sloučeny.',
    'create_string' => 'Vytvořit nový řetězec',
    'string_key_label' => 'Klíč řetězce',
    'string_key_comment' => 'Zadejte klíč řetězce s použitím tečky jako oddělovače, například: plugin.search. Řetězec bude vytvořen ve výchozím jazykovém souboru pluginu.',
    'string_value' => 'Hodnota řetězce',
    'string_key_is_empty' => 'Musíte vyplnit klíč řetězce',
    'string_value_is_empty' => 'Musíte vyplnit hodnotu řetězce',
    'string_key_exists' => 'Takový klíč řetězce již existuje',
  ],
  'permission' => [
    'menu_label' => 'Oprávnění',
    'tab' => 'Oprávnění',
    'form_tab_permissions' => 'Oprávnění',
    'btn_add_permission' => 'Přidat oprávnění',
    'btn_delete_permission' => 'Smazat oprávnění',
    'column_permission_label' => 'Kód oprávnění',
    'column_permission_required' => 'Zadejte prosím kód oprávnění',
    'column_tab_label' => 'Název záložky',
    'column_tab_required' => 'Zadejte prosím název záložky oprávnění',
    'column_label_label' => 'Popisek',
    'column_label_required' => 'Zadejte prosím popisek oprávnění',
    'saved' => 'Oprávnění bylo úspěšně uloženo.',
    'error_duplicate_code' => 'Kód oprávnění je duplicitní: \':code\'.',
  ],
  'yaml' => [
    'save_error' => 'Chyba ukládání souboru \':name\'. Zkontrolujte prosím práva k zápisu.',
  ],
  'common' => [
    'error_file_exists' => 'Soubor již existuje: \':path\'.',
    'field_icon_description' => 'October používá ikony Font Autumn: http://octobercms.com/docs/ui/icon',
    'destination_dir_not_exists' => 'Cílový adresář neexistuje: \':path\'.',
    'error_make_dir' => 'Chyba vytváření adresáře: \':name\'.',
    'error_dir_exists' => 'Adresář již existuje: \':path\'.',
    'template_not_found' => 'Soubor šablony nebyl nalezen: \':name\'.',
    'error_generating_file' => 'Chyba vytváření souboru: \':path\'.',
    'error_loading_template' => 'Chyba načtení souboru šablony: \':name\'.',
    'select_plugin_first' => 'Nejdříve vyberte plugin. Pro zobrazení všech pluginů klikněte na symbol > na vrchu levého menu.',
    'plugin_not_selected' => 'Není vybrán žádný plugin',
    'add' => 'Přidat',
  ],
  'migration' => [
    'entity_name' => 'Migrace',
    'error_version_invalid' => 'The version should be specified in format 1.0.1',
    'field_version' => 'Verze',
    'field_description' => 'Popis migrace',
    'field_code' => 'Kód migrace',
    'save_and_apply' => 'Uložit a aplikovat',
    'error_version_exists' => 'Tato verze migrace již existuje.',
    'error_script_filename_invalid' => 'The migration script file name can contain only Latin letters, digits and underscores. The name should start with a Latin letter and could not contain spaces.',
    'error_cannot_change_version_number' => 'Cannot change version number for an applied version.',
    'error_file_must_define_class' => 'Migration code should define a migration or seeder class. Leave the code field blank if you only want to update the version number.',
    'error_file_must_define_namespace' => 'Migration code should define a namespace. Leave the code field blank if you only want to update the version number.',
    'no_changes_to_save' => 'Žádné změny k uložení.',
    'error_namespace_mismatch' => 'Migrační kód by měl používat jmenný prostor pluginu: :namespace',
    'error_migration_file_exists' => 'Migrační soubor :file již existuje. Zadejte prosím jiný název třídy.',
    'error_cant_delete_applied' => 'This version has already been applied and cannot be deleted. Please rollback the version first.',
  ],
  'components' => [
    'list_title' => 'Record list',
    'list_description' => 'Displays a list of records for a selected model',
    'list_page_number' => 'Číslo stránky',
    'list_page_number_description' => 'This value is used to determine what page the user is on.',
    'list_records_per_page' => 'Records per page',
    'list_records_per_page_description' => 'Number of records to display on a single page. Leave empty to disable pagination.',
    'list_records_per_page_validation' => 'Invalid format of the records per page value. The value should be a number.',
    'list_no_records' => 'No records message',
    'list_no_records_description' => 'Message to display in the list in case if there are no records. Used in the default component\'s partial.',
    'list_no_records_default' => 'Žádné záznamy nebyly nalezeny',
    'list_sort_column' => 'Sort by column',
    'list_sort_column_description' => 'Model column the records should be ordered by',
    'list_sort_direction' => 'Směr',
    'list_display_column' => 'Display column',
    'list_display_column_description' => 'Column to display in the list. Used in the default component\'s partial.',
    'list_display_column_required' => 'Please select a display column.',
    'list_details_page' => 'Details page',
    'list_details_page_description' => 'Page to display record details.',
    'list_details_page_no' => '--no details page--',
    'list_sorting' => 'Řazení',
    'list_pagination' => 'Stránkování',
    'list_order_direction_asc' => 'Vzestupně',
    'list_order_direction_desc' => 'Sestupně',
    'list_model' => 'Modelová třída',
    'list_scope' => 'Scope',
    'list_scope_description' => 'Optional model scope to fetch the records',
    'list_scope_default' => '--select a scope, optional--',
    'list_details_page_link' => 'Link to the details page',
    'list_details_key_column' => 'Details key column',
    'list_details_key_column_description' => 'Model column to use as a record identifier in the details page links.',
    'list_details_url_parameter' => 'URL parameter name',
    'list_details_url_parameter_description' => 'Name of the details page URL parameter which takes the record identifier.',
    'details_title' => 'Record details',
    'details_description' => 'Displays record details for a selected model',
    'details_model' => 'Modelová třída',
    'details_identifier_value' => 'Identifier value',
    'details_identifier_value_description' => 'Identifier value to load the record from the database. Specify a fixed value or URL parameter name.',
    'details_identifier_value_required' => 'The identifier value is required',
    'details_key_column' => 'Key column',
    'details_key_column_description' => 'Model column to use as a record identifier for fetching the record from the database.',
    'details_key_column_required' => 'The key column name is required',
    'details_display_column' => 'Display column',
    'details_display_column_description' => 'Model column to display on the details page. Used in the default component\'s partial.',
    'details_display_column_required' => 'Please select a display column.',
    'details_not_found_message' => 'Not found message',
    'details_not_found_message_description' => 'Message to display if the record is not found. Used in the default component\'s partial.',
    'details_not_found_message_default' => 'Záznam nebyl nalezen',
  ],
];
