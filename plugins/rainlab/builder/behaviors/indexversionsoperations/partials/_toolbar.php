<div class="form-buttons loading-indicator-container">
    <a
        href="javascript:;"
        class="btn btn-primary oc-icon-check save"
        data-builder-command="version:cmdSaveVersion"
        data-load-indicator="<?= e(trans('backend::lang.form.saving')) ?>"
        data-hotkey="ctrl+s, cmd+s">
        <?= e(trans('backend::lang.form.save')) ?>
    </a>

    <a
        href="javascript:;"
        class="btn btn-primary oc-icon-bolt <?php if ($model->isApplied()): ?>hide<?php endif ?>"
        data-builder-command="version:cmdApplyVersion"
        data-load-indicator="<?= e(trans('rainlab.builder::lang.version.applying')) ?>">
        <?= e(trans('rainlab.builder::lang.version.apply_version')) ?>
    </a>

    <a
        href="javascript:;"
        class="btn btn-primary oc-icon-undo <?php if (!$model->isApplied()): ?>hide<?php endif ?>"
        data-builder-command="version:cmdRollbackVersion"
        data-load-indicator="<?= e(trans('rainlab.builder::lang.version.rolling_back')) ?>">
        <?= e(trans('rainlab.builder::lang.version.rollback_version')) ?>
    </a>
    
    <button
        type="button"
        class="btn btn-default empty oc-icon-trash-o <?php if (!strlen($originalVersion)): ?>hide<?php endif ?>"
        data-builder-command="version:cmdDeleteVersion"
        data-confirm="<?= e(trans('rainlab.builder::lang.version.confirm_delete')) ?>"
        data-control="delete-button"></button>
</div>