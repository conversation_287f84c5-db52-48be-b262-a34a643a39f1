.builder-controllers-builder-area {
    background: var(--bs-body-bg, white);

    ul.controller-behavior-list {
        .clearfix();

        padding: 20px;
        margin-bottom: 0;
        list-style: none;

        li {
            h4 {
                text-align: center;
                border-bottom: 1px dotted @builder-control-border-color;
                margin: 0 -20px 40px;

                span {
                    display: inline-block;
                    background: @builder-control-tooltip-color;
                    color: white;
                    margin: 0 auto;
                    border-radius: 8px;
                    padding: 7px 10px;
                    font-size: 13px;
                    line-height: 100%;
                    position: relative;
                    top: 14px;
                }
            }
        }

        .behavior-container {
            margin-bottom: 40px;
            .clearfix();
            cursor: pointer;

            .list-behavior, .import-export-behavior {
                border-radius: 4px;
                border: 2px solid @builder-control-border-color;
                padding: 25px 10px 25px 10px;

                table {
                    border-collapse: collapse;
                    width: 100%;

                    td {
                        padding: 0 15px 15px 15px;

                        border-right: 1px solid @builder-control-border-color;

                        &:last-child {
                            border-right: none;
                        }
                    }

                    .oc-placeholder {
                        background: var(--oc-secondary-bg, #EEF2F4);
                        height: 25px;
                    }

                    tbody tr:last-child td {
                        padding-bottom: 0;
                    }
                }
            }

            .import-export-behavior {
                table {
                    i.icon-bars, .oc-placeholder {
                        float: left;
                    }

                    i.icon-bars {
                        margin-right: 15px;
                        color: #D6DDE0;
                        font-size: 28px;
                        line-height: 28px;
                        position: relative;
                        top: -2px;
                    }
                }
            }

            .form-behavior {
                div.form {
                    .clearfix();

                    padding: 25px 25px 0 25px;
                    border: 2px solid @builder-control-border-color;
                    margin-bottom: 20px;
                    .border-radius(4px);
                }

                div.field {
                    &.left {
                        float: left;
                        width: 48%;
                    }

                    &.right {
                        float: right;
                        width: 45%;
                    }

                    div.label {
                        background: var(--oc-secondary-bg, #EEF2F4);
                        height: 25px;
                        margin-bottom: 10px;

                        &.size-3 {
                            width: 100px;
                        }

                        &.size-5 {
                            width: 150px;
                        }

                        &.size-2 {
                            width: 60px;
                        }
                    }

                    div.control {
                        background: var(--oc-secondary-bg, #EEF2F4);
                        height: 35px;
                        margin-bottom: 25px;
                    }
                }

                div.button {
                    background: var(--oc-secondary-bg, #EEF2F4);
                    height: 35px;
                    margin-right: 20px;
                    .border-radius(4px);

                    &.size-5 {
                        width: 100px;
                    }

                    &.size-3 {
                        width: 60px;
                    }

                    &:first-child {
                        margin-right: 0;
                    }
                }
            }

            &:hover, &.inspector-open {
                * {
                    border-color: @builder-hover-color!important;
                }
            }
        }
    }
}

// Fix for the Mac firefox

html.gecko.mac {
    .builder-controllers-builder-area {
        ul.controller-behavior-list {
            padding-right: 40px;
        }
    }
}