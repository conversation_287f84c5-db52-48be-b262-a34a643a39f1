## this.theme.* ##

tabs:
    fields:
        navbar:
            tab: Navbar
            type: nestedform
            showPanel: false
            permissions: developer
            form: ~/themes/branche-basis/config/nestedform/navbar.yaml
        footer:
            tab: Footer
            type: nestedform
            showPanel: false
            permissions: developer
            form: ~/themes/branche-basis/config/nestedform/footer.yaml

        _section_design:
            label: <PERSON><PERSON><PERSON> kleur
            comment: '<PERSON><PERSON>urenpalet via <strong><a href="https://tailwindcss.com/docs/customizing-colors#default-color-palette" target="_blank">Tailwind</a></strong> of <strong><a href="https://uicolors.app/create" target="_blank">zelf maken</a></strong>'
            commentHtml: true
            type: section
            tab: Design

        primary_default:
            label: 'default'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        primary_50:
            label: '50'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        primary_100:
            label: '100'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        primary_200:
            label: '200'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        primary_300:
            label: '300'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        primary_400:
            label: '400'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        primary_500:
            label: '500'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        primary_600:
            label: '600'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        primary_700:
            label: '700'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        primary_800:
            label: '800'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        primary_900:
            label: '900'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        primary_950:
            label: '950'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1

        _ruler_color1:
            type: ruler
            tab: Design

        _section_secondary:
            label: Secundaire kleur
            comment: 'Kleurenpalet via <strong><a href="https://tailwindcss.com/docs/customizing-colors#default-color-palette" target="_blank">Tailwind</a></strong> of <strong><a href="https://uicolors.app/create" target="_blank">zelf maken</a></strong>'
            commentHtml: true
            type: section
            tab: Design

        secondary_default:
            label: 'default'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        secondary_50:
            label: '50'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        secondary_100:
            label: '100'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        secondary_200:
            label: '200'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        secondary_300:
            label: '300'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        secondary_400:
            label: '400'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        secondary_500:
            label: '500'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        secondary_600:
            label: '600'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        secondary_700:
            label: '700'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        secondary_800:
            label: '800'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        secondary_900:
            label: '900'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1
        secondary_950:
            label: '950'
            tab: Design
            span: row
            spanClass: col-12 col-sm-4 col-md-3 col-lg-1

        pricing:
            tab: "Prijzen calculator"
            type: nestedform
            span: left
            showPanel: false
            form: ~/themes/branche-basis/config/nestedform/pricing-card.yaml

        addons:
            tab: "Prijzen calculator"
            type: nestedform
            span: right
            showPanel: false
            form: ~/themes/branche-basis/config/nestedform/pricing-addons.yaml
