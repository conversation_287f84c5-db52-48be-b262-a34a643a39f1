{% if box.text_align == 'center' %}
    {% set text_align = 'text-center' %}
{% elseif box.text_align == 'right' %}
    {% set text_align = 'text-right' %}
{% else %}
    {% set text_align = 'text-left' %}
{% endif %}

<div class="{{ text_align }} {{ box.text_color == 'dark' ? 'light' : 'dark' }} overflow-hidden relative {{ box.rounded_corners ? 'rounded-md' }} {{ box.shadow ? 'shadow-lg' }}" {% if box.corner %}style="border: {{ box.corner_width }}px solid {{ box.corner_color }};"{% endif %}>
    <div
        class="absolute inset-0"
        style="background-color: {{ box.background_color|default('#ffffff') }}; {% if box.bg_opacity != '100' %}opacity: .{{ box.bg_opacity }};{% endif %}"
    ></div>

    <div class="relative">
        <div class="flex justify-center {{ box.isIcon ? 'pt-6' }}">
            {% if box.img_link %}
                <a href="{{ box.img_link | link }}" class="bx-da">
            {% endif %}
            {% if box.isIcon %}
                <div class="px-6"><img src="{{ box.img | media }}" alt="{{ box.image.title }}"></div>
            {% else %}
                <div class="aspect-thumb">
                    <img src="{{ box.img | media | resize(768, null) }}" alt="{{ box.img.title }}" class="w-full h-full object-cover">
                </div>
            {% endif %}
            {% if box.img_link %}
                </a>
            {% endif %}
        </div>
        <div class="p-6">
            <div class="content_section">
                <h3>{{ box.title }}</h3>
                {{ box.text | content }}
            </div>
            {% if box.buttons %}
                <div class="">
                    {% for button in box.buttons %}
                        {% partial "ui/button" item = button %}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
