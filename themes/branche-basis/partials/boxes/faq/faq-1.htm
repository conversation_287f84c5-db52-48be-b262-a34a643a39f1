<div id="{{ box.anchorpoint }}" class="{{ box.css_class }}" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="container space-y-8 lg:space-y-12">
        <div class="space-y-2">
            {% if box.subtitle %}
            <div class="text-center text-primary-700">
                <h4>{{ box.subtitle }}</h4>
            </div>
            {% endif %}
            {% if box.title %}
            <div class="text-center">
                <h2 class="text-gray-800 ">{{ box.title }}</h2>
            </div>
            {% endif %}
            {% if box.content %}
            <div class="content_section text-gray-500 text-center md:w-2/3 md:mx-auto pt-4">
                {{ box.content|content }}
            </div>
            {% endif %}
        </div>

        <div class="max-w-4xl mx-auto">
            <div class="jobs-list divide-y divide-gray-300">
                {% for item in box.faq %}
                <div x-data="{ open: false }" class="px-10 {{ loop.first ? 'pt-0 pb-8' : 'pb-8 pt-8' }}">
                    <div>
                        <button class="flex items-center text-xl" @click="open = !(open)">
                            <span class="leading-none mr-6">
                                <i class="fa-solid fa-minus font-medium text-xl text-gray-800" x-show="open"></i>
                                <i class="fa-solid fa-plus font-medium text-xl text-gray-800" x-show="!(open)"></i>
                            </span>
                            <span class="text-primary-800 text-xl font-bold">{{ item.title }}</span>
                        </button>
                    </div>
                    <div class="p-4 text-gray-400"
                        x-show="open"
                        x-transition:enter="transition ease-out duration-500"
                        x-transition:leave="transition ease-in duration-500" x-collapse x-cloak>
                        <div class="content_section_base text-gray-600">{{ item.content | content }}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

    </div>
</div>
