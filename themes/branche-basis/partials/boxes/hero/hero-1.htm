<div class="relative {{ box.css_class }}" style="background-color: {{ box.background_color }};">
    {% if box.background_img %}
        <div class="absolute inset-0 overflow-hidden">
            <img src="{{ box.background_img | media | resize(1920, auto, { 'extension': 'webp' }) }}" alt="{{ box.background_img_title }}" class="h-full w-full object-cover">
        </div>
    {% endif %}

    <div class="relative container" data-boxes-container data-rel="boxes-wrapper">
        <div class="relative flex flex-col items-center space-y-6 lg:space-y-12">
            <div class="pt-32 lg:pt-44 text-center">
                <h1 class="text-gray-800 ">{{ box.title }} <span class="text-primary">{{ box.branche }}</span></h1>
            </div>
            <div class="content_section_hero max-w-xl text-center text-gray-500">
                {{ box.content | content }}
            </div>
            {% if box.buttons %}
                <div class="flex flex-col justify-center space-y-4 md:space-y-0 md:flex-row md:space-x-4 ">
                    {% for item in box.buttons %}
                        {% partial "ui/button" item = item %}
                    {% endfor %}
                </div>
            {% endif %}
            <div class="relative">
                <img src="{{ box.img | media | resize(1200, auto, { 'extension': 'webp' }) }}" alt="{{ box.img_title }}">
            </div>
        </div>
    </div>
</div>
