handle: support-header
name: Support header
section: Hero's
icon: /plugins/offline/boxes/assets/img/boxes/image.svg

spacing:
    - general

form:
    tabs:
        icons:
            Algemeen: icon-header
            Instellingen: icon-cog
            Buttons: icon-external-link-square
        fields:
            title:
                label: Titel
                tab: Algemeen
            content:
                label: Content
                type: richeditor
                tab: Algemeen
            cards:
                label: Support kaarten
                prompt: Voeg een Kaart toe
                type: repeater
                maxItems: 4
                itemsExpanded: false
                tab: Algemeen
                titleFrom: title
                useTabs: true
                form:
                    fields:
                        title:
                            label: Titel
                            tab: Algemeen
                        content:
                            label: Content
                            type: richeditor
                            tab: Algemeen
                        link_type:
                            type: mixin
                            tab: Actieknop
            background_color:
                type: mixin
                tab: Design
            background_img:
                label: Achtergrond Afbeelding
                type: mediafinder
                tab: Design
            img_title:
                label: Afbeelding titel (Alt-tag)
                tab: Design
            css_class:
                label: CSS class
                tab: Design
