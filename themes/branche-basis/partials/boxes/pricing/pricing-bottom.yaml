handle: pricing-bottom
name: Pri<PERSON>zen - bottom
section: Prijzen
icon: /plugins/offline/boxes/assets/img/boxes/image.svg

spacing:
- general

form:
    tabs:
        fields:
            mixin-title-subtitle:
                type: mixin
                tab: Algemeen
            columns:
                label: Kolommen titels
                type: repeater
                prompt: Kolom toevoegen
                itemsExpanded: false
                tab: Algemeen
                form:
                    fields:
                        title:
                            label: Titel
                        subtitle:
                            label: Onderschrift
            prices:
                label: Groepen
                type: repeater
                prompt: Groep toevoegen
                itemsExpanded: false
                tab: Algemeen
                form:
                    fields:
                        title:
                            label: Titel
                        includes:
                            label: Regels
                            type: repeater
                            prompt: Regel toevoegen
                            itemsExpanded: false
                            form:
                                fields:
                                    label:
                                        label: Label
                                    _ruler:
                                        type: ruler
                                    value_type_left:
                                        label: Waarde type links
                                        type: balloon-selector
                                        default: 'check'
                                        options:
                                            'empty': 'Leeg'
                                            'check': 'Checkbox'
                                            'text': 'Tekst'
                                    included_left:
                                        label: Inclusief
                                        type: checkbox
                                        trigger:
                                            field: value_type_left
                                            action: show
                                            condition: value[check]
                                    value_left:
                                        label: Tekst waarde links
                                        trigger:
                                            field: value_type_left
                                            action: show
                                            condition: value[text]
                                    # _ruler_2:
                                    #     type: ruler
                                    # value_type_middle:
                                    #     label: Waarde type middel
                                    #     type: balloon-selector
                                    #     default: 'check'
                                    #     options:
                                    #         'empty': 'Leeg'
                                    #         'check': 'Checkbox'
                                    #         'text': 'Tekst'
                                    # included_middle:
                                    #     label: Inclusief
                                    #     type: checkbox
                                    #     trigger:
                                    #         field: value_type_middle
                                    #         action: show
                                    #         condition: value[check]
                                    # value_middle:
                                    #     label: Tekst waarde middel
                                    #     trigger:
                                    #         field: value_type_middle
                                    #         action: show
                                    #         condition: value[text]
                                    # _ruler_3:
                                    #     type: ruler
                                    # value_type_right:
                                    #     label: Waarde type rechts
                                    #     type: balloon-selector
                                    #     default: 'check'
                                    #     options:
                                    #         'empty': 'Leeg'
                                    #         'check': 'Checkbox'
                                    #         'text': 'Tekst'
                                    # included_right:
                                    #     label: Inclusief
                                    #     type: checkbox
                                    #     trigger:
                                    #         field: value_type_right
                                    #         action: show
                                    #         condition: value[check]
                                    # value_right:
                                    #     label: Tekst waarde rechts
                                    #     trigger:
                                    #         field: value_type_right
                                    #         action: show
                                    #         condition: value[text]
            anchor:
                type: mixin
                tab: Design
            custom_css:
                type: mixin
                tab: Design
