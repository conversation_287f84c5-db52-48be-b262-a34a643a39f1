<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="content-cards"
    data-category="content"
    class="content relative {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    {% if box.background_img %}
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <img src="{{ box.background_img | media | resize(1920, auto, { 'extension': 'webp' }) }}" alt="{{ box.background_img_title }}" class="h-full w-full object-cover">
        </div>
    {% endif %}

    <div class="container relative z-20">

        <div class="">

            <div class="space-y-8">

                <div class="max-w-5xl mx-auto space-y-8">
                    <div>
                    {% if box.subtitle %}
                    <div class="text-center text-primary">
                        <h4>{{ box.subtitle }}</h4>
                    </div>
                    {% endif %}
                    <div class="text-center">
                        {% if box.title_heading == 'h1' %}
                            <h1 class="text-gray-800 ">{{ box.title }}</h1>
                        {% elseif box.title_heading == 'h3' %}
                            <h3 class="text-gray-800 ">{{ box.title }}</h3>
                        {% else %}
                            <h2 class="text-gray-800 ">{{ box.title }}</h2>
                        {% endif %}
                    </div>
                </div>

                <div class="content_section text-gray-500 md:w-2/3 md:mx-auto lg:w-full lg:mx-0 text-center">
                    {{ box.content|content }}
                </div>

                {% if box.buttons %}
                    <div class="flex flex-wrap space-x-8 justify-center lg:justify-start pt-6">
                        {% for item in box.buttons %}
                        {% partial "ui/button" item = item %}
                        {% endfor %}
                    </div>
                {% endif %}
                </div>

            </div>

            <div class="max-w-5xl mx-auto mt-10 lg:mt-16">

                <div class="space-y-4 md:space-y-0 md:grid md:grid-cols-2 md:gap-8">
                    {% for card in box.cards %}
                        <div class="p-4 md:p-6 rounded-xl shadow-md bg-white relative group hover:shadow-xl transition-all border hover:border-transparent">
                            {% if card.link %}
                                <a href="{{ card.link | link }}" class="absolute inset-0 z-10"></a>
                                <div class="absolute inset-0 group-hover:-inset-1 -z-10 bg-gradient-to-b from-primary to-primary/50 rounded-2xl transition-all"></div>
                            {% endif %}
                            <div class="relative text-center">
                                <div class="w-10 h-10 mx-auto flex items-center justify-center">
                                    <i class="fa-solid fa-{{ card.icon }} text-primary text-3xl group-hover:scale-125 transition-all"></i>
                                </div>
                                <div class="text-xl font-bold mt-4 mb-2">{{ card.title }}</div>
                                <p class="text-sm text-gray-500">{{ card.text }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>

            </div>

        </div>

    </div>
</section>
