<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="content-cards"
    data-category="content"
    class="content {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    <div class="container">

        <div class="flex flex-col lg:gap-20 lg:grid lg:grid-cols-12">

            <div class="md:col-span-6 space-y-8 p-8 lg:py-12 lg:px-0 {{ box.flip_content ? 'md:order-last' }}">

                <div>
                    {% if box.subtitle %}
                    <div class="text-center lg:text-left text-primary">
                        <h4>{{ box.subtitle }}</h4>
                    </div>
                    {% endif %}
                    <div class="text-center lg:text-left">
                        {% if box.title_heading == 'h1' %}
                            <h1 class="text-gray-800 ">{{ box.title }}</h1>
                        {% elseif box.title_heading == 'h3' %}
                            <h3 class="text-gray-800 ">{{ box.title }}</h3>
                        {% else %}
                            <h2 class="text-gray-800 ">{{ box.title }}</h2>
                        {% endif %}
                    </div>
                </div>

                <div class="content_section text-gray-500 md:w-2/3 md:mx-auto lg:w-full lg:mx-0">
                    {{ box.content|content }}
                </div>

                {% if box.benefits %}
                    <div class="flex flex-col space-y-3 md:w-2/3 md:mx-auto lg:w-full lg:mx-0">
                        {% for benefit in box.benefits %}
                        <div class="flex items-center">
                            <i class="fa-regular fa-angle-right text-primary-700 pr-4 text-xl"></i>
                            <div class="text-xl text-gray-500">
                                {% if benefit.link %}<a href="{{ benefit.link | link}}" class="hover:text-primary-600">{% endif %}
                                    {{ benefit.title | content }}
                                {% if benefit.link %}</a>{% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% endif %}

                {% if box.buttons %}
                    <div class="flex flex-col justify-center items-center text-center space-y-4 pt-6 md:space-y-0 md:flex-row md:space-x-4 lg:space-x-0 lg:flex-col lg:items-start lg:space-y-4 xl:space-y-0 xl:flex-row xl:justify-start xl:space-x-8">
                        {% for item in box.buttons %}
                        {% partial "ui/button" item = item %}
                        {% endfor %}
                    </div>
                {% endif %}

            </div>

            <div class="md:col-span-6 space-y-8 w-full content-center {{ box.flip_content ? 'md:order-first' }}">

                <div class="space-y-4 md:space-y-0 md:grid md:grid-cols-2 md:gap-8">
                    {% for card in box.cards %}
                        <div class="p-4 md:p-6 rounded-xl shadow-md bg-white relative group hover:shadow-xl transition-all border hover:border-transparent">
                            {% if card.link %}
                                <a href="{{ card.link | link }}" class="absolute inset-0 z-10"></a>
                                <div class="absolute inset-0 group-hover:-inset-1 -z-10 bg-gradient-to-b from-primary to-primary/50 rounded-2xl transition-all"></div>
                            {% endif %}
                            <div class="relative text-center">
                                <div class="w-10 h-10 mx-auto flex items-center justify-center">
                                    <i class="fa-solid fa-{{ card.icon }} text-primary text-3xl group-hover:scale-125 transition-all"></i>
                                </div>
                                <div class="text-xl font-bold mt-4 mb-2">{{ card.title }}</div>
                                <p class="text-sm text-gray-500">{{ card.text }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>

            </div>

        </div>

    </div>
</section>
