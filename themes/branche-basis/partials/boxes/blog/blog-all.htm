[categoryList]

[siteSearchInclude]
==
{% set items = postList.posts %}
{% set blogPage = postList.blogPage|link %}
{% set postPage = postList.postPage %}
{% set categoryPage = postList.categoryPage %}

<section class="relative {{ box.css_class }}" style="background-color: {{ box.background_color }};">
    <div class="absolute inset-0 overflow-hidden">
        <img src="{{ box.background_img | media | resize(1920, auto, { 'extension': 'webp' }) }}" alt="{{ box.img_title }}" class="h-full w-full object-cover">
    </div>

    <div class="relative container" data-boxes-container data-rel="boxes-wrapper">
        <div class="space-y-2">
            {% if box.subtitle %}
            <div class="text-center text-primary-700 pt-32 lg:pt-44">
                <h4>{{ box.subtitle }}</h4>
            </div>
            {% endif %}
            {% if box.title %}
            <div class="text-center">
                <h2 class="text-gray-800 ">{{ box.title }}</h2>
            </div>
            {% endif %}
            {% if box.content %}
            <div class="content_section text-gray-500 text-center md:w-2/3 md:mx-auto pt-4">
                {{ box.content|content }}
            </div>
            {% endif %}
        </div>
        <div class="mt-12 max-w-lg mx-auto grid gap-6 lg:gap-12 lg:grid-cols-2 xl:grid-cols-3 lg:max-w-none">
            {% for post in items %}
            <div class="flex flex-col rounded-2xl shadow-lg hover:shadow-primary-500/30 overflow-hidden">
                <div class="flex-shrink-0">
                    <a href="{{ postPage | page({ slug: post.slug }) }}">
                        {% if post.thumbnail_image %}
                        <img class="h-48 w-full object-cover" src="{{ post.thumbnail_image | media | resize(600) }}" alt="{{ post.title }}">
                        {% else %}
                        <img class="h-48 w-full object-cover" src="{{ post.image | media | resize(600) }}" alt="{{ post.title }}">
                        {% endif %}
                    </a>
                </div>
                <div class="flex-1 bg-white p-6 flex flex-col justify-between">
                    <div class="flex-1">
                        <div class="text-sm font-medium text-primary">
                            {% for category in post.categories %}
                            <a href="{{ categoryPage | page({ slug: category.slug }) }}" class="inline-block py-0.5 px-3 rounded-full bg-gradient-to-r from-primary-800 to-primary-800/60 text-white font-bold text-xs">{{ category.title }}</a>{% if not loop.last %}, {% endif %}
                            {% endfor %}
                        </div>
                        <a href="{{ postPage | page({ slug: post.slug }) }}" class="block content_section_base mt-2">
                            <h4 class="text-gray-800">
                                {{ post.title_short }}
                            </h4>
                            <p class="mt-3 text-gray-500 line-clamp-4 lg:line-clamp-6">
                                {{ post.excerpt }}
                            </p>
                        </a>
                    </div>
                    <div class="mt-6 flex items-center">
                        {% if postList.showAuthor and post.author.avatar %}
                        <div class="flex-shrink-0">
                            <a href="#">
                                <span class="sr-only">{{ post.author.first_name }} {{ post.author.last_name }}</span>
                                <img class="h-10 w-10 rounded-full" src="{{ post.author.avatar | media | resize(150) }}" alt="{{ post.author.first_name }} {{ post.author.last_name }}">
                            </a>
                        </div>
                        {% endif %}
                        <div class="ml-3">
                            {% if postList.showAuthor %}
                            <p class="text-sm font-medium text-gray-900 mb-0">
                                <span>{{ post.author.first_name }} {{ post.author.last_name }}</span>
                            </p>
                            {% endif %}
                            {% if postList.showDates %}
                            <div class="flex space-x-1 text-sm text-gray-500">
                                <time datetime="{{ post.publication_date|date('M d, Y') }}">{{ post.publication_date|date('M d, Y') }}</time>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="">{{ pager(items, { partial: 'ui/pagination' }) }}</div>
    </div>
</section>