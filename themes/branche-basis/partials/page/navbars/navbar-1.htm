[staticMenu mainMenu]
code = "main-menu"
==
{% set mainmenuItems = mainMenu.resetMenu(this.theme.navbar.site_primary_menu) %}

<header id="navbar-1" class="pt-8 bg-transparent {{ this.page.layout == 'hero' ? 'absolute inset-x-0 z-40' : '' }}" data-toggle="sticky-onscroll">
    <div class="container">
        <div class="flex flex-wrap items-center justify-between pb-4">
            <div class="">
                {% partial 'ui/logo/logo_primair' %}
            </div>
            <nav class="mainmenu" x-data="{ open: false }">
                {% partial 'page/mainmenus/mainmenu-trigger' %}
                {% partial 'page/mainmenus/mainmenu' mainmenuItems=mainmenuItems %}
                {% partial 'page/mobilemenus/mobilemenu' mainmenuItems=mainmenuItems %}
            </nav>
            <div class="navbar-buttons hidden md:block">
                {% for btn in this.theme.navbar.btn %}
                {% partial 'ui/button' item = btn %}
                {% endfor %}
            </div>
        </div>
    </div>
</header>
