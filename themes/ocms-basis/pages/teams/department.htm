url = "/teams/:slug"
layout = "default"
title = "Department"
meta_title = "Department details"

[DepartmentList]
slug = "{{ :slug }}"

[siteSearchInclude]
==
{% if not DepartmentList %}
    {% do abort(404) %}
{% endif %}

{% set department = DepartmentList.department %}
{% set members = DepartmentList.members %}
{% set teamPage = DepartmentList.teamPage %}
{% set departmentPage = DepartmentList.departmentPage %}
{% set memberPage = DepartmentList.memberPage %}

<div class="container py-20">
    <div class="">
        {% for member in members %}
        <div><a href="{{ memberPage | page({ slug: member.slug }) }}" class="">{{ member.name }}</a></div>
        {% endfor %}
    </div>
</div>
