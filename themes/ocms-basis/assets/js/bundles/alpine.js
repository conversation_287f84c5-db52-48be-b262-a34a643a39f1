// Import AlpineJS
import Alpine from 'alpinejs';

// Import AlpineJS Plugins
import intersect from '@alpinejs/intersect'
import collapse from '@alpinejs/collapse'
import mask from '@alpinejs/mask'
import focus from '@alpinejs/focus'
import persist from '@alpinejs/persist'

// Import AlpineJS Data
import setPersist from './../components/alpine/persist';
import cookieTabs from './../components/alpine/cookieTabs';
import tabs from './../components/alpine/tabs';
import openClose from './../components/alpine/openClose';
import searchBox from './../components/alpine/searchBox';
import radioButtonGroup from './../components/alpine/radioButtonGroup';

import './../components/alpine/tooltip';

// Activate AlpineJS Plugins
Alpine.plugin(focus)
Alpine.plugin(intersect)
Alpine.plugin(collapse)
Alpine.plugin(mask)
Alpine.plugin(persist)

// Activate AlpineJS Data
Alpine.data('openClose', openClose)
Alpine.data('cookieTabs', cookieTabs)
Alpine.data('tabs', tabs)
Alpine.data('searchBox', searchBox)
Alpine.data('radioButtonGroup', radioButtonGroup)
Alpine.data('setPersist', setPersist)

// Start AlpineJS
window.Alpine = Alpine;

Alpine.start();
