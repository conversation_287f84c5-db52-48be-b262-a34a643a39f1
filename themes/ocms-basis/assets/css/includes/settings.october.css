
.oc-img-full {
    width: 100% !important;
}

.oc-img-circle {
    @apply rounded-full;
}

.oc-img-rounded {
    @apply rounded-lg;
}

/* Text */
.oc-text-gray {
    color: #AAA !important;
}

.oc-text-bordered {
    border-top: solid 1px #222;
    border-bottom: solid 1px #222;
    padding: 10px 0;
}

.oc-text-spaced {
    letter-spacing: 1px;
}

.oc-text-uppercase {
    text-transform: uppercase;
}

/* Links */
a.oc-link-strong {
    font-weight: 700;
}

a.oc-link-green {
    color: green;
}

/* Table */
table.oc-dashed-borders td,
table.oc-dashed-borders th {
    border-style: dashed;
}

table.oc-alternate-rows tbody tr:nth-child(2n) {
    background: #f5f5f5;
}

/* Table cell */
table td.oc-cell-highlighted,
table th.oc-cell-highlighted {
    border: 1px double red;
}

table td.oc-cell-thick-border,
table th.oc-cell-thick-border {
    border-width: 2px;
}

/* Images */
img.oc-img-circle {
    border-radius: 100%;
    background-clip: padding-box;
}

img.oc-img-rounded {
    border-radius: 0.5rem;
}

img.oc-img-bordered {
    border: solid 10px #CCC;
    box-sizing: content-box;
}

img.oc-img-full {
    width: 100% !important;
}

img.img-fill-box {
    width: 100%;
    height: 100%;
    object-fit: cover;
    margin: 0 !important;
}
