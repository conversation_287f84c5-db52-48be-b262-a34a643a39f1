description = "Homepagina - Oneshot"

[staticPage]
useContent = 0
default = 0

[staticBreadcrumbs]

[sitePicker]
==
<!doctype html>
<html lang="en">
    <head>
        <title>{{ this.page.title }}</title>
        {% partial "site/head" %}
        {% styles %}
    </head>
    <body>
        {% partial "site/scripts_body_top" %}
        {% page  %}
        {% partial "page/navbars/" ~ this.theme.navbar ~ ".htm" %}

        {variable tab="Header" name="title_style" label="Titel Opmaak" span="row" spanClass="col-sm-6" type="balloon-selector" default="single_color" options="single_color: 1 kleur | multi_color: 2 kleuren"}{/variable}
        {variable tab="Header" name="image" label="Afbeelding" span="row" spanClass="col-sm-4" type="mediafinder"}{/variable}
        {variable tab="Header" name="title_1" label="Titel" span="row" spanClass="col-sm-6" type="text" }{/variable}
        {variable tab="Header" name="title_color" label="Titel toevoeging (in kleur)" span="row" spanClass="col-sm-6" type="text" }{/variable}
        {variable tab="Header" name="content" label="Tekst" span="row" spanClass="col-sm-12" type="richeditor" }{/variable}
        {variable tab="Header" name="buttons" label="Button" span="row" spanClass="col-sm-8" type="repeater" prompt="Voeg een button toe" maxItems="2" style="accordion" titleFrom="text" form="$/../themes/oneshot-online-operators/repeaters/includes/button.yaml"}{/variable}
        {repeater tab="Header" name="cards" label="Card" prompt="Voeg een card toe" maxItems="4" style="accordion" span="row" spanClass="col-sm-12" titleFrom="text"}
            {variable name="title" label="Card titel" span="row" spanClass="col-sm-7"}{/variable}
            {variable name="slug" label="Pagina" span="row" spanClass="col-sm-5" type="pagefinder" placeholder="Kies een pagina"}{/variable}
            {variable name="content" label="Content" span="row" spanClass="col-sm-12" type="text" }{/variable}
        {/repeater}


        <div class="bg-gradient-to-r from-tertiary-600 via-tertiary-300 to-tertiary-600">
            <div class="relative container">
                <div class="absolute inset-0 overflow-hidden">
                    <img src="{{ image|media|resize(1000, auto, { 'extension': 'webp' }) }}" alt="{{ title }}"
                         class="block mx-auto">
                </div>
                <div class="pt-12 md:pt-24 lg:pt-40 xl:pt-52 2xl:pt-58 pb-8 md:pb-12 relative text-white">
                    <div class="flex-col space-y-6 md:space-y-8 lg:space-y-0">
                        <div class="lg:mb-56">
                            <h1 class="flex flex-col md:space-y-3 text-4xl font-bold uppercase text-center tracking-tight sm:text-5xl lg:text-6xl">
                                <span class="text-primary-500">{{ title_1 }}</span>
                                {% if title_style == 'multi_color' %}
                                <span class="text-secondary-500">{{ title_color }}</span>
                                {% endif %}
                            </h1>
                            <div class="text-sm md:text-base text-center text-secondary-500">{{ content|raw }}</div>
                            {% if buttons %}
                            <div class="flex flex-wrap items-center justify-center align-items mt-6 lg:mt-12 space-x-0 md:space-x-6 space-y-3 md:space-y-0">
                                {% for button in buttons %}
                                {% partial 'ui/button' url=button.url size=button.size style=button.style icon=button.icon
                                text=button.text target_blank=button.target_blank %}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        <div class="grid md:grid-cols-2 gap-3 md:gap-8 lg:flex lg:w-full lg:w-auto lg:items-end lg:space-x-4 2xl:space-x-16">
                            {% for card in cards %}
                            <div class="lg:w-1/4 text-secondary-900">
                                <a href="{{ card.slug|link }}" class="group relative block w-full">
                                    <div class="lg:absolute lg:bottom-[68px] lg:group-hover:bottom-0 lg:opacity-0 lg:group-hover:opacity-100 flex flex-col items-center space-y-3
                                        w-full lg:scale-0 lg:group-hover:scale-100 border-b-2 border-primary-500 bg-white shadow-os shadow-black/40 transition-all
                                        ease-linear duration-200 px-4 md:px-8 py-3 md:py-6">
                                        <p class="text-lg text-primary-500 uppercase font-bold">{{ card.title }}</p>
                                        <p class="text-sm hyphens-auto">{{ card.content }}</p>
                                    </div>
                                    <div class="hidden lg:block text-center border-y-2 border-primary-500 py-5">
                                        <span class="leading-none text-lg text-secondary-500 uppercase font-semibold">{{ card.title }}</span>
                                    </div>
                                </a>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="">



            {repeater tab="Content" name="flexible" prompt="Voeg sectie toe" groups="$/../themes/oneshot-online-operators/repeaters/sections.yaml" displayMode="builder" titleFrom="name"}
                {% if 'banner_' in fields._group %}
                {% partial "section/banner/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'card_' in fields._group %}
                {% partial "section/card/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'cta_' in fields._group %}
                {% partial "section/cta/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'feature_' in fields._group %}
                {% partial "section/feature/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'hero_' in fields._group %}
                {% partial "section/hero/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'jobs_' in fields._group %}
                {% partial "section/jobs/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'logos_' in fields._group %}
                {% partial "section/logos/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'snippet_' in fields._group %}
                {% partial "section/snippet/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'stats_' in fields._group %}
                {% partial "section/stats/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'text_' in fields._group %}
                {% partial "section/text/" ~ fields._group ~ ".htm" fields=fields %}
                {% else %}
                {% partial "section/" ~ fields._group ~ ".htm" fields=fields %}
                {% endif %}
            {/repeater}

        </main>
        {% if this.theme.navbar == 'navbar-3' %}
        {variable tab="Flyout menu" name="snippet_title" type="text" label="Title" span="left" permissions="developer"}{/variable}
        {variable tab="Flyout menu" name="snippet" type="richeditor" size="huge" label="Snippet" span="left" toolbarButtons="snippets|html" permissions="developer"}{/variable}
        {% endif %}

        {% partial "page/footers/" ~ this.theme.footer ~ ".htm" %}

        {% partial "page/cookie-alert" %}

        {% partial "site/foot" %}

        {% scripts %}
        {% framework extras %}
    </body>
</html>