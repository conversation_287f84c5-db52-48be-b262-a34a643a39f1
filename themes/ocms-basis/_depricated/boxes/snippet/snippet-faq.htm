<div class="py-8 md:py-12 lg:py-20">
    <div class="container">
        <div>
            <h2 class="text-center">{{ box.title }}</h2>
        </div>

        <div class="mt-12 relative">
            <dl class="">
              {% for q in box.questions %}
              <div class="pt-6" x-data="{ open: false }">
                <dt class="text-lg" x-id="['faq']">
                  <!-- Expand/collapse question button -->
                  <button type="button" class="text-left w-full flex justify-between items-start py-4 px-5 text-white rounded" :class="{ 'bg-primary-500': open, 'bg-primary-600': !open}" :aria-controls="$id('faq')" :aria-expanded="open"  @click="open = !open" x-transition>
                    <span class="font-medium">{{ q.question }} </span>
                    <span class="ml-6 h-7 flex items-center">

                      <svg class="rotate-0 h-6 w-6 transform" :class="{ 'rotate-0': open, '-rotate-90': !open}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
                      </svg>
                    </span>
                  </button>
                </dt>
                <dd class="py-4 px-5" :id="$id('faq')" x-show="open" x-collapse>
                  <div class="text-base mt-2">{{ q.answer|raw }}</div>
                </dd>
              </div>
              {% endfor %}
          </dl>
        </div>
    </div>
</div>