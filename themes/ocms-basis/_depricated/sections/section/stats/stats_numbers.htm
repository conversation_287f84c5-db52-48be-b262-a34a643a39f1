<div class="py-10 sm:py-20" style="background-color: {{ fields.background_color }};">
    <div class="container px-6 lg:px-8">
        <div class="mx-auto max-w-2xl lg:max-w-none">
            {% if fields.title %}
            <div class="text-center mb-8 lg:mb-16">
                <h2 class="text-primary-500 text-3xl md:text-4xl font-bold">{{ fields.title }}</h2>
                {% if fields.subtitle %}
                <h2 class="text-secondary-400 text-base mt-2 md:mt-4">{{ fields.subtitle }}</h2>
                {% endif %}
            </div>
            {% endif %}
            <dl class="grid grid-cols-1 gap-0.5 overflow-hidden rounded-lg text-center sm:grid-cols-2 lg:grid-cols-4">
                {% for item in fields.statistics %}
                <div class="flex flex-col bg-gray-400/20 p-8">
                    <dt class="text-sm font-semibold leading-6 text-secondary-300">{{ item.subtext }}</dt>
                    <dd class="order-first text-3xl font-semibold tracking-tight text-secondary-500">
                        <span class="counter">
                            {% if item.additive %}
                                <span class="">{{ item.additive }}</span>
                            {% endif %}
                            <span class="counter-target" data-target-count="{{ item.number }}">0</span>
                            {% if item.additive2 %}
                                <span class="">{{ item.additive2 }}</span>
                            {% endif %}
                        </span>
                    </dd>
                </div>
                {% endfor %}
            </dl>
        </div>
    </div>
</div>
