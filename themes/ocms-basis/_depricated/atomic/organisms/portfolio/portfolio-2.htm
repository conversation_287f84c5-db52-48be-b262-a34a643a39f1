<div class="pf-tabs space-y-12" x-data="tabs" x-id="['tab']">
    {% if not PortfolioProjectList.property('category') %}
        {% partial 'atomic/molecules/portfolio/tabs-list' %}
    {% endif %}

    <div class="pf-tab-content" role="tabpanels">
        {% set button_label = 'Bekijk alle projecten'|_ %}
        {% set cat_button_label = 'Bekijk categorie'|_ %}
        {% set button_url = portfolioPage|link %}
        {% if PortfolioProjectList.property('category') %}
            {% set button_url = categoryPage | page({ slug: portfolioCategory.slug }) %}
        {% endif %}

        {% partial 'atomic/molecules/portfolio/tab'
            items = projects
            button_label = button_label
            button_link = button_url
            card_size = "sm"
            tab_grid = "space-y-8 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-4 md:gap-8" %}

        {% if not PortfolioProjectList.property('category') %}
            {% for category in categories %}
                {% set cat_projects = category.projects.sortByDesc('id').take(max_items) %}
                {% set cat_button_url = categoryPage | page({ slug: category.slug }) %}

                {% partial 'atomic/molecules/portfolio/tab'
                    items = cat_projects
                    button_label = cat_button_label
                    button_link = cat_button_url
                    card_size = "sm"
                    tab_grid = "space-y-8 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-4 md:gap-8" %}
            {% endfor %}
        {% endif %}
    </div>
</div>
