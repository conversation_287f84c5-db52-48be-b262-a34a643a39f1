[localePicker]
forceUrl = 0
==
<section id="top" class="py-1 text-white bg-gray-200 relative z-50">
    <div class="px-1 sm:px-3 lg:px-6">
        {% if this.theme.social_media %}
        <ul class="flex flex-wrap justify-end items-center space-x-4">
            {% for item in this.theme.social_media %}

            {% if item.title == 'facebook' %}
            {% set brandcolor = "text-social-facebook" %}
            {% elseif item.title == 'instagram' %}
            {% set brandcolor = "text-social-instagram" %}
            {% elseif item.title == 'snapchat' %}
            {% set brandcolor = "text-social-snapchat" %}
            {% elseif item.title == 'tiktok' %}
            {% set brandcolor = "text-social-tiktok" %}
            {% elseif item.title == 'twitter' %}
            {% set brandcolor = "text-social-twitter" %}
            {% elseif item.title == 'youtube' %}
            {% set brandcolor = "text-social-youtube" %}
            {% elseif item.title == 'linkedin' %}
            {% set brandcolor = "text-social-linkedin" %}
            {% elseif item.title == 'github' %}
            {% set brandcolor = "text-social-github" %}
            {% else %}
            {% set brandcolor = "text-white" %}
            {% endif %}

            <li class="">
                <a href="{{ item.slug }}" target="_blank">
                    <span class="sr-only">{{ item.title }}</span>
                    <i class="fab fa-{{ item.title }} text-lg {{ brandcolor }} hover:text-gray-500"></i>
                </a>
            </li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
</section>
<div class="block lg:hidden w-full px-3 py-1 relative z-50 bg-white">
    <div class="flex flex-col items-end space-y-1 text-sm md:flex-row md:items-center md:justify-end">
        <div class="md:pr-3 md:border-r md:mr-3 border-gray-300">
            <a href="mailto:{{ html_email(this.theme.company_email)|raw }}" class="group flex flex-wrap items-center text-gray-600 hover:text-gray-800 hover:underline"><i class="text-base fas fa-envelope mr-2 text-primary group-hover:text-primary-400"></i> {{ html_email(this.theme.company_email)|raw }}</a>
        </div>
        <div class="sm:pr-6">
            <a href="tel:{{ this.theme.company_telephone }}" class="group flex flex-wrap items-center text-gray-600 hover:text-gray-800 hover:underline"><i class="text-base fas fa-phone mr-2 text-primary group-hover:text-primary-400"></i> {{ this.theme.company_telephone }}</a>
        </div>
        <div class="hidden md:block">
            <a href="/contact" class="inline-flex items-center px-3 py-1.5 border border-transparent font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 text-white bg-primary-500 hover:bg-primary-600 focus:ring-primary-400 text-sm">Kom met ons in contact!</a>
        </div>
    </div>
</div>
