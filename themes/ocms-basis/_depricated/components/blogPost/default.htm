{% set post = __SELF__.post %}

<div class="container py-16">
    <div class="md:grid md:grid-cols-3 md:gap-16">
        <div class="col-span-2">
            <div class="mb-8">
                <div class="max-w-3xl">
                    <h1>{{ post.title }}</h1>
                </div>
                
                <p class="text-lg font-medium text-gray-800 mt-4 block">
                    {{ post.excerpt }}
                </p>
                <hr class="my-6 md:my-3">
                <div class="md:hidden">
                    {% for image in post.featured_images|slice(0,1) %}
                        <img
                            data-src="{{ image.filename }}"
                            src="{{ image.path }}"
                            alt="{{ image.description }}"
                            class="max-w-full rounded-lg shadow-xl" />
                    {% endfor %}
                     <hr class="my-6 md:my-3">
                </div>
                <div class="flex flex-wrap items-center justify-between text-sm text-gray-400">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-primary-600 mr-2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>
                            {{ post.published_at|date('d M, Y') }}
                        </span>
                    </div>
                    <div>
                        {% for category in post.categories %}
                            <a href="{{ category.url }}" class="text-primary-600 hover:text-primary-700 font-semibold">{{ category.name }}</a>{% if not loop.last %}, {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="content_section text-gray-600">
                {{ post.content_html|raw }}
            </div>
        </div>
        <div>
            {% for image in post.featured_images %}
                <img
                    data-src="{{ image.filename }}"
                    src="{{ image.path }}"
                    alt="{{ image.description }}"
                    class="max-w-full rounded-lg shadow-xl" />
            {% endfor %}
        </div>
    </div>

</div>