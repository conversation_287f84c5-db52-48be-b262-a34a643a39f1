<div class="search-icon">
    <button @click="toggle"><i class="fa-solid fa-magnifying-glass"></i></button>
</div>

<div class="relative z-50" role="dialog" aria-modal="true" aria-labelledby="modal-title" role="dialog" x-show="open" x-cloak x-on:keydown.escape.prevent.stop="close">
    <!--
        Background backdrop, show/hide based on modal state.
    -->
    <div aria-hidden="true"
        x-show="open"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        @click="close">
    </div>

    <div class="fixed inset-0 z-10 w-screen overflow-y-auto p-4 sm:p-6 md:p-20">
        <!--
            Command palette, show/hide based on modal state.
        -->
        <div x-show="open"
            x-transition:enter="ease-out duration-300"
            x-transition:enter-start="opacity-0 scale-95"
            x-transition:enter-end="opacity-100 scale-100"
            x-transition:leave="ease-in duration-200"
            x-transition:leave-start="opacity-100 scale-100"
            x-transition:leave-end="opacity-0 scale-95"
            class="mx-auto max-w-xl transform divide-y divide-gray-100 overflow-hidden rounded-xl bg-white shadow-2xl ring-1 ring-black ring-opacity-5 transition-all">

            <form action="{{ __SELF__.searchPage | page }}?q={{ query | url_encode(true) }}" method="get" class="relative" @click.away="close">
                <svg class="pointer-events-none absolute left-4 top-3.5 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" />
                </svg>
                <input
                    name="q" type="text" placeholder="{{ 'Waar bent u naar op zoek?'|_ }}" 
                    value="{{ __SELF__.query }}"
                    autocomplete="off"
                    autofocus
                    {% if __SELF__.useAutoComplete %}
                        data-track-input
                        data-request="{{ __SELF__ }}::onType"
                        data-request-before-update="document.getElementById('autocomplete-results').classList.toggle('ss-search-form__results--visible', !!this.value)"
                        data-request-update="'{{ __SELF__ }}::autocomplete': '#autocomplete-results'"
                    {% endif %}
                    class="h-12 w-full border-0 bg-transparent pl-11 pr-4 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm"
                >
            </form>

        <!-- Results, show/hide based on command palette state -->
            <div id="autocomplete-results"></div>
        </div>
    </div>
</div>