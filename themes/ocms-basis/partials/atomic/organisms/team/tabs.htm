[MemberList]
pagination = "{{ pagination }}"
itemsPerPage = "{{ itemsPerPage }}"
==
{% set departments = MemberList.departments %}
{% set members = MemberList.members %}
{% set teamPage = MemberList.teamPage %}
{% set departmentPage = MemberList.departmentPage %}
{% set memberPage = MemberList.memberPage %}
{% set team_box = team_box %}

<div class="space-y-8 xl:space-y-12" x-data="tabs" x-id="['tab']">
    {% if departments.toArray() %}
    <div class="flex items-center justify-center">
        <ul
            class="flex flex-wrap items-center justify-center dark:bg-gray-300 border border-gray-400 rounded-lg p-1 gap-2"
            role="tablist"
            x-ref="tablist"
            @keydown.right.prevent.stop="$focus.wrap().next()"
            @keydown.home.prevent.stop="$focus.first()"
            @keydown.page-up.prevent.stop="$focus.first()"
            @keydown.left.prevent.stop="$focus.wrap().prev()"
            @keydown.end.prevent.stop="$focus.last()"
            @keydown.page-down.prevent.stop="$focus.last()"
        >
            <li>
                <button
                    :id="$id('tab', whichChild($el.parentElement, $refs.tablist))"
                    @click="select($el.id)"
                    @mousedown.prevent
                    @focus="select($el.id)"
                    :tabindex="isSelected($el.id) ? 0 : -1"
                    :aria-selected="isSelected($el.id)"
                    :class="isSelected($el.id) ? ' bg-primary-100 hover:bg-primary-200 text-primary-900 dark:bg-primary-600 dark:hover:bg-primary-700 dark:text-primary-50' : 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 dark:text-gray-100'"
                    class="inline-block font-bold tracking-wide text-sm rounded-lg shadow-sm leading-none py-3 px-6"
                    role="tab"
                    type="button"
                >
                    {{ 'Het hele team'|_ }}
                </button>
            </li>
            {% for category in departments %}
                <li>
                    <button
                        :id="$id('tab', whichChild($el.parentElement, $refs.tablist))"
                        @click="select($el.id)"
                        @mousedown.prevent
                        @focus="select($el.id)"d
                        :tabindex="isSelected($el.id) ? 0 : -1"
                        :aria-selected="isSelected($el.id)"
                        :class="isSelected($el.id) ? ' bg-primary-100 hover:bg-primary-200 text-primary-900 dark:bg-primary-600 dark:hover:bg-primary-700 dark:text-primary-50' : 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 dark:text-gray-100'"
                        class="inline-block font-bold tracking-wide text-sm rounded-lg shadow-sm leading-none py-3 px-6"
                        role="tab"
                        type="button"
                    >
                        {{ category.title }}
                    </button>
                </li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}

    <div class="" role="tabpanels">

        {% if team_box == "1" %}
            {% set gridClass = 'flex flex-col justify-center md:grid md:grid-cols-2 lg:grid-cols-3' %}
        {% elseif team_box == "2" %}
            {% if grid_columns == "2" %}
                {% set gridClass = 'md:mx-auto md:max-w-2xl md:grid md:grid-cols-2' %}
            {% elseif grid_columns == "3" %}
                {% set gridClass = 'md:mx-auto md:max-w-5xl md:grid md:grid-cols-2 lg:grid-cols-3' %}
            {% else %}
                {% set gridClass = 'md:grid md:grid-cols-2 lg:grid-cols-4' %}
            {% endif %}
        {% elseif team_box == "3" %}
            {% set gridClass = 'flex flex-col justify-center space-y-8 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-8' %}
        {% elseif team_box == "4" %}
            {% set gridClass = 'space-y-8 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-8' %}
        {% elseif team_box == "6" %}
            {% set gridClass = 'flex flex-col justify-center space-y-8 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-8' %}
        {% elseif team_box == "7" %}
            {% set gridClass = 'flex flex-col justify-center space-y-8 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-8' %}
        {% elseif team_box == "8" %}
            {% set gridClass = 'flex flex-col justify-center md:grid md:grid-cols-2 lg:grid-cols-4' %}
        {% elseif team_box == "9" %}
            {% set gridClass = 'flex flex-col justify-center space-y-8 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-4 md:gap-8' %}
        {% endif %}

        <div
            class="{{ gridClass }}"
            x-show="isSelected($id('tab', whichChild($el, $el.parentElement)))"
            :aria-labelledby="$id('tab', whichChild($el, $el.parentElement))"
            role="tabpanel"
            tabindex="0"
            x-transition:enter="transition ease-out duration-500"
            x-transition:enter-start="opacity-0 translate-y-2"
            x-transition:enter-end="opacity-100 translate-y-0"
        >
            {% for item in members %}
                {% partial 'atomic/organisms/team/team-box' item=item team_box=team_box %}
            {% endfor %}

        </div>
        {% for department in departments %}
            <div
                class="{{ gridClass }}"
                x-show="isSelected($id('tab', whichChild($el, $el.parentElement)))"
                :aria-labelledby="$id('tab', whichChild($el, $el.parentElement))"
                role="tabpanel"
                tabindex="0"
                x-transition:enter="transition-all ease-out duration-500"
                x-transition:enter-start="opacity-0 translate-y-2"
                x-transition:enter-end="opacity-100 translate-y-0"
                x-cloak
            >
                {% for item in department.members %}
                    {% partial 'atomic/organisms/team/team-box' item=item team_box=team_box %}
                {% endfor %}
            </div>
        {% endfor %}
    </div>
    {% if pagination %}
        <div class="">
            {{ pager(members, { partial: 'ui/pagination' }) }}
        </div>
    {% endif %}
</div>
