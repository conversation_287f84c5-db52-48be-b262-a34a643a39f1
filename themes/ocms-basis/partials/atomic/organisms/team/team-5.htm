{% put scripts %}
    <script>
        $('.team-{{ box.id }}-mainslider').slick({
            slidesToShow: 1,
            slidesToScroll: 1,
            infinite: true,
            // dots: true,
            autoplay: true,
            autoplaySpeed: 5000,
            prevArrow: '#team-{{ box.id }}-prev',
            nextArrow: '#team-{{ box.id }}-next',
            asNavFor: '.team-{{ box.id }}-thumbslider, .team-{{ box.id }}-contentslider',
            {% if members|length <= 5 %}swipe: false,{% endif %}
        });
        $('.team-{{ box.id }}-thumbslider').slick({
            slidesToShow: 5,
            slidesToScroll: 1,
            focusOnSelect: true,
            arrows: false,
            {% if members|length > 5 %}
            centerMode: true,
            {% endif %}
            asNavFor: '.team-{{ box.id }}-mainslider, .team-{{ box.id }}-contentslider',
            responsive: [
                {
                    breakpoint: 450,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1,
                    }
                }

            ]
        });
    </script>
{% endput %}

{% put styles %}
    <style>
        .team-{{ box.id }}-thumbslider .slick-slide {
            opacity: 0.5;
        }

        .team-{{ box.id }}-thumbslider .slick-slide.slick-current {
            opacity: 1;
        }
    </style>
{% endput %}

<div class="relative">

    <div class="team-{{ box.id }}-slider team-{{ box.id }}-mainslider">
        {% for item in members %}

            {% set memberName = item.name ~ " " ~ item.surname %}

            <div class="">
                <div class="space-y-8 md:space-y-0 md:grid md:grid-cols-2 md:gap-8">

                    <div class="col-span-1 overflow-hidden">
                        {% partial 'atomic/atoms/media/image' img=item.img title=memberName resize_w='900' class='lazy w-full h-full object-cover' %}
                    </div>

                    <div class="col-span-1 flex items-center h-full">

                        <div>

                            <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                                {% partial 'atomic/atoms/headings/header-span' text=memberName level=3 headingClass='member-name' %}
                                {% if item.title %}
                                    {% partial 'atomic/atoms/headings/header-span' text=item.title level=5 headingClass='member-title' %}
                                {% endif %}
                            </div>

                            <div class="flex flex-1 flex-col gap-4">
                                {% if item.email or item.phonenumber %}
                                    <div
                                        class="flex flex-col prose prose-a:no-underline hover:prose-a:underline prose-primary dark:prose-primary_inverted max-w-none">
                                        {% if item.email %}
                                            <a href="mailto:{{ html_email(item.email)|raw }}" title=""
                                               itemprop="email"
                                               class="">{{ html_email(item.email)|raw }}</a>
                                        {% endif %}
                                        {% if item.phonenumber %}
                                            <a href="tel:{{ item.phonenumber }}" title="" itemprop="telephone"
                                               class="">{{ item.phonenumber }}</a>
                                        {% endif %}
                                    </div>
                                {% endif %}

                                {% if item.description %}
                                    <div
                                        class="prose-p:text-gray-900 prose prose-sm prose-primary dark:prose-p:text-gray-300 dark:prose-primary_inverted max-w-none">
                                        {% partial 'atomic/molecules/content-section' content=item.description %}
                                    </div>
                                {% endif %}

                                {% if item.socials %}
                                    <div class="">
                                        <ul class="flex flex-wrap gap-x-4 gap-y-2">
                                            {% for item in item.socials %}
                                                {% partial 'atomic/atoms/team/socials' item=item %}
                                            {% endfor %}
                                        </ul>
                                    </div>
                                {% endif %}

                            </div>
                        </div>

                    </div>
                </div>

            </div>
        {% endfor %}

    </div>

    <div class="hidden absolute top-0 right-0 flex gap-8">
        <div class="">
            <button type="button" id="team-{{ box.id }}-prev" class="text-2xl md:text-3xl lg:text-4xl">
                <i class="fa-solid fa-chevron-left"></i>
            </button>
        </div>

        <div class="">
            <button type="button" id="team-{{ box.id }}-next" class="text-2xl md:text-3xl lg:text-4xl">
                <i class="fa-solid fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <div class="pt-6">
        <div class="team-{{ box.id }}-slider team-{{ box.id }}-thumbslider">
            {% for item in members %}
                {% set itemName = item.name ~ " " ~ item.surname %}
                <div>
                    <div class="aspect-[3/2] lg:pt-6 lg:px-4">
                        {% partial 'atomic/atoms/media/image' img=item.img title=itemName resize_w='320' class='lazy w-full h-full object-cover' %}
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

</div>
