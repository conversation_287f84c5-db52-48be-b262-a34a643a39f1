{% put scripts %}
    <script>
        $('.blog-{{ box.id }}-slider').slick({
            slidesToShow: 2,
            slidesToScroll: 1,
            infinite: true,
            autoplay: true,
            autoplaySpeed: 5000,
            prevArrow: '#blog-{{ box.id }}-prev',
            nextArrow: '#blog-{{ box.id }}-next',
            responsive: [
                {
                    breakpoint: 450,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                    }
                }

            ]
        });
    </script>
{% endput %}

<div class="space-y-4">
    <div class="grid grid-cols-2 gap-4 md:grid md:grid-cols-12 md:gap-8">

        <div class="col-span-2 md:col-span-10">
            <div class="blog-{{ box.id }}-slider">
                {% for item in posts %}
                    <div class="px-4">
                        {% partial 'atomic/molecules/blog/blog-item-card-slider' item=item categoryPage=categoryPage link=postPage | page({ slug: item.slug }) %}
                    </div>
                {% endfor %}
            </div>
        </div>

        <div class="flex items-center justify-center text-gray-700 dark:text-gray-200 md:order-first">
            <button type="button" id="blog-{{ box.id }}-prev" class="text-4xl">
                <i class="fa-solid fa-chevron-left"></i>
            </button>
        </div>

        <div class="flex items-center justify-center text-gray-700 dark:text-gray-200 md:order-last">
            <button type="button" id="blog-{{ box.id }}-next" class="text-4xl">
                <i class="fa-solid fa-chevron-right"></i>
            </button>
        </div>

    </div>

</div>

{% put styles %}
<style>
    .slick-track { display: flex; }
    .slick-slide > div,
    .slick-slide .slide { height: 100% !important; }
    .slick-slide .slide { display: flex !important; }
</style>
{% endput %}