{% if loop.index == "1" %}
    {% set aspect = "aspect-thumb lg:aspect-[8/3]" %}
{% else %}
    {% set aspect = "aspect-thumb" %}
{% endif %}

<div class="pf-project {{ card_size == "sm" ? 'pf-project-sm' }} group transition-all duration-300 {{ project_class }}">
    <a href="{{ projectPage | page({ slug: item.slug }) }}" title="{{ 'Bekijk project'|_ }}" class="{{ aspect }} block h-full">
        <div class="absolute inset-x-0 bottom-0 h-1/2 bg-gradient-to-t from-gray-800 z-20"></div>

        <img src="{{ item.overview_image | media | resize(800, 600, { mode: 'crop' }) }}" alt="{{ item.title }}" class="lazy w-full h-full object-cover group-hover:scale-110 z-10">

        <div class="pf-project-title z-30">{{ item.title }}</div>
    </a>
</div>
