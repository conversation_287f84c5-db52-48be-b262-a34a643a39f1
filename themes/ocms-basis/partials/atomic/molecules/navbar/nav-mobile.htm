<div id="mobile-nav" role="navigation" aria-label="Mobile">
    <ul>
        {% for item in mainmenuItems %}
            {% if not item.viewBag.isHidden %}
                <li class="relative {{ item.isActive or item.isChildActive ? 'active' }} group/menuitem" x-data="{ subOpen: false }">
                    {% if item.type == 'header' %}
                        {% partial 'atomic/atoms/navbar/nav-header' mobile=true %}
                    {% else %}
                        {% partial 'atomic/atoms/navbar/nav-link' mobile=true %}
                    {% endif %}

                    {% if item.items %}
                        <ul class="w-full mt-2 ml-2 space-y-2" x-show="subOpen" x-collapse x-cloak>
                            {% for sub in item.items %}
                                <li>
                                    {% if sub.type == 'header' %}
                                        {% partial 'atomic/atoms/navbar/nav-header' item = sub %}
                                    {% else %}
                                        {% partial 'atomic/atoms/navbar/nav-link' item = sub %}
                                    {% endif %}
                                </li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </li>
            {% endif %}
        {% endfor %}
    </ul>

    {% if navbar.topmenu %}
        <div class="w-full my-3 border-t"></div>

        <ul class="mobile-topmenu">
            {% for item in topmenuItems %}
                {% if not item.viewBag.isHidden %}
                    <li class="relative {{ item.isActive or item.isChildActive ? 'active' }} group/menuitem" x-data="{ subOpen: false }">
                        {% if item.type == 'header' %}
                            {% partial 'atomic/atoms/navbar/nav-header' mobile=true no_parent_icon=true %}
                        {% else %}
                            {% partial 'atomic/atoms/navbar/nav-link' mobile=true no_parent_icon=true %}
                        {% endif %}
                    </li>
                {% endif %}
            {% endfor %}
        </ul>
    {% endif %}
</div>
