{% set memberName = item.name ~ " " ~ item.surname %}
<div class="{{ class }}">
    <div class="flex items-center justify-center overflow-hidden">
        {% partial 'atomic/atoms/media/image' img=item.img title=memberName resize_w='320' class='w-32 h-32 rounded-full object-cover' %}
    </div>
    <div class="items-center prose prose-primary dark:prose-primary_inverted max-w-none">
        {% partial 'atomic/atoms/headings/header-span' text=memberName level=3 headingClass='member-name' %}
        {% if item.title %}
            {% partial 'atomic/atoms/headings/header-span' text=item.title level=5 headingClass='member-title' %}
        {% endif %}
    </div>
</div>