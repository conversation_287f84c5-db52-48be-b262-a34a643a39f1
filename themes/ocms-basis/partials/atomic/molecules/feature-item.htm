<div class="feature {{ feature_class }}">
    <div class="feature-icon">
        {% partial 'atomic/atoms/media/ph-icon' icon=item.icon|default('ph') style=icon_style|default('ph-check-circle') %}
    </div>
    <div>
        {% partial 'atomic/atoms/header-h5' text=item.title %}
        {% partial 'atomic/atoms/text-small' text=item.text %}
    </div>
</div>

{#
    accepted variables:

    'item'
        - feature to display
        - type: object
        - default: null
        - mixin: _mixin_features.yaml (handle 'features')

    'feature_class'
        - CSS class, can be Tailwind CSS classes
        - default: null

    'icon_style'
        - phosphor icon style code
        - default: 'ph'
        - mixin: _mixin_icon_style_ph.yaml (handle 'icon-style-ph')
#}
