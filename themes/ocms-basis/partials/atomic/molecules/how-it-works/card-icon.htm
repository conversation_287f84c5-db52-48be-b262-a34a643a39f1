<div class="card-atomic card-icon {{ cardClass }}">

    <div class="relative dark:text-white">
        <div class="absolute -top-4 -left-4 z-10">
            {% partial 'atomic/atoms/how-it-works/number-sm' text=index %}
        </div>
        {% partial 'atomic/atoms/media/ph-icon' icon=item.icon class=iconClass %}
    </div>

    <div class="relative card-atomic card-content {{ cardContentClass }}">
        {% if item.title %}
            <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                {% partial 'atomic/atoms/headings/header-h3' text=item.title %}
            </div>
        {% endif %}

        {% if item.content_card %}
            {% partial 'atomic/atoms/content-card' text=item.content_card class=contentClass truncate=truncate %}
        {% endif %}
    </div>

    {% for item in item.button_single %}
        <div class="mt-auto">
            {% partial "ui/button" item=item css_class='flex items-center justify-center w-full text-center' %}
        </div>
    {% endfor %}
</div>
