{% macro euro(number) %}
    &euro; {{ number | number_format(2, ',', '.') }}
{% endmacro %}
{% import _self as format %}
{% put styles %}
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
    <link rel="stylesheet" type="text/css" href="slick/slick-theme.css"/>

    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />
    <style>
        .slick-current.slick-active { opacity: 1; }
        .slick-slide { opacity: .5; }
    </style>
    <style>
    .slick-dots {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
    }
    .slick-dots li { display: flex; }
    .slick-dots button {
        width: 8px;
        height: 8px;
        border-radius: 999px;
        font-size: 0;
        line-height: 0;
        color: transparent;
        opacity: .5;
    }
    .slick-dots .slick-active button {
        opacity: 1;
    }
</style>
{% endput %}

{% put scripts %}
    <script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <script>
        $('.product-slider').slick({
            slidesToShow: 1,
            slidesToScroll: 1,
            dots: true,
            appendDots: '.product-slider-dots',
            prevArrow: '.product-slider-prev',
            nextArrow: '.product-slider-next',
        });
    </script>
{% endput %}

<div class="md:hidden mb-8">
    <div class="flex flex-col-reverse">

        <div class="">
            <h1 class="text-2xl font-bold tracking-tight text-gray-900 sm:text-4xl">{{ product.title }}</h1>
            <p class="text-primary-500 text-xl font-medium mt-2">Vanaf {{ format.euro(product.price) }}</p>
            <h2 id="information-heading" class="sr-only">Product informatie</h2>
        </div>

    </div>

    <p class="mt-6 text-gray-500 text-lg">{{ product.excerpt }}</p>
</div>

<div class="md:aspect-w-4 md:aspect-h-3 overflow-hidden rounded-lg bg-gray-100 relative">
    <div class="p-4 md:p-10 flex items-center">
        <div class="w-full relative flex flex-wrap items-center md:block justify-between">
            <div class="product-slider w-full mb-4 md:mb-0">
                {% for img in product.images %}
                    <div>
                        <img src="{{ img|media|resize(500, null, { extension: 'webp', filename: true, quality: 90 }) }}" alt="{{ product.title }}" class="block mx-auto">
                    </div>
                {% endfor %}
            </div>
            <div id="product-slider-prev" class="product-slider-prev md:absolute md:left-0 md:top-1/2 md:-translate-y-1/2 w-10 h-10 bg-primary-500 rounded-full shadow-xl shadow-black/20 text-white text-base after:hidden flex items-center justify-center cursor-pointer">
                <i class="fa-regular fa-chevron-left pointer-events-none"></i>
            </div>

            <div id="product-slider-next" class="product-slider-next md:absolute md:right-0 md:top-1/2 md:-translate-y-1/2 w-10 h-10 bg-primary-500 rounded-full shadow-xl shadow-black/20 text-white text-base after:hidden flex items-center justify-center cursor-pointer">
                <i class="fa-regular fa-chevron-right pointer-events-none"></i>
            </div>
        </div>
        <div class="product-slider-dots absolute inset-x-0 bottom-8 md:bottom-10"></div>
    </div>
</div>
