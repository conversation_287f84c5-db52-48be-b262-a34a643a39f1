[MemberList]
department = "{{ department }}"
pagination = "{{ pagination }}"
itemsPerPage = "{{ itemsPerPage }}"
==
{% set members = MemberList.members %}
{% set departments = MemberList.departments %}
{% set teamPage = MemberList.teamPage %}
{% set departmentPage = MemberList.departmentPage %}
{% set memberPage = MemberList.memberPage %}

{% if team_box == "1" %}
    {% partial 'atomic/organisms/team/team-1' %}
{% elseif team_box == "2" %}
    {% partial 'atomic/organisms/team/team-2' grid_columns=grid_columns %}
{% elseif team_box == "3" %}
    {% partial 'atomic/organisms/team/team-3' %}
{% elseif team_box == "4" %}
    {% partial 'atomic/organisms/team/team-4' %}
{% elseif team_box == "5" %}
    {% partial 'atomic/organisms/team/team-5' %}
{% elseif team_box == "6" %}
    {% partial 'atomic/organisms/team/team-6' %}
{% elseif team_box == "7" %}
    {% partial 'atomic/organisms/team/team-7' %}
{% elseif team_box == "8" %}
    {% partial 'atomic/organisms/team/team-8' %}
{% elseif team_box == "9" %}
    {% partial 'atomic/organisms/team/team-9' %}
{% elseif team_box == "10" %}
    {% partial 'atomic/organisms/team/team-10' %}
{% endif %}

{% if pagination %}
    <div class="">
        {{ pager(members, { partial: 'ui/pagination' }) }}
    </div>
{% endif %}
