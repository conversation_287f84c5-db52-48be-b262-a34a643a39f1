<div>
    <h3 class="ss-result__title / leading-none">
        <a href="{{ result.url | app }}">{{ result.title | raw }}</a>
        {% if __SELF__.showProviderBadge %}
            <span class="ss-result__badge">
                {{ result.provider }}
            </span>
        {% endif %}
    
        {# Some results contain meta information.     #}
        {# You can access it using result.meta.       #}
        {# To find out from which plugin the result   #}
        {# was returned you can use result.identifier #}
        {# Some results contain the original model.   #}
        {# You can access it using result.model       #}
    </h3>
    
    <p class="ss-result__text content_section">
        {{ result.excerpt | raw }}
    </p>
</div>

<div class="shrink-0">
    <p class="ss-result__url">
        <a href="{{ result.url | app }}" class="ss-result__link text-xl group hover:text-primary-600">{{ __SELF__.visitPageMessage }} <span class="text-primary-600 group-hover:text-primary-700 ml-2">&rarr;</span></a>
    </p>
</div>
