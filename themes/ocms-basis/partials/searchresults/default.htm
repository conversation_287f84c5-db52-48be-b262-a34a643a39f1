{% set results = __SELF__.results %}

{% if (__SELF__.query | length) < __SELF__.minQueryLength %}

    {% partial __SELF__ ~ '::query-too-short.htm' %}

{% else %}

    <div class="prose prose-primary max-w-none">
        <h2 class="mb-4">{{ results|length }} {{ results|length > 1 ? 'resultaten' : 'resultaat' }}:</h2>
    </div>

    {% for result in results %}
        {# Display all results #}
        {% partial __SELF__ ~ '::searchresult.htm' result=result %}
    {% else %}
        {# No results found #}
        {% partial __SELF__ ~ '::no-results.htm' %}
    {% endfor %}

    {% partial __SELF__ ~ '::pagination.htm' results=results %}

{% endif %}
