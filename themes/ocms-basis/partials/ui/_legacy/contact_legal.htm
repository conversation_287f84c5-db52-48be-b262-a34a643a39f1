{% if this.theme.company.legal.tos or this.theme.company.legal.privacy %}
<div class="mt-6 flex">
    <div class="flex-shrink-0">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
    </div>
    {% if this.theme.company.legal.tos.media or this.theme.company.legal.tos.slug or this.theme.company.legal.privacy.media or this.theme.company.legal.privacy.slug %}
    <div class="flex flex-col">
        {% if this.theme.company.legal.tos.slug or this.theme.company.legal.tos.media %}
        <div class="ml-3 text-sm text-gray-900">
            <p>
                <a class="text-lead hover:text-primary-500 hover:underline"
                    href="{{ this.theme.company.legal.tos.type  == 'page' ? this.theme.company.legal.tos.slug|link : this.theme.company.legal.tos.media|media }}"
                    target="_blank">
                    {{ 'Algemene voorwaarden'|_ }}
                </a>
            </p>
        </div>
        {% endif %}
        {% if this.theme.company.legal.privacy.slug or this.theme.company.legal.privacy.media %}
            <div class="mt-1 ml-3 text-sm text-gray-900">
                <p>
                    <a class="text-lead hover:text-primary-500 hover:underline"
                    href="{{ this.theme.company.legal.privacy.type  == 'page' ? this.theme.company.legal.privacy.slug|link : this.theme.company.legal.privacy.media|media }}"
                    target="_blank">{{ 'Privacy Statement'|_ }}</a>
                </p>
            </div>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endif %}
