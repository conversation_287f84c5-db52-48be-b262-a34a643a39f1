<div class="" itemscope itemtype="https://schema.org/LocalBusiness">
    <span class="sr-only" itemprop="name">{{ company.name }}</span>
    <address class="not-italic" itemprop="address" itemscope itemtype="https://schema.org/PostalAddress">
        {% set email = address.email ? address.email : company.email %}

        <span itemprop="streetAddress">{{ company.street }} {{ company.housenumber }}{{ company.addition }}</span> <br>
        <span itemprop="postalCode">{{ company.zipcode }}</span> <span itemprop="addressLocality">{{ company.city }}</span> <br>
        <br>
        <a href="tel:{{ company.telephone }}" title="{{ 'Bel ons!'|_ }}" itemprop="telephone">{{ company.telephone }}</a>
        <a href="tel:{{ company.mobile }}" title="{{ 'Bel ons!'|_ }}">{{ company.mobile }}</a>
        <a href="mailto:{{ html_email(company.email)|raw }}" title="{{ 'Mail ons!'|_ }}" itemprop="email">{{ html_email(company.email)|raw }}</a>
    </address>
</div>