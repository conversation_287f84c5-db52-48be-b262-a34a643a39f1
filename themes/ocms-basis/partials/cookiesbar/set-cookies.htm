var date = new Date();

date.setDate(date.getDate() + {{ cookiesSettingsGet('set_cookies_lifetime_days') ? cookiesSettingsGet('set_cookies_lifetime_days') : 365 }});

document.cookie = "{{ 'sg-cookies'~(sgCookiesLocalePrefix ? sgCookiesLocalePrefix)~'-consent=1; path=/; expires='}}" + date.toGMTString();

{% if cookiesSettingsGet('cookies',null)|length %}

    {% for cookie in cookiesSettingsGet('cookies') %}

        {% if setOnlyRequiredCookies|default(false) %}

            {% if cookie.required %}

                document.cookie = "{{ 'sg-cookies'~(sgCookiesLocalePrefix ? sgCookiesLocalePrefix)~'-'~( cookie.slug ? cookie.slug : loop.index)~'=1; path=/; expires='}}" + date.toGMTString();

            {% endif %}

        {% else %}

            {% if setOnlyManageCookies|default(false) %}

                var item = document.getElementById('sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix }}-{{cookie.slug}}{{customId|default('custom')}}');

                if( item.checked == true ) {
                    document.cookie = 'sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix }}-{{cookie.slug}}=1; path=/; expires=' + date.toGMTString();
                } else {
                    document.cookie = 'sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix}}-{{cookie.slug}}=0; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                }

            {% else %}

                document.cookie = "{{ 'sg-cookies'~(sgCookiesLocalePrefix ? sgCookiesLocalePrefix)~'-'~( cookie.slug ? cookie.slug : loop.index)~'=1; path=/; expires='}}" + date.toGMTString();

            {% endif %}

        {% endif %}

    {% endfor %}

    var necessary = false;
    var preferences = false;
    var statistics = false;
    var marketing = false;
    {% for cookie in cookiesSettingsGet('cookies') %}
        {% if cookie.slug == 'necessary' %}
            var necessaryItem = document.getElementById('sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix }}-{{cookie.slug }}-modal');
            if( necessaryItem.checked == true ) {
                necessary = true;
            }
        {% endif %}
        {% if cookie.slug == 'preferences' %}
            var preferencesItem = document.getElementById('sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix }}-{{cookie.slug }}-modal');
            if( preferencesItem.checked == true ) {
                preferences = true;
            }
        {% endif %}
        {% if cookie.slug == 'statistics' %}
            var statisticsItem = document.getElementById('sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix }}-{{cookie.slug }}-modal');
            if( statisticsItem.checked == true ) {
                statistics = true;
            }
        {% endif %}
        {% if cookie.slug == 'marketing' %}
            var marketingItem = document.getElementById('sg-cookies{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix }}-{{cookie.slug }}-modal');
            if( marketingItem.checked == true ) {
                marketing = true;
            }
        {% endif %}
    {% endfor %}

    dataLayer.push({
        'event':'submit_cookie_bar',
        'preferences_cookies':preferences,
        'analytics_cookies':statistics,
        'marketing_cookies':marketing
    })

    document.cookie = "CookieConsent=%7B%22necessary%22:" + necessary + "%2C%22preferences%22:" + preferences + "%2C%22statistics%22:" + statistics + "%2C%22marketing%22:" + marketing + "%7D; path=/; expires=" + date.toGMTString();
    {# "%7B%22necessary%22:" + necessary + "%2C%22preferences%22:" + preferences + "%2C%22statistics%22:" + statistics + "%2C%22marketing%22:" + marketing + "%7D; path=/; expires=" + date.toGMTString(); #}

{% endif %}
