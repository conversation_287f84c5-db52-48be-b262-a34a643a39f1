<section 
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %} 
    data-name="features-4-2"
    data-category="features" 
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}" 
    style="background-color: {{ box.background_color }};" 
    data-boxes-container 
    data-rel="boxes-wrapper">
    
	<div class="lg:max-w-7xl mx-auto space-y-8 xl:space-y-16">

        {% if box.title or box.subtitle or box.content %}
            <div class="px-4">
                {% partial 'atomic/molecules/content-heading-centered' box=box %}
            </div>
        {% endif %}
        
        <div class="
        flex flex-col gap-8
        md:grid md:grid-cols-2
        xl:grid-cols-12 xl:gap-10">

            {% set Items = box.cards_sm %}
            {% set halfLength = Items|length / 2 %}
            {% set midPoint = halfLength|round %}

            {% set leftGroup = Items|slice(0, midPoint) %}
            {% set rightGroup = Items|slice(midPoint) %}

            <div class="
            flex flex-col gap-8 px-4 order-1
            md:order-first
            lg:w-2/3 lg:ml-auto
            xl:w-full xl:ml-0 xl:flex xl:flex-col xl:col-span-3 xl:py-8 xl:order-first">

            {% for item in leftGroup %}
                <div class="">
                    {% partial 'atomic/molecules/cards/card-sm'
                        item=item
                        cardClass='items-center text-center '
                        iconClass="text-4xl dark:text-gray-200"
                        contentClass="prose-sm"
                        truncate=100 %}
                </div>
            {% endfor %}
            </div>

            <div class="
            flex h-full rounded overflow-hidden order-first pt-4
            md:col-span-2 md:order-last
            xl:col-span-6 xl:pt-0 xl:order-1">

                {% partial 'atomic/atoms/media/image' img=box.img title=box.img_title resize_w='640' class='w-full h-full object-cover' %}

            </div>

            <div class="
            flex flex-col gap-8 px-4 order-last
            md:order-1
            lg:w-2/3 lg:mr-auto
            xl:w-full xl:mr-0 xl:flex xl:flex-col xl:col-span-3 xl:py-8 xl:order-last">

            {% for item in rightGroup %}
                <div class="">
                    {% partial 'atomic/molecules/cards/card-sm'
                        item=item
                        cardClass='items-center text-center '
                        iconClass="text-4xl dark:text-gray-200"
                        contentClass="prose-sm"
                        truncate=100 %}
                </div>
            {% endfor %}
            </div>
            
        </div>
        
    </div>
	
</section>