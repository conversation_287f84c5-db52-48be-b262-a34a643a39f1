<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="features-3-1"
    data-category="features"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

    <div class="container space-y-8">

        {% if box.title or box.subtitle or box.content %}
            <div class="">
                {% partial 'atomic/molecules/content-heading-centered' box=box %}
            </div>
        {% endif %}

        <div class="flex flex-col justify-center md:flex-row md:flex-wrap">

            {% for item in box.cards_extended %}

                <div class="md:w-1/2 lg:w-1/3 md:px-4">

                    {% if item.media_type == 'icon' %}

                        {% partial 'atomic/molecules/cards/card-icon'
                            item=item
                            cardClass="items-center text-center md:px-4 md:py-8 lg:p-8"
                            iconClass="text-7xl"
                            cardContentClass="gap-4"
                            contentClass=""
                            truncate=200 %}

                    {% elseif item.media_type == 'image' %}

                        {% partial 'atomic/molecules/cards/card-img'
                            item=item
                            cardClass="items-center text-center overflow-hidden gap-8 pt-8"
                            contentClass=""
                            truncate=200 %}

                    {% else %}

                        {% partial 'atomic/molecules/cards/card'
                            item=item
                            cardClass="items-center text-center md:px-4 md:py-8"
                            cardContentClass="gap-4"
                            contentClass=""
                            buttonClass="flex items-center justify-center w-full text-center"
                            truncate=200 %}

                    {% endif %}

                </div>

            {% endfor %}

        </div>

    </div>

</section>