handle: features-9-1
name: "Kenmerken 9"
section: Kenmerken
icon: /app/assets/boxes/features/features-9.png
order: 190

spacing:
- general

form:
    tabs:
        fields:
            mixin-title-subtitle:
                type: mixin
                tab: Algemeen
            content:
                type: mixin
                tab: Algemeen
            feature_group:
                label: Kenmerken groep
                prompt: Voeg een groep toe
                type: repeater
                style: accordion
                tab: Algemeen
                titleFrom: label
                form:
                    fields:
                        label:
                            label: Label
                        img:
                            type: mixin
                        features_sm:
                            label: Kenmerken
                            type: repeater
                            prompt: Item toevoegen
                            itemsExpanded: false
                            titleFrom: title
                            form:
                                fields:
                                    mixin-title-single:
                                        type: mixin
                                    content_card:
                                        type: mixin
            background_color:
                type: mixin
                tab: Design
            flip-content:
                type: mixin
                tab: Design
            anchor:
                type: mixin
                tab: Design
            custom_css:
                type: mixin
                tab: Design
