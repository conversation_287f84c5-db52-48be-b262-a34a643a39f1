{% put styles %}
    <style>
        .testimonial-{{ box.id }}-slider-nav .slick-slide { opacity: .5; }
        .testimonial-{{ box.id }}-slider-nav .slick-slide.slick-current { opacity: 1; }

    </style>
{% endput %}

{% put scripts %}
    <script>
        $('.testimonial-{{ box.id }}-slider-slides').slick({
            slidesToShow: 1,
            slidesToScroll: 1,
            arrows: false,
            fade: true,
            autoplay: true,
            infinite: true,
        });
        $('.nav-item').on('click', function(){
            var key = $(this).data('key');
            $('.testimonial-{{ box.id }}-slider-slides').slick('slickGoTo', key)
        });
    </script>
{% endput %}


<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="testiminials-8"
    data-category="testimonials"
    class="testiminials {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    <div class="container">

        {% partial 'atomic/molecules/box-heading-center' text=box.title %}

        <div class="md:grid md:grid-cols-12 md:gap-8">
            <div class="md:col-span-10 md:col-start-2 xl:col-span-6 xl:col-start-4">
                <div class="testimonial-{{ box.id }}-slider-slides">

                    {% for key, item in box.slides %}
                        <div>
                            <figure>
                                <div class="flex justify-center gap-x-12 mb-12">
                                    {% for navKey, nav in box.slides %}
                                        <div data-key="{{ navKey }}" class="nav-item justify-self-center rounded-full border-2 cursor-pointer {{ navKey != key ? 'opacity-50 border-transparent' : 'border-gray-500 shadow'}}">
                                            {% partial 'atomic/atoms/media/image' img=nav.image title=nav.img_title resize_w="96" resize_h="96" resize_mode="crop" class="rounded-full" %}
                                        </div>
                                    {% endfor %}
                                </div>
                                <div role="blockquote" class="prose prose-tertiary dark:prose-primary_inverted max-w-none text-center">
                                    {{ item.text | content }}
                                </div>
                            </figure>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

    </div>
</section>
