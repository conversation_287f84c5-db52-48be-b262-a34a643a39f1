handle: content-4
name: Content 4
section: Content
icon: /app/assets/boxes/content/content-4.png
order: 140

spacing:
- general

form:
    tabs:
        fields:
            icon-ph:
                type: mixin
                tab: Algemeen
            mixin-title-single:
                type: mixin
                tab: Algemeen
            content:
                type: mixin
                tab: Algemeen
            _ruler1:
                type: ruler
                tab: Algemeen
            video:
                type: mixin
                tab: Algemeen
            _ruler2:
                type: ruler
                tab: Algemeen
            buttons:
                type: mixin
                tab: Algemeen
            background_color:
                type: mixin
                tab: Design
            _ruler4:
                type: ruler
                tab: Algemeen
            flip-content:
                type: mixin
                tab: Design
            _ruler5:
                type: ruler
                tab: Algemeen
            anchor:
                type: mixin
                tab: Design
            custom_css:
                type: mixin
                tab: Design

