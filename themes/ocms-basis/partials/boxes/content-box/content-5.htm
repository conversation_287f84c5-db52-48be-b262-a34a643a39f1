<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="{{ box.partial }}"
    data-category="{{ context.partial.config.section|lower }}"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

    <div class="container">

        <div class="flex flex-col gap-8 md:grid md:grid-cols-12 md:items-center">

            <div class="md:col-span-6 space-y-8 {{ box.flip_content ? 'order-last' : 'order-last md:order-first' }}">

                {% if box.title or box.subtitle or box.content %}
                    {% partial 'atomic/molecules/prose/prose-primary' body %}
                    {% partial 'atomic/molecules/content-heading' %}
                    {% partial 'atomic/molecules/content-section' content=box.content %}
                    {% endpartial %}
                {% endif %}

                {% if box.cards_sm %}
                    <div class="space-y-8 px-8 md:grid md:grid-cols-3 md:gap-8 md:px-0 md:space-y-0">
                        {% for item in box.cards_sm %}

                            {% partial 'atomic/molecules/cards/card-sm'
                                item=item
                                contentClass="line-clamp-3"
                                iconClass='text-4xl dark:text-gray-200' %}

                        {% endfor %}
                    </div>
                {% endif %}

                {% if box.buttons %}
                    {% partial 'atomic/molecules/buttons' buttons=box.buttons class="buttons-wrap" %}
                {% endif %}
            </div>

            <div class="md:col-span-6 {{ box.flip_content ? 'order-first' : 'order-first md:order-last' }}">
                <div class="">
                    {% partial 'atomic/atoms/media/image' img=box.img title=box.img_title resize_w="610" class="w-full rounded shadow" %}
                </div>
            </div>

        </div>

    </div>
</section>
