<section {% if box.anchor %}id="{{ box.anchor }}"{% endif %} data-name="content-2" data-category="content" class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="lg:max-w-7xl mx-auto md:px-4 lg:px-0">

        <div class="space-y-0.5 md:space-y-4 lg:space-y-0 lg:grid lg:grid-cols-2 lg:gap-x-0.5">
            {% for item in box.items %}
                <div class="aspect-[3/4] md:aspect-thumb lg:aspect-square relative">

                    {% partial 'atomic/atoms/media/image' img=item.img title=item.img_title resize_w="736" class="w-full h-full object-cover" %}

                    <div class="absolute inset-x-0 bottom-0 p-6">
                        <div class="md:w-2/3 mx-auto">

                            {% partial 'atomic/molecules/cards/card'
                                item=item
                                cardClass='items-center justify-center text-center bg-white rounded dark:bg-gray-600'
                                contentClass="line-clamp-3"
                                buttonClass="flex items-center justify-center w-full text-center" %}

                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

    </div>
</section>
