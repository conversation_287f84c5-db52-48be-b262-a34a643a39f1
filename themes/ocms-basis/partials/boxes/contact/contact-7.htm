<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="contact-7"
    data-category="contact"
    class="relative {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    <div class="relative">
        <div class="relative z-20 md:pb-8 lg:pb-16 pointer-events-none">
            <div class="container">
                <div class="md:grid md:grid-cols-12 md:gap-8">
                    <div class="md:col-span-8 md:col-start-3">
                        <div class="bg-white md:p-8 space-y-8 pointer-events-auto shadow-xl border">
                            {% if box.title or box.intro_text %}
                                <div class="flex items-center justify-center text-center max-w-xl mx-auto">
                                    <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                                        {% partial 'atomic/molecules/content-heading' %}
                                        {% partial 'atomic/molecules/content-section' content=box.intro_text %}
                                    </div>
                                </div>
                            {% endif %}
                            <div>
                                {% partial 'atomic/molecules/contact-info-small-center' %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="md:absolute inset-x-0 bottom-0 top-14 z-10 overflow-hidden bg-gray-100" id="google_maps_banner_iframe">
            {{ box.embed_code|raw ?: company.maps_embed_code|raw }}
        </div>
    </div>

</section>

{% put styles %}
    <style>
        #google_maps_banner_iframe iframe { width: 100%; height: 100%; }
    </style>
{% endput %}
