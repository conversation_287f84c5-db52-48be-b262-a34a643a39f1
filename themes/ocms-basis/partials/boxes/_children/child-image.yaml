handle: child-image
name: Afbeelding
section: Sub-blokken
icon: /plugins/offline/boxes/assets/img/boxes/image.svg
contexts:
- nested-boxes

form:
    tabs:
        fields:
            img:
                type: mixin
                tab: Algemeen
            alt:
                label: Alt-tag
                tab: Algemeen
            img_link:
                label: Afbeelding link
                type: pagefinder
                tab: Algemeen
            _ruler:
                type: ruler
                tab: Algemeen
            img_aspect:
                label: Afbeelding vorm
                type: dropdown
                default: original
                options:
                    original: Origineel
                    square: Vierkant
                    thumb: Thumbnail (4/3 verhouding)
                    video: Video (16/9 verhouding)
                    circle: Cirkel
                tab: Algemeen
            rounded_corners:
                label: Afgeronde hoeken
                type: dropdown
                default: none
                options:
                    none: Rechte hoeken
                    sm: Klein
                    md: Normaal
                    lg: Groot
                    xl: Extra groot
                trigger:
                    action: hide
                    field: img_aspect
                    condition: value[circle]
                tab: Algemeen
            shadow:
                label: Schaduw
                type: switch
                tab: Algemeen
            zoom:
                label: Afbeelding vergrootbaar maken
                type: switch
                tab: Algemeen
            custom_css:
                type: mixin
                tab: Algemeen
            col-span:
                type: mixin
                tab: Indeling