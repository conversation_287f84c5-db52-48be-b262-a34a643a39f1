
{% set parentBg = box.parent.bg_color %}

<div class="box-steps">
    <div class="content_section">
        <h2>{{ box.title }}</h2>
        {{ box.text | content }}
    </div>

    <div class="flex flex-col mt-10 box-steps-steplist">
        {% for item in box.steps %}
            <div class="flex w-full box-steps-step">
                <span class="w-1/6">
                    <div class="box-steps-orb-wrap">
                        <div class="box-steps-step-orb" style="background-color: {{ parentBg == 'transparent' ? '#ffffff' : parentBg }}">
                            {{ loop.index }}
                        </div>
                        {% if not loop.last %}
                            <div class="box-steps-step-line"></div>
                        {% endif %}
                    </div>
                </span>
                <span class="w-5/6 pb-12 pl-4">
                    <h4 class="box-steps-step-title">{{ item.title }}</h4>
                    <p class="box-steps-step-text">{{ item.text }}</p>
                </span>
            </div>
        {% endfor %}
    </div>

    {% if box.buttons %}
        <div class="button_wrapper flex">
            {% for button in box.buttons %}
                {% partial "ui/button" item = button %}
            {% endfor %}
        </div>
    {% endif %}
</div>
