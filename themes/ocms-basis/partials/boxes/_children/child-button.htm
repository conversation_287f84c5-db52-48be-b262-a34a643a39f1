{% if box.alignment == "right" %}
    {% set alignment = "justify-end" %}
{% elseif box.alignment == "center" %}
    {% set alignment = "justify-center" %}
{% else %}
    {% set alignment = "justify-start" %}
{% endif %}
<div data-rel="boxes-wrapper">
    {% if box.buttons %}
        <div class="flex space-x-2 space-y-3 {{ alignment }}">
            {% for button in box.buttons %}
                {% partial "ui/button" item = button %}
            {% endfor %}
        </div>
    {% endif %}
</div>
