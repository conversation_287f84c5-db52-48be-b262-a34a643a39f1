<section id="features-8" class="features" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="container">
        <div class="text-center section-heading">
            <h2>{{ box.title }}</h2>
        </div>

        <div class="features-card-grid row-grid md:grid-cols-3 lg:grid-cols-4 gap-8">
            {% for card in box.cards %}
                {% set hideclass = loop.index > 4 ? 'hide-mobile-flex ' %}
                {% partial "ui/cards/card" cssClass=hideclass ~ "bg-transparent p-4 gap-y-2" body %}
                <div class="card-icon card-icon-sm">
                    <div class="relative">
                        <i class="ph-fill {{ card.icon }} group-hover:scale-[1.15] transition"></i>
                    </div>
                </div>
                <h3 class="card-title">{{ card.title }}</h3>
                <div class="card-text content_section">
                    {{ card.content|content }}
                </div>
                {% endpartial %}
            {% endfor %}
        </div>
    </div>
</section>
