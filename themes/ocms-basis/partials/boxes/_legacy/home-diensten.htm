<div class="bg-white" data-boxes-container data-rel="boxes-wrapper">
  <div class="container">
    <div class="mx-auto max-w-2xl lg:mx-0">
      {% if box.title %}<h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">{{ box.title }}</h2>{% endif %}
      {% if box.content %}<div class="mt-6 text-lg leading-8 text-gray-600">{{ box.content | content }}</div>{% endif %}
    </div>
    <div class="mx-auto max-w-2xl {% if box.title and box.content %}mt-16 sm:mt-20 lg:mt-24 {% endif %}lg:max-w-none">
      <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">

        {% for item in box.services %}
            <div class="flex flex-col">
                <dt class="text-xl font-semibold leading-7 text-gray-900">
                    <a href="{{ item.url | link }}" class="mb-6 flex h-14 w-14 items-center justify-center rounded-lg bg-sky-500 hover:bg-sky-600">
                        <i class="fa-regular fa-{{ item.icon }} text-white text-3xl"></i>
                    </a>
                    {{ item.title }}
                </dt>
                <dd class="mt-1 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p class="flex-auto">{{ item.text }}</p>
                    <p class="mt-6">
                        <a href="{{ item.url | link }}" class="text-sm font-semibold leading-6 text-sky-500 hover:text-sky-600">{{ 'Meer lezen'|_ }} <span aria-hidden="true">→</span></a>
                    </p>
                </dd>
            </div>
        {% endfor %}


      </dl>
    </div>
  </div>
</div>
