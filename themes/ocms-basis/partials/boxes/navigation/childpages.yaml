handle: child-pages
name: Onder<PERSON>ggende pagina's
icon: /app/assets/boxes/navigation/child-pages.png
section: Navigatie
order: 120

spacing:
- general

form:
    tabs:
        fields:
            mixin-title-subtitle:
                type: mixin
                tab: Algemeen
            content:
                type: mixin
                tab: Algemeen
            style:
                label: Stijl
                type: balloon-selector
                options:
                    'buttons': 'Knoppen'
                    'cards': 'Kaarten'
                default: 'buttons'
                tab: Algemeen
            _tip:
                label: Let op
                comment: Zorg dat er bij elke onderliggende pagina afbeeldingen en introductieteksten zijn ingevuld.
                type: hint
                mode: warning
                tab: Algemeen
                trigger:
                    action: show
                    field: style
                    condition: value[cards]

            columns:
                label: Aantal items naast elkaar
                type: dropdown
                tab: Algemeen
                options:
                    2: '2'
                    3: '3'
                    4: '4'
                default: 4
            background_color:
                type: mixin
                tab: Design

