handle: blog-1-1
name: "Blog 1"
section: Blog
icon: /app/assets/boxes/blog/blog-1.png
order: 110

spacing:
    - general

form:
    tabs:
        fields:
            mixin-title-subtitle:
                type: mixin
                tab: Algemeen
            content:
                type: mixin
                tab: Algemeen
            category:
                label: Categorie filter
                type: dropdown
                emptyOption: '-- geen categorie filter --'
                optionsMethod: getBlogCategoryOptions
                tab: Algemeen
            max_items:
                label: Maximaal aantal items
                type: dropdown
                default: 3
                options:
                    3: 3
                    6: 6
                tab: Algemeen
            background_color:
                type: mixin
                tab: Design
            anchor:
                type: mixin
                tab: Design
            custom_css:
                type: mixin
                tab: Design
