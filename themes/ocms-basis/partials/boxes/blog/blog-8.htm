<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="blog-8"
    data-category="blog"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

    <div class="container space-y-8 xl:space-y-12">

        {% if box.title or box.subtitle or box.content %}
            <div class="">
                {% partial 'atomic/molecules/content-heading-centered' box=box %}
            </div>
        {% endif %}

        {% partial 'site/dynamic-blog' category=box.category blog_box="8" %}

        <div class="text-center mt-16">
            {% set buttonAlleBerichten = "Alle berichten"|_  %}

            {% partial 'ui/button'
                text=buttonAlleBerichten
                color="primary"
                type="filled"
                style="rounded"
                size="base"
                url=blogPage|link
                css_class="" %}
        </div>

    </div>

</section>