<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="benefits-2"
    data-category="benefits"
    class="benefits {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    data-boxes-container
    data-rel="boxes-wrapper">

    <div class="lg:max-w-7xl mx-auto xl:px-4">

        <div class="flex flex-col md:flex-row md:flex-wrap" style="background-color: {{ box.background_color }};">
            <div class="w-full md:w-1/2 py-12 md:py-8 px-8 xl:px-16 flex items-center {{ box.flip_content ? 'order-last' : 'order-last md:order-first' }}">
                <div>
                    {% if box.title or box.content %}
                        <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                            {% partial 'atomic/atoms/headings/header-h3' text=box.title %}
                            {% partial 'atomic/molecules/content-section' content=box.content truncate="430" %}
                        </div>
                    {% endif %}

                    {% if box.buttons %}
                        {% partial 'atomic/molecules/buttons' buttons=box.buttons class="buttons-wrap" %}
                    {% endif %}
                </div>
            </div>

            <div class="w-full md:w-1/2 {{ box.flip_content ? 'order-first' : 'order-first md:order-last' }}">
                <div class="aspect-video md:aspect-square xl:aspect-[5/4]">
                    {% partial 'atomic/atoms/media/image' img=box.img title=box.img_title resize_w="640" resize_mode="crop" class="w-full h-full object-cover" %}
                </div>
            </div>
        </div>

    </div>

</section>
