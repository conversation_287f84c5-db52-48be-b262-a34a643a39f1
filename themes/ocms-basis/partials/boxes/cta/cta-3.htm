<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="cta-3"
    data-category="call-to-actions"
    class="relative {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    <div class="max-w-7xl mx-auto md:px-4">
        <div class="space-y-12">
            <div class="px-4 md:px-0">
                {% if box.title or box.subtitle or box.content %}
                    {% partial 'atomic/molecules/content-heading-centered' box=box %}
                {% endif %}

                {% if box.buttons %}
                    {% partial 'atomic/molecules/buttons' buttons=box.buttons class="buttons-wrap justify-center" %}
                {% endif %}
            </div>

            {% if box.img %}
                <div class="lg:grid lg:grid-cols-12 lg:gap-8">
                    <div class="lg:col-span-10 lg:col-start-2 justify-self-center">
                        {% partial 'atomic/atoms/media/image' img=box.img title=box.img_title resize_w="1280" %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</section>
