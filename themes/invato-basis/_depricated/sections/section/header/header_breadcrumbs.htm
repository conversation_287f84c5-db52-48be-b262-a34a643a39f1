[staticBreadcrumbs]
==
<div class="bg-secondary-100">
    <div class="container max-w-screen-xl py-6 lg:py-12 xl:py-18">
        <h1 class="text-primary-500 font-bold text-lg md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl mb-4">{{ fields.title ? fields.title : this.page.title }}</h1>
        {% if fields.intro %}
        <div class="md:max-w-4xl lg:max-w-5xl mt-6">
            <div class="text-black text-xs lg:text-sm xl:text-base lg:leading-snug mt-6">
                {{ fields.intro }}
            </div>
        </div>
        {% endif %}
    </div>
    {% component 'staticBreadcrumbs' %}
</div>
