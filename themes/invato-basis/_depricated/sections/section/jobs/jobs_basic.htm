[collection jobList]
handle = "Jobs\Job"
==
<div class="py-8 lg:py-16" style="background-color: {{ fields.background_color }};">
    <div class="container">
        <div class="lg:grid lg:grid-cols-3 lg:gap-8">
            <div class="max-w-xl">
                <h3 class="text-base font-semibold tracking-wide text-primary-600 uppercase"><span class="mr-1">Working at:</span>{{ this.theme.company_name }}</h3>
                <h2 class="mt-2 text-2xl font-extrabold text-gray-900 sm:text-3xl lg:text-4xl">{{ fields.title }}</h2>
                <div class="mt-6 max-w-xl prose-sm">
                    {{ fields.content|content }}
                </div>
            </div>

            <div class="col-span-2">
                <div class="bg-white shadow-os shadow-black/30 overflow-hidden sm:rounded-md">
                    <ul role="list" class="divide-y divide-secondary-200">
                        {% for item in jobList %}
                        {% partial "jobs/item" item=item loop=loop %}
                        {% endfor %}
                    </ul>
                </div>
            </div>

        </div>
    </div>
</div>