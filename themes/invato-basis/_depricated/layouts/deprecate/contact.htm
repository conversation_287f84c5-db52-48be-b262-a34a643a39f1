description = "Contactpagina"

[staticPage]
useContent = 0
default = 0

[staticBreadcrumbs]

[formrender Contactformulier]
formCode = "contactformulier"

[sitePicker]
==
<!doctype html>
<html lang="nl">
    <head>
        {% partial "site/meta" %}
        {% partial "site/scripts_head" %}
        {% partial "page/scripts_head" %}
        {% partial "site/head" %}
        {% styles %}
    </head>
    <body>
        {% partial "site/scripts_body_top" %}
        {% partial "page/scripts_body_top" %}
        {% partial "page/navbars/" ~ this.theme.navbar|default('navbar-1') ~ ".htm" %}

        <main class="">
            <div class="bg-primary-600 text-white py-8 sm:py-16 lg:py-24">
                <div class="container">
                    <span class="text-base font-semibold tracking-wide text-primary-100 uppercase">Contact</span>
                    <h1 class="mb-0">{text tab="Pagina instellingen" name="page_title" label="Pagina titel" default="Vragen? Neem contact met ons op!"}Vragen? Neem contact met ons op!{/text}</h1>
                </div>
            </div>
            {% page  %}

            {% partial 'contact/default' %}
        </main>

        {% if this.theme.navbar == 'navbar-3' %}
            {variable tab="Flyout menu" name="snippet_title" type="text" label="Title" span="left" permissions="developer"}{/variable}
            {variable tab="Flyout menu" name="snippet" type="richeditor" size="huge" label="Snippet" span="left" toolbarButtons="snippets|html" permissions="developer"}{/variable}
        {% endif %}

{% partial "page/footers/" ~ this.theme.footer|default('footer-1') ~ ".htm" %}

        {% partial "page/cookie-alert" %}

        {repeater tab="Scripts" name="page_scripts_head" label="Scripts in de head" prompt="Script toevoegen" span="row" spanClass="col-sm-4" nameFrom="title"}
            {variable name="title" label="Titel" tab="Scripts" type="text"}{/variable}
            {variable name="script" label="Scripts in head" tab="Scripts" type="codeeditor" size="huge" language"javascript"}{/variable}
        {/repeater}
        {repeater tab="Scripts" name="page_scripts_body_top" label="Scripts in de body (boven)" prompt="Script toevoegen" span="row" spanClass="col-sm-4" nameFrom="title"}
            {variable name="title" label="Titel" tab="Scripts" type="text"}{/variable}
            {variable name="script" label="Scripts in body top" tab="Scripts" type="codeeditor" size="huge" language"javascript"}{/variable}
        {/repeater}
        {repeater tab="Scripts" name="page_scripts_body_bottom" label="Scripts in de body (onder)" prompt="Script toevoegen" span="row" spanClass="col-sm-4" nameFrom="title"}
            {variable name="title" label="Titel" tab="Scripts" type="text"}{/variable}
            {variable name="script" label="Scripts in body bottom" tab="Scripts" type="codeeditor" size="huge" language"javascript"}{/variable}
        {/repeater}

        {% partial "site/foot" %}
        {% partial "page/scripts_body_bottom" %}
        {% partial "site/scripts_body_bottom" %}

        {% scripts %}
        {% framework extras %}
    </body>
</html>
