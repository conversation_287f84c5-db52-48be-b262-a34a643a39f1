const defaultTheme = require('tailwindcss/defaultTheme')
const colors = require('tailwindcss/colors')

module.exports = {
    content: [
        './assets/js/*.js',
        '../../invato-basis/*.htm',
        '../../invato-basis/assets/js/*.js',
        './**/*.htm',
        '../../plugins/invato/**/*.htm',
        '../../plugins/invato/**/*.js',
        '../../plugins/instalweb/**/*.htm',
        '../../plugins/instalweb/**/*.js',
    ],
    safelist: [
        'oc-boxes-edit-mode'
    ],
    theme: {
        screens: {
            'sm': '640px',
            'md': '768px',
            'lg': '1024px',
            'xl': '1280px',
            '2xl': '1536px',
        },
        container: {
            center: true,
            padding: '1rem',
        },
        extend: {
            fontFamily: {
                sans: ['Inter', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                primary: colors.blue,
                secondary: colors.slate,
                gray: colors.gray,
                social: {
                    'facebook': '#3b5999',
                    'messenger': '#0084ff',
                    'twitter': '#55acee',
                    'tiktok': '#000000',
                    'github': '#000000',
                    'linkedin': '#0077b5',
                    'dropbox': '#007ee5',
                    'google-plus': '#dd4b39',
                    'pinterest': '#bd081c',
                    'youtube': '#cd201f',
                    'reddit': '#ff5700',
                    'yelp': '#af0606',
                    'producthunt': '#da552f',
                    'soundcloud': '#ff3300',
                    'blogger': '#f57d00',
                    'whatsapp': '#25d366',
                    'instagram': '#e4405f',
                    'dribbble': '#ea4c89',
                    'snapchat': '#fffc00',
                    'spotify': '#1db954',
                }
            },
        },
        aspectRatio: {
            auto: 'auto',
            square: '1 / 1',
            video: '16 / 9',
            thumb: '4 / 3',
            1: '1',
            2: '2',
            3: '3',
            4: '4',
            5: '5',
            6: '6',
            7: '7',
            8: '8',
            9: '9',
            10: '10',
            11: '11',
            12: '12',
            13: '13',
            14: '14',
            15: '15',
            16: '16',
          },
    },
    plugins: [
        require('@tailwindcss/aspect-ratio'),
        require('@tailwindcss/typography'),
        require('@tailwindcss/forms'),
    ],
}
