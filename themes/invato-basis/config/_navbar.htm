<?php
    use Illuminate\Support\Facades\File;
    use Illuminate\Support\Facades\View;
    use Cms\Classes\Theme;
    use October\Rain\Support\Collection;

    $theme = Theme::getActiveTheme()->getDirName();
    $listFiles = File::allFiles( themes_path( $theme ) . '/partials/page/navbars' );

    $items = [];
    foreach ($listFiles as $item) {
        $filename = $item->getFilename();
        if ( $filename != 'navbar-mobile.htm' && $filename != 'navbars.htm' ) {
            $items[] = str_replace('.htm', '', $filename);
        }
    }
    sort($items, SORT_NATURAL | SORT_FLAG_CASE);

    $navbarList = new Collection($items);
    $screenshotDir = '/themes/' . $theme . '/assets/img/screenshots/navbars/';
?>

<?php $navbarList->each( function($item, $key) use ($field, $value, $screenshotDir) { ?>
    <div class="config-option card">
        <label for="<?= $item ?>" class="">
            <input type="radio" name="<?= $field->getName() ?>" id="<?= $item; ?>" value="<?= $item ?>" <?php if ( $value === $item ) { ?>checked  <?php } ?> >

            <div class="config-option-screenshot">
                <img src="<?= $screenshotDir ?><?= $item ?>.png" alt="<?= $item ?>" style="max-width: 100%;" class="">
            </div>
        </label>
    </div>

<?php });?>

<style>
    .config-option { margin-bottom: 16px; }
    .config-option input { display: none; }
    .config-option-screenshot { display: inline-block; border-radius: 6px; opacity: .6; transform: scale(80%); transition: all .1s ease-in-out; cursor: pointer; }
    .config-option-screenshot:hover { opacity: 1; transform: scale(90%); }
    input:checked ~ .config-option-screenshot { opacity: 1; outline: 3px solid var(--bs-primary); transform: scale(100%); }
</style>
