## this.theme.company.extra_adres.* ##

fields:
    location:
        label: Adressen
        span: row
        spanClass: col-sm-12
        type: repeater
        titleFrom: adres_type
        prompt: Voeg een adres toe (max 2)
        style: accordion
        maxItems: 2
        form:
            fields:
                type:
                    label: Adres type/locatie
                    type: dropdown
                    placeholder: Ki<PERSON> een adres type
                    options:
                        postadres: Post adres
                        showroom: Showroom
                        bezoekadres: Bezoek adres
                        magazijn: Ma<PERSON><PERSON><PERSON>
                        anders-nl: <PERSON> nl;
                custom_type:
                    label: Aangepast adres type
                    trigger:
                        action: show
                        field: adres_type
                        condition: value[anders-nl]
                street:
                    label: Straatnaam
                    span: row
                    spanClass: col-sm-8
                housenumber:
                    label: Huisnummer
                    span: row
                    spanClass: col-sm-2
                housenumber_additive:
                    label: Toevoeging
                    span: row
                    spanClass: col-sm-2
                    tab: Bedrijfsgegevens
                zipcode:
                    label: Postcode
                    span: row
                    spanClass: col-sm-4
                city:
                    label: Plaats
                    span: row
                    spanClass: col-sm-8
                telephone:
                    label: Locatie telefoonnummer
                    span: row
                    spanClass: col-sm-5
                email:
                    label: Locatie e-mailadres
                    span: row
                    spanClass: col-sm-7
