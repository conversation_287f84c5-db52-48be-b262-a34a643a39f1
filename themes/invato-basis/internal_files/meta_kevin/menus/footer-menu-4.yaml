items:
    -
        title: First
        nesting: null
        type: url
        url: '#'
        code: ''
        reference: null
        cmsPage: null
        replace: null
        viewBag:
            isHidden: '0'
            icon: ''
            cssClass: ''
            isExternal: '0'
    -
        title: Second
        nesting: null
        type: url
        url: '#'
        code: ''
        reference: null
        cmsPage: null
        replace: null
        viewBag:
            isHidden: '0'
            icon: ''
            cssClass: ''
            isExternal: '0'
    -
        title: Third
        nesting: null
        type: url
        url: '#'
        code: ''
        reference: null
        cmsPage: null
        replace: null
        viewBag:
            isHidden: '0'
            icon: ''
            cssClass: ''
            isExternal: '0'
    -
        title: Fourth
        nesting: null
        type: url
        url: '#'
        code: ''
        reference: null
        cmsPage: null
        replace: null
        viewBag:
            isHidden: '0'
            icon: ''
            cssClass: ''
            isExternal: '0'
    -
        title: Fifth
        nesting: null
        type: url
        url: '#'
        code: ''
        reference: null
        cmsPage: null
        replace: null
        viewBag:
            isHidden: '0'
            icon: ''
            cssClass: ''
            isExternal: '0'
    -
        title: Sixth
        nesting: null
        type: url
        url: '#'
        code: ''
        reference: null
        cmsPage: null
        replace: null
        viewBag:
            isHidden: '0'
            icon: ''
            cssClass: ''
            isExternal: '0'
    -
        title: Seventh
        nesting: null
        type: url
        url: '#'
        code: ''
        reference: null
        cmsPage: null
        replace: null
        viewBag:
            isHidden: '0'
            icon: ''
            cssClass: ''
            isExternal: '0'
    -
        title: Eighth
        nesting: null
        type: url
        url: '#'
        code: ''
        reference: null
        cmsPage: null
        replace: null
        viewBag:
            isHidden: '0'
            icon: ''
            cssClass: ''
            isExternal: '0'
    -
        title: Ninth
        nesting: null
        type: url
        url: '#'
        code: ''
        reference: null
        cmsPage: null
        replace: null
        viewBag:
            isHidden: '0'
            icon: ''
            cssClass: ''
            isExternal: '0'
    -
        title: Tenth
        nesting: null
        type: url
        url: '#'
        code: ''
        reference: null
        cmsPage: null
        replace: null
        viewBag:
            isHidden: '0'
            icon: ''
            cssClass: ''
            isExternal: '0'
name: 'Quaternary footer menu'
