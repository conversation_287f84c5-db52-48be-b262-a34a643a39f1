{% if this.theme.social.media %}
<div class="flex justify-center px-3 md:px-5 ">
    <ul class="flex flex-wrap md:flex-nowrap justify-center space-x-2 md:space-x-8">
        {% for item in this.theme.social.media %}

        {% if item.title == 'facebook' %}
        {% set brandcolor = "text-social-facebook hover:text-gray-500" %}
        {% elseif item.title == 'instagram' %}
        {% set brandcolor = "text-social-instagram hover:text-gray-500" %}
        {% elseif item.title == 'snapchat' %}
        {% set brandcolor = "text-gray-500 hover:text-social-snapchat" %}
        {% elseif item.title == 'tiktok' %}
        {% set brandcolor = "text-social-tiktok hover:text-gray-500" %}
        {% elseif item.title == 'youtube' %}
        {% set brandcolor = "text-social-youtube hover:text-gray-500" %}
        {% elseif item.title == 'linkedin' %}
        {% set brandcolor = "text-social-linkedin hover:text-gray-500" %}
        {% elseif item.title == 'github' %}
        {% set brandcolor = "text-social-github hover:text-gray-500" %}
        {% else %}
        {% set brandcolor = "text-white hover:text-gray-500" %}
        {% endif %}

        <li class="">
            <a href="{{ item.slug }}" target="_blank">
                <span class="sr-only">{{ item.title }}</span>
                <i class="fab fa-{{ item.title }} text-2xl {{ brandcolor }}"></i>
            </a>
        </li>
        {% endfor %}
    </ul>
</div>
{% endif %}
