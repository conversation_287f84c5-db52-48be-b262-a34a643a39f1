<section id="features-3" class="features" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="container">
        <div class="text-center">
            <h2>{{ box.title }}</h2>
        </div>

        <div class="features-card-grid row-grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for card in box.cards %}
                {% set hideclass = loop.index > 4 ? 'hide-mobile-flex ' %}
                    {% partial "ui/cards/card" cssClass=hideclass ~ "items-center text-center" body %}
                        <div class="card-icon">
                            <div class="relative">
                                <i class="{{ card.icon }} group-hover:scale-[1.15] transition"></i>
                            </div>
                        </div>
                        <h3 class="card-title">{{ card.title }}</h3>
                        <div class="card-text content_section">
                            {{ card.content|content }}
                        </div>
                    {% if card.btn_text %}
                        <div class="card-buttons">
                            {% partial 'ui/button' icon=card.btn_icon text=card.btn_text url=card.btn_url style=card.btn_style size=card.btn_size target_blank=btn_target_blank %}
                        </div>
                    {% endif %}
                {% endpartial %}
            {% endfor %}
        </div>
    </div>
</section>