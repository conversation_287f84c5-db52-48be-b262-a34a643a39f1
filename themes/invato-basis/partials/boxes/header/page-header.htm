<div class="relative overflow-hidden bg-white" data-boxes-container data-rel="boxes-wrapper">
    <div class="container">
        <div class="relative z-10 bg-white py-8 sm:py-16 md:py-20 lg:w-full lg:max-w-2xl lg:py-28 xl:py-32">
            <svg class="absolute inset-y-0 right-0 hidden h-full w-48 translate-x-24 2xl:translate-x-44 transform text-white lg:block" fill="currentColor" viewBox="0 0 100 100" preserveAspectRatio="none" aria-hidden="true">
                <polygon points="50,0 100,0 50,100 0,100" />
            </svg>

            <main class="container px-4 sm:px-6 lg:px-8">
                <div class="sm:text-center lg:text-left">
                    <h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
                        <span class="block xl:inline">{{ box.title }}</span>
                        {% if box.title_style == 'multi_color' %}
                        <span class="block text-primary-600 xl:inline">{{ box.title_color }}</span>
                        {% endif %}
                    </h1>
                    <div class="mt-3 text-base text-gray-500 sm:mx-auto sm:mt-5 sm:max-w-xl sm:text-lg md:mt-5 md:text-xl lg:mx-0">{{ box.content|raw }}</div>
                </div>
            </main>

        </div>
    </div>
    <div class="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
        <img class="h-56 w-full object-cover sm:h-72 md:h-96 lg:h-full lg:w-full" src="{{ ( box.img | media | resize(1200) ) }}" alt="">
    </div>
</div>
