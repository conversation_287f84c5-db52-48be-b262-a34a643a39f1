<section id="content-9" class="content relative" data-boxes-container data-rel="boxes-wrapper">
    <div class="absolute h-full w-full overflow-hidden">
        <img src="{{ box.background_img|media }}" class="w-full h-full object-cover" alt="">
    </div>
    <div class="absolute w-full h-full">
        <div class="bg-gradient-to-l from-[#002E50]/90 to-[#002E50]/80 w-full h-full"></div>
    </div>
    <div class="container relative h-full text-white pb-8 md:pb-64 lg:pb-28 mb-40">
        <div class="flex flex-col h-full lg:w-1/2 space-y-6 lg:space-y-12">
            <div class="space-y-4 lg:space-y-8">
                <div class="title mt-16 md:mt-20 lg:mt-28">
                    <H2 class="text-white">{{ box.title }}</H2>
                </div>
                <div class="content_section text-lg">
                    {{ box.content|content }}
                </div>
            </div>
            <div class="space-y-6">
                {% for benefit in box.benefits %}
                {% partial 'ui/benefits/benefit-points' icon=benefit.icon name=benefit.name %}
                {% endfor %}
            </div>
            {% if box.buttons %}
            <div class="z-10 pt-6">
                {% for button in box.buttons %}
                {% partial 'ui/button' url=button.url text=button.text btnSize=button.size btnStyle=button.style
                target_blank=button.target_blank icon=button.icon %}
                {% endfor %}
            </div>
            {% endif %}
        </div>
        <div class="md:absolute md:-bottom-28 lg:right-0 md:flex md:justify-end pt-12 lg:pt-0">
            <div class="flex flex-col space-y-4 bg-gray-200 md:w-2/3 lg:w-1/2 xl:w-2/5 p-6 md:mr-8">
                <div class="title">
                    <h3 class="">{{ box.card_title }}</h3>
                </div>
                <div class="content_section text-lg text-gray-800">
                    {{ box.card_content|content }}
                </div>
                {% if box.buttons %}
                <div class="button flex justify-end">
                    {% for button in box.buttons %}
                    {% partial 'ui/button' url=button.url text=button.text btnSize=button.size btnStyle=button.style
                    target_blank=button.target_blank icon=button.icon %}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>