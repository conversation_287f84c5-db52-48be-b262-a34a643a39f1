{% if box.text_align == 'center' %}
    {% set text_align = 'text-center' %}
{% elseif box.text_align == 'right' %}
    {% set text_align = 'text-right' %}
{% else %}
    {% set text_align = 'text-left' %}
{% endif %}

<div class="card bg-white rounded-lg border shadow-xl overflow-hidden {{ text_align }}">

    <div class="flex justify-center {{ box.isIcon ? 'pt-6' }}">
        {% if box.isIcon %}
            <img src="{{ box.image | media }}" alt="{{ box.image.title }}">
        {% else %}
            <img src="{{ box.image | media | resize(768, null) }}" alt="{{ box.image.title }}">
        {% endif %}
    </div>

    <div class="p-6">
        <h3>{{ box.title }}</h3>
        <div class="content_section">
            {{ box.text | content }}
        </div>
        {% if box.buttons %}
            <div class="button_wrapper flex">
                {% for button in box.buttons %}
                    {% partial 'ui/button' url=button.url size=button.size style=button.style icon=button.icon
                    text=button.text target_blank=button.target_blank %}
                {% endfor %}
            </div>
        {% endif %}
    </div>
</div>
