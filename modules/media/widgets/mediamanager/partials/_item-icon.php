<div class="icon-container <?= $itemType ?>">
    <div class="icon-wrapper"><i class="<?= $this->itemTypeToIconClass($item, $itemType) ?>"></i></div>

    <?php if ($itemType == Media\Classes\MediaLibraryItem::FILE_TYPE_IMAGE):
        $thumbnailPath = $this->thumbnailExists($thumbnailParams, $item->path, $item->lastModified);
    ?>
        <div>
            <?php if (!$thumbnailPath): ?>
                <div class="image-placeholder"
                    data-width="<?= $thumbnailParams['width'] ?>"
                    data-height="<?= $thumbnailParams['height'] ?>"
                    data-path="<?= e($item->path) ?>"
                    data-last-modified="<?= $item->lastModified ?>"
                    id="<?= $this->getPlaceholderId($item) ?>"
                >
                    <div class="icon-wrapper"><i class="<?= $this->itemTypeToIconClass($item, $itemType) ?>"></i></div>
                </div>
            <?php else: ?>
                <?= $this->makePartial('thumbnail-image', [
                    'isError' => $this->thumbnailIsError($thumbnailPath),
                    'imageUrl' => $this->getThumbnailImageUrl($thumbnailPath)
                ]) ?>
            <?php endif ?>
        </div>
    <?php endif ?>
</div>
