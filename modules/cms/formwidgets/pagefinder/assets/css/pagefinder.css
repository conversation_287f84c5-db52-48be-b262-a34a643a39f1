.field-pagefinder .pagefinder-control-container{background:var(--oc-form-control-bg);border:1px solid var(--bs-border-color);border-radius:4px}.field-pagefinder .pagefinder-control-toolbar{font-size:0;padding:3px;white-space:nowrap}.field-pagefinder .pagefinder-control-toolbar .backend-toolbar-button.populated-only{display:none}.field-pagefinder .pagefinder-record-container{position:relative}.field-pagefinder .record-item{display:block;margin:4px;outline:none;position:relative}.field-pagefinder .record-item .record-data-container{padding:4px 3px 3px}.field-pagefinder .record-item .record-data-container-inner{position:relative}.field-pagefinder .record-item .icon-container,.field-pagefinder .record-item .info,.field-pagefinder .record-item .info h4,.field-pagefinder .record-item .info h4 p{display:inline-block;vertical-align:top}.field-pagefinder .record-item .icon-container{height:20px;margin-top:1px;position:absolute;width:20px}.field-pagefinder .record-item .icon-container i{left:3px;position:relative;top:2px}.field-pagefinder .record-item .info{padding-left:22px}.field-pagefinder .record-item .description,.field-pagefinder .record-item .recordname{color:var(--bs-body-color);font-size:14px;margin-bottom:0;word-break:break-all}.field-pagefinder .record-item .description{color:var(--bs-secondary-color);font-size:.9rem}
