/*
 * This file has been compiled from: /modules/system/lang/pl/client.php
 */
if (!window.oc) {
    window.oc = {};
}

if (!window.oc.langMessages) {
    window.oc.langMessages = {};
}

window.oc.langMessages['pl'] = $.extend(
    window.oc.langMessages['pl'] || {},
    {"markdowneditor":{"formatting":"Formaty","quote":"Cy<PERSON>","code":"Widok kod","header1":"Nag\u0142\u00f3wek 1","header2":"Nag\u0142\u00f3wek 2","header3":"Nag\u0142\u00f3wek 3","header4":"Nag\u0142\u00f3wek 4","header5":"Nag\u0142\u00f3wek 5","header6":"Nag\u0142\u00f3wek 6","bold":"Pogrubienie","italic":"<PERSON><PERSON><PERSON><PERSON>","unorderedlist":"\"Lista nieuporz\u0105dkowana","orderedlist":"Uporz\u0105dkowana lista","snippet":"Snippet","video":"Wideo","image":"Obrazek","link":"Link","horizontalrule":"Wstaw lini\u0119 poziom\u0105","fullscreen":"Pe\u0142ny ekran","preview":"Podgl\u0105d","strikethrough":"Strikethrough","cleanblock":"Clean Block","table":"Table","sidebyside":"Side by Side"},"mediamanager":{"insert_link":"Wstaw Link","insert_image":"Wstaw Obraz","insert_video":"Wstaw Wideo","insert_audio":"Wstaw Audio","invalid_file_empty_insert":"Prosimy wybra\u0107 plik do podlinkowania.","invalid_file_single_insert":"Prosimy wybra\u0107 pojedynczy plik.","invalid_image_empty_insert":"Prosimy wybra\u0107 obrazy do wstawienia.","invalid_video_empty_insert":"Prosimy wybra\u0107 wideo do wstawienia.","invalid_audio_empty_insert":"Prosimy wybra\u0107 audio do wstawienia."},"alert":{"error":"Error","confirm":"Confirm","dismiss":"Dismiss","confirm_button_text":"OK","cancel_button_text":"Anuluj","widget_remove_confirm":"Remove this widget?"},"datepicker":{"previousMonth":"Poprzedni miesi\u0105c","nextMonth":"Nast\u0119pny miesi\u0105c","months":["Stycze\u0144","Luty","Marzec","Kwiecie\u0144","Maj","Czerwiec","Lipiec","Sierpie\u0144","Wrzesie\u0144","Pa\u017adziernik","Listopad","Grudzie\u0144"],"weekdays":["Niedziela","Poniedzia\u0142ek","Wtorek","\u015aroda","Czwartek","Pi\u0105tek","Sobota"],"weekdaysShort":["Nie","Pn","Wt","\u015ar","Czw","Pt","So"]},"colorpicker":{"choose":"OK"},"filter":{"group":{"all":"wszystkie"},"scopes":{"apply_button_text":"Apply","clear_button_text":"Clear"},"dates":{"all":"wszystkie","filter_button_text":"Filtruj","reset_button_text":"Resetuj","date_placeholder":"Data","after_placeholder":"Po","before_placeholder":"Przed"},"numbers":{"all":"all","filter_button_text":"Filter","reset_button_text":"Reset","min_placeholder":"Min","max_placeholder":"Max"}},"eventlog":{"show_stacktrace":"Poka\u017c stos wywo\u0142a\u0144","hide_stacktrace":"Ukryj stos wywo\u0142a\u0144","tabs":{"formatted":"Sformatowany","raw":"Nieprzetworzony"},"editor":{"title":"Edytor kodu \u017ar\u00f3d\u0142owego","description":"Tw\u00f3j system operacyjny powinien by\u0107 skonfigurowany aby nas\u0142uchiwa\u0107 na jednym z podanych schemat\u00f3w URL.","openWith":"Otw\u00f3rz za pomoc\u0105","remember_choice":"Zapami\u0119taj wybran\u0105 opcj\u0119 dla tej sesji","open":"Otw\u00f3rz","cancel":"Anuluj"}},"upload":{"max_files":"You can not upload any more files.","invalid_file_type":"You can't upload files of this type.","file_too_big":"File is too big ({{filesize}}MB). Max filesize: {{maxFilesize}}MB.","response_error":"Server responded with {{statusCode}} code.","remove_file":"Remove file"},"inspector":{"add":"Add","remove":"Remove","key":"Key","value":"Value","ok":"OK","cancel":"Cancel","items":"Items"}}
);


//! moment.js locale configuration v2.22.2

;(function (global, factory) {
   typeof exports === 'object' && typeof module !== 'undefined'
       && typeof require === 'function' ? factory(require('../moment')) :
   typeof define === 'function' && define.amd ? define(['../moment'], factory) :
   factory(global.moment)
}(this, (function (moment) { 'use strict';


    var monthsNominative = 'styczeń_luty_marzec_kwiecień_maj_czerwiec_lipiec_sierpień_wrzesień_październik_listopad_grudzień'.split('_'),
        monthsSubjective = 'stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_września_października_listopada_grudnia'.split('_');
    function plural(n) {
        return (n % 10 < 5) && (n % 10 > 1) && ((~~(n / 10) % 10) !== 1);
    }
    function translate(number, withoutSuffix, key) {
        var result = number + ' ';
        switch (key) {
            case 'ss':
                return result + (plural(number) ? 'sekundy' : 'sekund');
            case 'm':
                return withoutSuffix ? 'minuta' : 'minutę';
            case 'mm':
                return result + (plural(number) ? 'minuty' : 'minut');
            case 'h':
                return withoutSuffix  ? 'godzina'  : 'godzinę';
            case 'hh':
                return result + (plural(number) ? 'godziny' : 'godzin');
            case 'MM':
                return result + (plural(number) ? 'miesiące' : 'miesięcy');
            case 'yy':
                return result + (plural(number) ? 'lata' : 'lat');
        }
    }

    var pl = moment.defineLocale('pl', {
        months : function (momentToFormat, format) {
            if (!momentToFormat) {
                return monthsNominative;
            } else if (format === '') {
                // Hack: if format empty we know this is used to generate
                // RegExp by moment. Give then back both valid forms of months
                // in RegExp ready format.
                return '(' + monthsSubjective[momentToFormat.month()] + '|' + monthsNominative[momentToFormat.month()] + ')';
            } else if (/D MMMM/.test(format)) {
                return monthsSubjective[momentToFormat.month()];
            } else {
                return monthsNominative[momentToFormat.month()];
            }
        },
        monthsShort : 'sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paź_lis_gru'.split('_'),
        weekdays : 'niedziela_poniedziałek_wtorek_środa_czwartek_piątek_sobota'.split('_'),
        weekdaysShort : 'ndz_pon_wt_śr_czw_pt_sob'.split('_'),
        weekdaysMin : 'Nd_Pn_Wt_Śr_Cz_Pt_So'.split('_'),
        longDateFormat : {
            LT : 'HH:mm',
            LTS : 'HH:mm:ss',
            L : 'DD.MM.YYYY',
            LL : 'D MMMM YYYY',
            LLL : 'D MMMM YYYY HH:mm',
            LLLL : 'dddd, D MMMM YYYY HH:mm'
        },
        calendar : {
            sameDay: '[Dziś o] LT',
            nextDay: '[Jutro o] LT',
            nextWeek: function () {
                switch (this.day()) {
                    case 0:
                        return '[W niedzielę o] LT';

                    case 2:
                        return '[We wtorek o] LT';

                    case 3:
                        return '[W środę o] LT';

                    case 6:
                        return '[W sobotę o] LT';

                    default:
                        return '[W] dddd [o] LT';
                }
            },
            lastDay: '[Wczoraj o] LT',
            lastWeek: function () {
                switch (this.day()) {
                    case 0:
                        return '[W zeszłą niedzielę o] LT';
                    case 3:
                        return '[W zeszłą środę o] LT';
                    case 6:
                        return '[W zeszłą sobotę o] LT';
                    default:
                        return '[W zeszły] dddd [o] LT';
                }
            },
            sameElse: 'L'
        },
        relativeTime : {
            future : 'za %s',
            past : '%s temu',
            s : 'kilka sekund',
            ss : translate,
            m : translate,
            mm : translate,
            h : translate,
            hh : translate,
            d : '1 dzień',
            dd : '%d dni',
            M : 'miesiąc',
            MM : translate,
            y : 'rok',
            yy : translate
        },
        dayOfMonthOrdinalParse: /\d{1,2}\./,
        ordinal : '%d.',
        week : {
            dow : 1, // Monday is the first day of the week.
            doy : 4  // The week that contains Jan 4th is the first week of the year.
        }
    });

    return pl;

})));


/*! Select2 4.1.0-rc.0 | https://github.com/select2/select2/blob/master/LICENSE.md */

!function(){if(jQuery&&jQuery.fn&&jQuery.fn.select2&&jQuery.fn.select2.amd)var n=jQuery.fn.select2.amd;n.define("select2/i18n/pl",[],function(){var n=["znak","znaki","znaków"],e=["element","elementy","elementów"],r=function(n,e){return 1===n?e[0]:n>1&&n<=4?e[1]:n>=5?e[2]:void 0};return{errorLoading:function(){return"Nie można załadować wyników."},inputTooLong:function(e){var t=e.input.length-e.maximum;return"Usuń "+t+" "+r(t,n)},inputTooShort:function(e){var t=e.minimum-e.input.length;return"Podaj przynajmniej "+t+" "+r(t,n)},loadingMore:function(){return"Trwa ładowanie…"},maximumSelected:function(n){return"Możesz zaznaczyć tylko "+n.maximum+" "+r(n.maximum,e)},noResults:function(){return"Brak wyników"},searching:function(){return"Trwa wyszukiwanie…"},removeAllItems:function(){return"Usuń wszystkie przedmioty"}}}),n.define,n.require}();

/*!
 * Froala Editor for October CMS
 */

(function (factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['jquery'], factory);
    } else if (typeof module === 'object' && module.exports) {
        // Node/CommonJS
        module.exports = function( root, jQuery ) {
            if ( jQuery === undefined ) {
                // require('jQuery') returns a factory that requires window to
                // build a jQuery instance, we normalize how we use modules
                // that require this pattern but the window provided is a noop
                // if it's defined (how jquery works)
                if ( typeof window !== 'undefined' ) {
                    jQuery = require('jquery');
                }
                else {
                    jQuery = require('jquery')(root);
                }
            }
            return factory(jQuery);
        };
    } else {
        // Browser globals
        factory(window.jQuery);
    }
}(function ($) {
/**
 * Polish
 */

if (!$.FE_LANGUAGE) {
    $.FE_LANGUAGE = {};
}

$.FE_LANGUAGE['pl'] = {
  translation: {
    // Place holder
    "Type something": "Wpisz co\u015b",

    // Basic formatting
    "Bold": "Pogrubienie",
    "Italic": "Kursywa",
    "Underline": "Podkre\u015blenie",
    "Strikethrough": "Przekre\u015blenie",

    // Main buttons
    "Insert": "Wstaw",
    "Delete": "Usun\u0105\u0107",
    "Cancel": "Anuluj",
    "OK": "Ok",
    "Back": "Plecy",
    "Remove": "Usun\u0105\u0107",
    "More": "Jeszcze",
    "Update": "Aktualizacja",
    "Style": "Styl",

    // Font
    "Font Family": "Kr\u00f3j czcionki",
    "Font Size": "Rozmiar czcionki",

    // Colors
    "Colors": "Kolory",
    "Background": "T\u0142o",
    "Text": "Tekstu",
    "HEX Color": "Sześciokąt",

    // Paragraphs
    "Paragraph Format": "Formaty",
    "Normal": "Normalny",
    "Code": "Kod \u017ar\u00f3d\u0142owy",
    "Heading 1": "Nag\u0142\u00f3wek 1",
    "Heading 2": "Nag\u0142\u00f3wek 2",
    "Heading 3": "Nag\u0142\u00f3wek 3",
    "Heading 4": "Nag\u0142\u00f3wek 4",

    // Style
    "Paragraph Style": "Styl akapitu",
    "Inline Style": "Stylu zgodna",

    // Alignment
    "Align": "Wyr\u00f3wnaj",
    "Align Left": "Wyr\u00f3wnaj do lewej",
    "Align Center": "Wyr\u00f3wnaj do \u015brodka",
    "Align Right": "Wyr\u00f3wnaj do prawej",
    "Align Justify": "Do lewej i prawej",
    "None": "\u017baden",

    // Lists
    "Ordered List": "Uporz\u0105dkowana lista",
    "Default": "Domyślna",
    "Lower Alpha": "Niższy alfa",
    "Lower Greek": "Niższy grecki",
    "Lower Roman": "Niższe rzymskie",
    "Upper Alpha": "Górna alfa",
    "Upper Roman": "Górny rzymski",

    "Unordered List": "Lista nieuporz\u0105dkowana",
    "Circle": "Okrąg",
    "Disc": "Dysk",
    "Square": "Plac",

    // Line height
    "Line Height": "Wysokość linii",
    "Single": "Pojedynczy",
    "Double": "Podwójnie",

    // Indent
    "Decrease Indent": "Zmniejsz wci\u0119cie",
    "Increase Indent": "Zwi\u0119ksz wci\u0119cie",

    // Links
    "Insert Link": "Wstaw link",
    "Open in new tab": "Otw\u00f3rz w nowej karcie",
    "Open Link": "Otw\u00f3rz link",
    "Edit Link": "Link edytuj",
    "Unlink": "Usu\u0144 link",
    "Choose Link": "Wybierz link",

    // Images
    "Insert Image": "Wstaw obrazek",
    "Upload Image": "Za\u0142aduj obrazek",
    "By URL": "Przez URL",
    "Browse": "Przegl\u0105danie",
    "Drop image": "Upu\u015bci\u0107 obraz",
    "or click": "lub kliknij",
    "Manage Images": "Zarz\u0105dzanie zdj\u0119ciami",
    "Loading": "\u0141adowanie",
    "Deleting": "Usuwanie",
    "Tags": "Tagi",
    "Are you sure? Image will be deleted.": "Czy na pewno? Obraz zostanie skasowany.",
    "Replace": "Zast\u0105pi\u0107",
    "Uploading": "Zamieszczanie",
    "Loading image": "\u0141adowanie obrazek",
    "Display": "Wystawa",
    "Inline": "Zgodna",
    "Break Text": "Z\u0142ama\u0107 tekst",
    "Alternative Text": "Tekst alternatywny",
    "Change Size": "Zmie\u0144 rozmiar",
    "Width": "Szeroko\u015b\u0107",
    "Height": "Wysoko\u015b\u0107",
    "Something went wrong. Please try again.": "Co\u015b posz\u0142o nie tak. Prosz\u0119 spr\u00f3buj ponownie.",
    "Image Caption": "Podpis obrazu",
    "Advanced Edit": "Zaawansowana edycja",

    // Video
    "Insert Video": "Wstaw wideo",
    "Embedded Code": "Kod osadzone",
    "Paste in a video URL": "Wklej adres URL filmu",
    "Drop video": "Upuść wideo",
    "Your browser does not support HTML5 video.": "Twoja przeglądarka nie obsługuje wideo html5.",
    "Upload Video": "Prześlij wideo",

    // Tables
    "Insert Table": "Wstaw tabel\u0119",
    "Table Header": "Nag\u0142\u00f3wek tabeli",
    "Remove Table": "Usu\u0144 tabel\u0119",
    "Table Style": "Styl tabeli",
    "Horizontal Align": "Wyr\u00f3wnaj poziomy",
    "Row": "Wiersz",
    "Insert row above": "Wstaw wiersz przed",
    "Insert row below": "Wstaw wiersz po",
    "Delete row": "Usu\u0144 wiersz",
    "Column": "Kolumna",
    "Insert column before": "Wstaw kolumn\u0119 przed",
    "Insert column after": "Wstaw kolumn\u0119 po",
    "Delete column": "Usu\u0144 kolumn\u0119",
    "Cell": "Kom\u00f3rka",
    "Merge cells": "\u0141\u0105cz kom\u00f3rki",
    "Horizontal split": "Podzia\u0142 poziomy",
    "Vertical split": "Podzia\u0142 pionowy",
    "Cell Background": "T\u0142a kom\u00f3rek",
    "Vertical Align": "Pionowe wyr\u00f3wnanie",
    "Top": "Top",
    "Middle": "\u015arodkowy",
    "Bottom": "Dno",
    "Align Top": "Wyr\u00f3wnaj do g\u00f3ry",
    "Align Middle": "Wyr\u00f3wnaj \u015brodku",
    "Align Bottom": "Wyr\u00f3wnaj do do\u0142u",
    "Cell Style": "Styl kom\u00f3rki",

    // Files
    "Upload File": "Prze\u015blij plik",
    "Drop file": "Upu\u015bci\u0107 plik",

    // Emoticons
    "Emoticons": "Emotikony",
    "Grinning face": "Z u\u015bmiechem twarz",
    "Grinning face with smiling eyes": "Z u\u015bmiechem twarz z u\u015bmiechni\u0119tymi oczami",
    "Face with tears of joy": "Twarz ze \u0142zami rado\u015bci",
    "Smiling face with open mouth": "U\u015bmiechni\u0119ta twarz z otwartymi ustami",
    "Smiling face with open mouth and smiling eyes": "U\u015bmiechni\u0119ta twarz z otwartymi ustami i u\u015bmiechni\u0119te oczy",
    "Smiling face with open mouth and cold sweat": "U\u015bmiechni\u0119ta twarz z otwartymi ustami i zimny pot",
    "Smiling face with open mouth and tightly-closed eyes": "U\u015bmiechni\u0119ta twarz z otwartymi ustami i szczelnie zamkni\u0119tych oczu",
    "Smiling face with halo": "U\u015bmiechni\u0119ta twarz z halo",
    "Smiling face with horns": "U\u015bmiechni\u0119ta twarz z rogami",
    "Winking face": "Mrugaj\u0105ca twarz",
    "Smiling face with smiling eyes": "U\u015bmiechni\u0119ta twarz z u\u015bmiechni\u0119tymi oczami",
    "Face savoring delicious food": "Twarz smakuj\u0105 c pyszne jedzenie",
    "Relieved face": "Z ulg\u0105  twarz",
    "Smiling face with heart-shaped eyes": "U\u015bmiechni\u0119ta twarz z oczami w kszta\u0142cie serca",
    "Smiling face with sunglasses": "U\u015bmiechni\u0119ta twarz z okulary",
    "Smirking face": "Zadowolony z siebie twarz",
    "Neutral face": "Neutralny twarzy",
    "Expressionless face": "Bezwyrazowy twarzy",
    "Unamused face": "Nie rozbawiony twarzy",
    "Face with cold sweat": "Zimny pot z twarzy",
    "Pensive face": "Zamy\u015blona twarz",
    "Confused face": "Myli\u0107 twarzy",
    "Confounded face": "Ha\u0144ba twarz",
    "Kissing face": "Ca\u0142owanie twarz",
    "Face throwing a kiss": "Twarz rzucaj\u0105c poca\u0142unek",
    "Kissing face with smiling eyes": "Ca\u0142owanie twarz z u\u015bmiechni\u0119tymi oczami",
    "Kissing face with closed eyes": "Ca\u0142owanie twarz z zamkni\u0119tymi oczami",
    "Face with stuck out tongue": "Twarz z j\u0119zyka stercza\u0142y",
    "Face with stuck out tongue and winking eye": "Twarz z stercza\u0142y j\u0119zyka i mrugaj\u0105c okiem",
    "Face with stuck out tongue and tightly-closed eyes": "Twarz z stercza\u0142y j\u0119zyka i szczelnie zamkni\u0119tych oczu",
    "Disappointed face": "Rozczarowany twarzy",
    "Worried face": "Martwi twarzy",
    "Angry face": "Gniewnych twarzy",
    "Pouting face": "D\u0105sy twarzy",
    "Crying face": "P\u0142acz\u0105cy",
    "Persevering face": "Wytrwa\u0142a twarz",
    "Face with look of triumph": "Twarz z wyrazem triumfu",
    "Disappointed but relieved face": "Rozczarowany ale ulg\u0119 twarz",
    "Frowning face with open mouth": "Krzywi\u0105c twarz z otwartymi ustami",
    "Anguished face": "Bolesna twarz",
    "Fearful face": "W obawie twarzy",
    "Weary face": "Zm\u0119czona twarz",
    "Sleepy face": "Je\u017adziec bez twarzy",
    "Tired face": "Zm\u0119czonej twarzy",
    "Grimacing face": "Skrzywi\u0142 twarz",
    "Loudly crying face": "G\u0142o\u015bno p\u0142aka\u0107 twarz",
    "Face with open mouth": "twarz z otwartymi ustami",
    "Hushed face": "Uciszy\u0142 twarzy",
    "Face with open mouth and cold sweat": "Twarz z otwartymi ustami i zimny pot",
    "Face screaming in fear": "Twarz z krzykiem w strachu",
    "Astonished face": "Zdziwienie twarzy",
    "Flushed face": "Zaczerwienienie twarzy",
    "Sleeping face": "\u015api\u0105ca twarz",
    "Dizzy face": "Zawroty g\u0142owy twarzy",
    "Face without mouth": "Twarz bez usta",
    "Face with medical mask": "Twarz\u0105 w medycznych maski",

    // Line breaker
    "Break": "Z\u0142ama\u0107",

    // Math
    "Subscript": "Indeks dolny",
    "Superscript": "Indeks g\u00f3rny",

    // Full screen
    "Fullscreen": "Pe\u0142ny ekran",

    // Horizontal line
    "Insert Horizontal Line": "Wstaw lini\u0119 poziom\u0105",

    // Clear formatting
    "Clear Formatting": "Usu\u0144 formatowanie",

    // Save
    "Save": "\u005a\u0061\u0070\u0069\u0073\u0061\u0107",

    // Undo, redo
    "Undo": "Cofnij",
    "Redo": "Pon\u00f3w",

    // Select all
    "Select All": "Zaznacz wszystko",

    // Code view
    "Code View": "Widok kod",

    // Quote
    "Quote": "Cytat",
    "Increase": "Wzrost",
    "Decrease": "Zmniejszenie",

    // Quick Insert
    "Quick Insert": "Szybkie wstaw",

    // Spcial Characters
    "Special Characters": "Znaki specjalne",
    "Latin": "Łacina",
    "Greek": "Grecki",
    "Cyrillic": "Cyrylica",
    "Punctuation": "Interpunkcja",
    "Currency": "Waluta",
    "Arrows": "Strzałki",
    "Math": "Matematyka",
    "Misc": "Misc",

    // Print.
    "Print": "Wydrukować",

    // Spell Checker.
    "Spell Checker": "Sprawdzanie pisowni",

    // Help
    "Help": "Wsparcie",
    "Shortcuts": "Skróty",
    "Inline Editor": "Edytor w wierszu",
    "Show the editor": "Pokazać edytor",
    "Common actions": "Wspólne działania",
    "Copy": "Kopiuj",
    "Cut": "Ciąć",
    "Paste": "Pasta",
    "Basic Formatting": "Podstawowe formatowanie",
    "Increase quote level": "Zwiększyć poziom notowań",
    "Decrease quote level": "Zmniejszyć poziom notowań",
    "Image / Video": "Obraz / wideo",
    "Resize larger": "Zmienić rozmiar większy",
    "Resize smaller": "Zmienić rozmiar mniejszy",
    "Table": "Stół",
    "Select table cell": "Wybierz komórkę tabeli",
    "Extend selection one cell": "Przedłużyć wybór jednej komórki",
    "Extend selection one row": "Przedłużyć wybór jednego rzędu",
    "Navigation": "Nawigacja",
    "Focus popup / toolbar": "Focus popup / toolbar",
    "Return focus to previous position": "Powrót do poprzedniej pozycji",

    // Embed.ly
    "Embed URL": "Osadzaj url",
    "Paste in a URL to embed": "Wklej w adresie URL do osadzenia",

    // Word Paste.
    "The pasted content is coming from a Microsoft Word document. Do you want to keep the format or clean it up?": "Wklejana treść pochodzi z programu Microsoft Word. Czy chcesz zachować formatowanie czy wkleić jako zwykły tekst?",
    "Keep": "Zachowaj formatowanie",
    "Clean": "Wklej jako tekst",
    "Word Paste Detected": "Wykryto sformatowany tekst"
  },
  direction: "ltr"
};

}));

