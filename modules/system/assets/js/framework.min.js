(()=>{"use strict";var e={};function t(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}e.amdO={};var n=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var n,i,o;return n=e,o=[{key:"load",value:function(t,n){return(new e).loadCollection(t,n)}}],(i=[{key:"loadCollection",value:function(e,t){var n=this,i=e.js?e.js:[],o=e.css?e.css:[],a=e.img?e.img:[];i=r(i,(function(e){return!document.querySelector('head script[src="'+e+'"]')})),o=r(o,(function(e){return!document.querySelector('head link[href="'+e+'"]')}));var s=0,u=!1,l=!1;function c(){return!!l&&!!u&&!(s<o.length)&&void(t&&t())}0!==i.length||0!==o.length||0!==a.length?(this.loadJavaScript(i,(function(){u=!0,c()})),o.forEach((function(e){n.loadStyleSheet(e,(function(){s++,c()}))})),this.loadImage(a,(function(){l=!0,c()}))):t&&t()}},{key:"loadStyleSheet",value:function(e,t){var n=document.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("type","text/css"),n.setAttribute("href",e),n.addEventListener("load",t,!1),void 0!==n&&document.getElementsByTagName("head")[0].appendChild(n),n}},{key:"loadJavaScript",value:function(e,t){if(e.length<=0)return t();var n=this,r=e.shift(),i=document.createElement("script");i.setAttribute("type","text/javascript"),i.setAttribute("src",r),i.addEventListener("load",(function(){n.loadJavaScript(e,t)}),!1),void 0!==i&&document.getElementsByTagName("head")[0].appendChild(i)}},{key:"loadImage",value:function(e,t){if(e.length<=0)return t();var n=0;e.forEach((function(r){var i=new Image;i.onload=function(){++n==e.length&&t&&t()},i.src=r}))}}])&&t(n.prototype,i),o&&t(n,o),Object.defineProperty(n,"prototype",{writable:!1}),e}();function r(e,t){for(var n=[],r=e.length,i=0;i<r;i++)t(e[i])&&n.push(e[i]);return n}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var a=function(){function e(t,n){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!t)throw new Error("The request handler name is not specified.");if(!t.match(/^(?:\w+\:{2})?on*/))throw new Error('Invalid handler name. The correct handler name format is: "onEvent".');if("undefined"==typeof FormData)throw new Error("The browser does not support the FormData interface.");this.options=n,this.handler=t}var t,n,r;return t=e,n=[{key:"getRequestOptions",value:function(){return{method:"POST",url:this.options.url?this.options.url:window.location.href,headers:this.buildHeaders(),responseType:!1===this.options.download?"":"blob"}}},{key:"buildHeaders",value:function(){var e=this.handler,t=this.options,n={"X-Requested-With":"XMLHttpRequest","X-OCTOBER-REQUEST-HANDLER":e};t.files||(n["Content-Type"]=t.bulk?"application/json":"application/x-www-form-urlencoded"),t.flash&&(n["X-OCTOBER-REQUEST-FLASH"]=1),t.partial&&(n["X-OCTOBER-REQUEST-PARTIAL"]=t.partial);var r=this.extractPartials(t.update,t.partial);r&&(n["X-OCTOBER-REQUEST-PARTIALS"]=r);var i=this.getXSRFToken();i&&(n["X-XSRF-TOKEN"]=i);var o=this.getCSRFToken();return o&&(n["X-CSRF-TOKEN"]=o),t.headers&&t.headers.constructor==={}.constructor&&Object.assign(n,t.headers),n}},{key:"extractPartials",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=[];if(e){if("object"!==i(e))throw new Error("Invalid update value. The correct format is an object ({...})");for(var r in e)"_self"===r&&t?n.push(t):n.push(r)}return n.join("&")}},{key:"getCSRFToken",value:function(){var e=document.querySelector('meta[name="csrf-token"]');return e?e.getAttribute("content"):null}},{key:"getXSRFToken",value:function(){var e=null;if(document.cookie&&""!=document.cookie)for(var t=document.cookie.split(";"),n=0;n<t.length;n++){var r=t[n].replace(/^([\s]*)|([\s]*)$/g,"");if("XSRF-TOKEN="==r.substring(0,11)){e=decodeURIComponent(r.substring(11));break}}return e}}],r=[{key:"fetch",value:function(e,t){return new this(e,t).getRequestOptions()}}],n&&o(t.prototype,n),r&&o(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var u="pending",l="rejected",c="resolved",f=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.options=t||{},this.stateStr=u,this.successFuncs=[],this.failureFuncs=[],this.progressFuncs=[],this.resolveArgs=[],this.rejectArgs=[],this.progressArgs=[],this.isProgressNotified=!1}var t,n,r;return t=e,n=[{key:"resolve",value:function(){return this.stateStr===u&&(this.resolveArgs=arguments,this.callFunction.call(this,this.successFuncs,this.resolveArgs),this.stateStr=c),this}},{key:"reject",value:function(){return this.stateStr===u&&(this.rejectArgs=arguments,this.callFunction.call(this,this.failureFuncs,this.rejectArgs),this.stateStr=l),this}},{key:"notify",value:function(){return this.stateStr===u&&(this.progressArgs=arguments,this.callFunction.call(this,this.progressFuncs,this.progressArgs),this.isProgressNotified=!0),this}},{key:"abort",value:function(){this.options.delegate&&this.options.delegate.abort()}},{key:"done",value:function(){var e=Array.prototype.slice.call(arguments);return this.successFuncs=this.successFuncs.concat(e),this.stateStr===c&&this.callFunction.call(this,e,this.resolveArgs),this}},{key:"fail",value:function(){var e=Array.prototype.slice.call(arguments);return this.failureFuncs=this.failureFuncs.concat(e),this.stateStr===l&&this.callFunction.call(this,e,this.rejectArgs),this}},{key:"progress",value:function(){var e=Array.prototype.slice.call(arguments);return this.progressFuncs=this.progressFuncs.concat(e),this.stateStr===u&&this.isProgressNotified&&this.callFunction.call(this,e,this.progressArgs),this}},{key:"always",value:function(){var e=Array.prototype.slice.call(arguments);return this.successFuncs=this.successFuncs.concat(e),this.failureFuncs=this.failureFuncs.concat(e),this.stateStr!==u&&this.callFunction.call(this,e,this.resolveArgs||this.rejectArgs),this}},{key:"then",value:function(){var e=[];for(var t in arguments){var n;n=Array.isArray(arguments[t])?arguments[t]:[arguments[t]],e.push(n)}return this.done.apply(this,e[0]),this.fail.apply(this,e[1]),this.progress.apply(this,e[2]),this}},{key:"promise",value:function(){var e=["resolve","reject","promise","notify"],t={};for(var n in this)-1===e.indexOf(n)&&(t[n]=this[n]);return t}},{key:"state",value:function(){return arguments.length>0&&(stateStr=arguments[0]),stateStr}},{key:"callFunction",value:function(e,t,n){var r=(n=n||{}).scope||this;for(var i in e){var o=e[i];"function"==typeof o&&o.apply(r,t)}}}],n&&s(t.prototype,n),r&&s(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function d(){var e=oc.useTurbo&&oc.useTurbo()?oc.AjaxTurbo.controller.getLastVisitUrl():function(){if(!document.referrer)return null;try{var e=new URL(document.referrer);if(e.origin!==location.origin)return null;var t=localStorage.getItem("ocPushStateReferrer");return t&&0===t.indexOf(e.pathname)?t:document.referrer}catch(e){}}();return!e||function(e){var t=new URL(e,window.location.origin),n=new URL(window.location.href);return t.origin===n.origin&&t.pathname===n.pathname}(e)?null:e}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){y(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function v(e){return function(e){if(Array.isArray(e))return g(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return g(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return g(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function m(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var b="replace",w="prepend",E="append",k="update",A=function(){function e(t,n,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.el=t.el,this.delegate=t,this.context=n,this.options=r,this.context.start=this.start.bind(this),this.context.success=this.success.bind(this),this.context.error=this.error.bind(this),this.context.complete=this.complete.bind(this),this.context.cancel=this.cancel.bind(this)}var t,r,i;return t=e,(r=[{key:"invoke",value:function(e,t){return this.options[e]?this.options[e].apply(this.context,t):this[e]?this[e].apply(this,v(t)):void 0}},{key:"invokeFunc",value:function(e,t){if(this.options[e])return this.options[e](this.el,t)}},{key:"start",value:function(e){this.invoke("markAsUpdating",[!0]),this.delegate.options.message&&this.invoke("handleProgressMessage",[this.delegate.options.message,!1])}},{key:"success",value:function(e,t,n){var r=this,i=new f;if(!1===this.invoke("beforeUpdate",[e,t,n]))return i;if(!1===this.invokeFunc("beforeUpdateFunc",e))return i;if(!this.delegate.applicationAllowsUpdate(e,t,n))return i;if(this.delegate.options.download&&e instanceof Blob)return this.invoke("handleFileDownload",[e,n]),this.delegate.notifyApplicationRequestSuccess(e,t,n),this.invokeFunc("successFunc",e),i;if(this.delegate.options.flash&&e.X_OCTOBER_FLASH_MESSAGES)for(var o in e.X_OCTOBER_FLASH_MESSAGES)this.invoke("handleFlashMessage",[e.X_OCTOBER_FLASH_MESSAGES[o],o]);return e.X_OCTOBER_DISPATCHES&&this.invoke("handleBrowserEvents",[e.X_OCTOBER_DISPATCHES])||(i=this.invoke("handleUpdateResponse",[e,t,n])).done((function(){r.delegate.notifyApplicationRequestSuccess(e,t,n),r.invokeFunc("successFunc",e)})),i}},{key:"error",value:function(e,t,n){var r,i=this,o=new f;if(void 0!==window.ocUnloading&&window.ocUnloading)return o;if(this.delegate.toggleRedirect(!1),406==t&&e){if(e.X_OCTOBER_DISPATCHES&&this.invoke("handleBrowserEvents",[e.X_OCTOBER_DISPATCHES]))return o;r=e.X_OCTOBER_ERROR_MESSAGE,o=this.invoke("handleUpdateResponse",[e,t,n])}else r=e,o.resolve();return o.done((function(){i.el!==document&&i.el.setAttribute("data-error-message",r),i.delegate.applicationAllowsError(e,t,n)&&!1!==i.invokeFunc("errorFunc",e)&&i.invoke("handleErrorMessage",[r])})),o}},{key:"complete",value:function(e,t,n){this.delegate.notifyApplicationRequestComplete(e,t,n),this.invokeFunc("completeFunc",e),this.invoke("markAsUpdating",[!1]),this.delegate.options.message&&this.invoke("handleProgressMessage",[null,!0])}},{key:"cancel",value:function(){this.invokeFunc("cancelFunc")}},{key:"handleConfirmMessage",value:function(e){var t=this,n=new f;if(n.done((function(){t.delegate.sendInternal()})).fail((function(){t.invoke("cancel",[])})),this.delegate.notifyApplicationConfirmMessage(e,n).defaultPrevented)return!1;if(e){var r=confirm(e);return r||this.invoke("cancel",[]),r}}},{key:"handleProgressMessage",value:function(e,t){}},{key:"handleFlashMessage",value:function(e,t){}},{key:"handleErrorMessage",value:function(e){this.delegate.notifyApplicationErrorMessage(e).defaultPrevented||e&&alert(e)}},{key:"handleValidationMessage",value:function(e,t){if(this.delegate.notifyApplicationBeforeValidate(e,t),this.delegate.formEl){var n=!0;for(var r in t){var i,o=[];i=r.replace(/\.(\w+)/g,"[$1]"),o.push('[name="'+i+'"]:not([disabled])'),o.push('[name="'+i+'[]"]:not([disabled])'),i=("."+r).replace(/\.(\w+)/g,"[$1]"),o.push('[name$="'+i+'"]:not([disabled])'),o.push('[name$="'+i+'[]"]:not([disabled])');var a=r.replace(/\.[0-9]+$/g,"");r!==a&&(i=a.replace(/\.(\w+)/g,"[$1]"),o.push('[name="'+i+'[]"]:not([disabled])'),i=("."+a).replace(/\.(\w+)/g,"[$1]"),o.push('[name$="'+i+'[]"]:not([disabled])'));var s=this.delegate.formEl.querySelector(o.join(", "));if(s){var u=this.delegate.notifyApplicationFieldInvalid(s,r,t[r],n);n&&(u.defaultPrevented||s.focus(),n=!1)}}}}},{key:"handleBrowserEvents",value:function(e){var t=this;if(!e||!e.length)return!1;var n=!1;return e.forEach((function(e){t.delegate.notifyApplicationCustomEvent(e.event,p(p({},e.data||{}),{},{context:t.context})).defaultPrevented&&(n=!0)})),n}},{key:"handleRedirectResponse",value:function(e){this.delegate.notifyApplicationBeforeRedirect().defaultPrevented||(this.options.browserRedirectBack&&(e=d()||e),oc.useTurbo&&oc.useTurbo()?oc.visit(e):location.assign(e))}},{key:"markAsUpdating",value:function(e){var t=this.options.update||{};for(var n in t){var r=t[n],i=[];t._self&&n==this.options.partial&&this.delegate.partialEl?(r=t._self,i=[this.delegate.partialEl]):i=j(r,'[data-ajax-partial="'+n+'"]'),i.forEach((function(t){e?t.setAttribute("data-ajax-updating",""):t.removeAttribute("data-ajax-updating")}))}}},{key:"handleUpdateResponse",value:function(e,t,r){var i=this,o=this.options.update||{},a=new f;return a.done((function(){var n=function(){var n=o[a]||a,s=[];o._self&&a==i.options.partial&&i.delegate.partialEl?(n=o._self,s=[i.delegate.partialEl]):s=j(n,'[data-ajax-partial="'+a+'"]'),s.forEach((function(o){var s=function(e,t){if("string"==typeof e){if("!"===e.charAt(0))return b;if("@"===e.charAt(0))return E;if("^"===e.charAt(0))return w}return void 0!==t.dataset.ajaxUpdateMode?t.dataset.ajaxUpdateMode:k}(n,o);if(s===b){var u=o.parentNode;o.insertAdjacentHTML("afterEnd",e[a]),u.removeChild(o),O(u,e[a])}else s===E?(o.insertAdjacentHTML("beforeEnd",e[a]),O(o,e[a])):s===w?(o.insertAdjacentHTML("afterBegin",e[a]),O(o,e[a])):(i.delegate.notifyApplicationBeforeReplace(o),o.innerHTML=e[a],function(e){Array.from(e.querySelectorAll("script")).forEach((function(e){var t=document.createElement("script");Array.from(e.attributes).forEach((function(e){return t.setAttribute(e.name,e.value)})),t.appendChild(document.createTextNode(e.innerHTML)),e.parentNode.replaceChild(t,e)}))}(o));i.delegate.notifyApplicationAjaxUpdate(o,e,t,r)}))};for(var a in e)n();setTimeout((function(){i.delegate.notifyApplicationUpdateComplete(e,t,r),i.invoke("afterUpdate",[e,t,r]),i.invokeFunc("afterUpdateFunc",e)}),0)})),e.X_OCTOBER_REDIRECT&&this.delegate.toggleRedirect(e.X_OCTOBER_REDIRECT),this.delegate.isRedirect&&this.invoke("handleRedirectResponse",[this.delegate.options.redirect]),e.X_OCTOBER_ERROR_FIELDS&&this.invoke("handleValidationMessage",[e.X_OCTOBER_ERROR_MESSAGE,e.X_OCTOBER_ERROR_FIELDS]),e.X_OCTOBER_ASSETS?n.load(e.X_OCTOBER_ASSETS,(function(){return a.resolve()})):a.resolve(),a}},{key:"handleFileDownload",value:function(e,t){if(this.options.browserTarget)window.open(window.URL.createObjectURL(e),this.options.browserTarget);else{var n="string"==typeof this.options.download?this.options.download:function(e){var t=e.getResponseHeader("Content-Disposition");if(!t)return null;for(var n=/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/g,r=null,i=null;null!==(i=n.exec(t));)r=i;return null!==r&&r[1]?null===/filename[^;*=\n]*\*=[^']*''/.exec(r[0])?r[1].replace(/['"]/g,""):decodeURIComponent(r[1].substring(r[1].indexOf("''")+2)):null}(t);if(n){var r=document.createElement("a");r.href=window.URL.createObjectURL(e),r.download=n,r.target="_blank",r.click(),window.URL.revokeObjectURL(r.href)}}}},{key:"applyQueryToUrl",value:function(e){for(var t=new URLSearchParams(window.location.search),n=function(){var n=i[r],o=e[n];Array.isArray(o)?(t.delete(n),t.delete("".concat(n,"[]")),o.forEach((function(e){return t.append("".concat(n,"[]"),e)}))):null===o?(t.delete(n),t.delete("".concat(n,"[]"))):t.set(n,o)},r=0,i=Object.keys(e);r<i.length;r++)n();var o=window.location.pathname,a=t.toString();a&&(o+="?"+a.replaceAll("%5B%5D=","[]=")),oc.useTurbo&&oc.useTurbo()?oc.visit(o,{action:"swap",scroll:!1}):(history.replaceState(null,"",o),localStorage.setItem("ocPushStateReferrer",o))}}])&&m(t.prototype,r),i&&m(t,i),Object.defineProperty(t,"prototype",{writable:!1}),e}();function j(e,t){return!0===e?document.querySelectorAll(t):"string"!=typeof e?[e]:-1===["#",".","@","^","!","="].indexOf(e.charAt(0))?[]:(-1!==["@","^","!","="].indexOf(e.charAt(0))&&(e=e.substring(1)),e||(e=t),document.querySelectorAll(e))}function O(e,t){var n=document.createElement("div");n.innerHTML=t,Array.from(n.querySelectorAll("script")).forEach((function(t){var n=document.createElement("script");Array.from(t.attributes).forEach((function(e){return n.setAttribute(e.name,e.value)})),n.appendChild(document.createTextNode(t.innerHTML)),e.appendChild(n),e.removeChild(n)}))}function x(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var S=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r;return t=e,r=[{key:"assignToObj",value:function(t,n,r){(new e).assignObjectInternal(t,n,r)}},{key:"serializeJSON",value:function(t){return(new e).parseContainer(t)}}],(n=[{key:"parseContainer",value:function(e){var t=this,n={};return e.querySelectorAll("input, textarea, select").forEach((function(e){if(!(!e.name||e.disabled||["file","reset","submit","button"].indexOf(e.type)>-1)&&(!(["checkbox","radio"].indexOf(e.type)>-1)||e.checked)){if("select-multiple"===e.type){var r=[];return Array.from(e.options).forEach((function(t){t.selected&&r.push({name:e.name,value:t.value})})),void t.assignObjectInternal(n,e.name,r)}t.assignObjectInternal(n,e.name,e.value)}})),n}},{key:"assignObjectInternal",value:function(e,t,n){this.assignObjectNested(e,this.nameToArray(t),n,t.endsWith("[]"))}},{key:"assignObjectNested",value:function(e,t,n,r){var i=e,o=t.length-1;t.forEach((function(e,t){r&&t===o?(Array.isArray(i[e])||(i[e]=[]),i[e].push(n)):(void 0!==i[e]&&i[e].constructor==={}.constructor||(i[e]={}),t===o&&(i[e]=n),i=i[e])}))}},{key:"nameToArray",value:function(e){for(var t,n=/([^\]\[]+)/g,r=[];t=n.exec(e);)r.push(t[0]);return r}}])&&x(t.prototype,n),r&&x(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function T(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var q=function(){function e(t,n,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.userData=t||{},this.targetEl=n,this.formEl=r}var t,n,r;return t=e,(n=[{key:"getRequestData",value:function(){var e;return e=this.formEl?new FormData(this.formEl):new FormData,this.appendSingleInputElement(e),e}},{key:"getAsFormData",value:function(){return this.appendJsonToFormData(this.getRequestData(),this.userData)}},{key:"getAsQueryString",value:function(){return this.convertFormDataToQuery(this.getAsFormData())}},{key:"getAsJsonData",value:function(){return JSON.stringify(this.convertFormDataToJson(this.getAsFormData()))}},{key:"appendSingleInputElement",value:function(e){if(!this.formEl&&this.targetEl&&(t=this.targetEl,["input","select","textarea"].includes((t.tagName||"").toLowerCase()))){var t,n=this.targetEl.name;n&&void 0===this.userData[n]&&("file"===this.targetEl.type?this.targetEl.files.forEach((function(t){e.append(n,t)})):e.append(n,this.targetEl.value))}}},{key:"appendJsonToFormData",value:function(e,t,n){var r=this;for(var i in t){var o=i;n&&(o=n+"["+i+"]");var a=t[i];a&&a.constructor==={}.constructor?this.appendJsonToFormData(e,a,o):a&&a.constructor===[].constructor?a.forEach((function(t,n){t.constructor==={}.constructor||t.constructor===[].constructor?r.appendJsonToFormData(e,t,o+"["+n+"]"):e.append(o+"[]",r.castJsonToFormData(t))})):e.append(o,this.castJsonToFormData(a))}return e}},{key:"convertFormDataToQuery",value:function(e){var t=this.formDataToArray(e);return Object.keys(t).map((function(e){return e.endsWith("[]")?t[e].map((function(t){return encodeURIComponent(e)+"="+encodeURIComponent(t)})).join("&"):encodeURIComponent(e)+"="+encodeURIComponent(t[e])})).join("&")}},{key:"convertFormDataToJson",value:function(e){var t=this.formDataToArray(e),n={};for(var r in t)S.assignToObj(n,r,t[r]);return n}},{key:"formDataToArray",value:function(e){return Object.fromEntries(Array.from(e.keys()).map((function(t){return[t,t.endsWith("[]")?e.getAll(t):e.getAll(t).pop()]})))}},{key:"castJsonToFormData",value:function(e){return null===e?"":!0===e?"1":!1===e?"0":e}}])&&T(t.prototype,n),r&&T(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function R(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.target,r=void 0===n?document:n,i=t.detail,o=void 0===i?{}:i,a=t.bubbles,s=void 0===a||a,u=t.cancelable,l=void 0===u||u,c=new CustomEvent(e,{detail:o,bubbles:s,cancelable:l});return r.dispatchEvent(c),c}function C(e){return e.replace(/^\n/,"")}function P(e,t){return e.reduce((function(e,n,r){return e+n+(null==t[r]?"":t[r])}),"")}function F(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=B(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function D(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,i,o=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(e){s=!0,i=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw i}}return o}(e,t)||B(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function B(e,t){if(e){if("string"==typeof e)return L(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?L(e,t):void 0}}function L(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function I(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var U=/[^.]*(?=\..*)\.|.*/,N=/\..*/,J=/::\d+$/,M={},_=1,Q={mouseenter:"mouseover",mouseleave:"mouseout"},H=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]),X=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r;return t=e,r=[{key:"on",value:function(e,t,n,r,i){G(e,t,n,r,i,!1)}},{key:"one",value:function(e,t,n,r,i){G(e,t,n,r,i,!0)}},{key:"off",value:function(e,t,n,r,i){if("string"==typeof t&&e){var o=D(z(t,n,r,i),4),a=o[0],s=o[1],u=o[2],l=o[3],c=u!==t,f=K(e),d=f[u]||{},h=t.startsWith(".");if(void 0===s){if(h)for(var p=0,y=Object.keys(f);p<y.length;p++)Y(e,f,y[p],t.slice(1));for(var v=0,g=Object.keys(d);v<g.length;v++){var m=g[v],b=m.replace(J,"");if(!c||t.includes(b)){var w=d[m];Z(e,f,u,w.callable,w.delegationSelector,l)}}}else{if(!d)return;Z(e,f,u,s,a?n:null,l)}}}},{key:"dispatch",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.target,r=void 0===n?document:n,i=t.detail,o=void 0===i?{}:i,a=t.bubbles,s=void 0===a||a,u=t.cancelable,l=void 0===u||u;return R(e,{target:r,detail:o,bubbles:s,cancelable:l})}}],(n=null)&&I(t.prototype,n),r&&I(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function V(e,t){return t&&"".concat(t,"::").concat(_++)||e.uidEvent||_++}function K(e){var t=V(e);return e.uidEvent=t,M[t]=M[t]||{},M[t]}function W(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return Object.values(e).find((function(e){return e.callable===t&&e.delegationSelector===n}))}function z(e,t,n,r){var i,o="string"==typeof t,a=o?n:t,s=o?r:n,u=(i=(i=e).replace(N,""),Q[i]||i);return H.has(u)||(u=e),[o,a,u,s]}function G(e,t,n,r,i,o){if("string"==typeof t&&e){var a=D(z(t,n,r,i),4),s=a[0],u=a[1],l=a[2],c=a[3];if(t in Q){u=function(e){return function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)}}(u)}var f=K(e),d=f[l]||(f[l]={}),h=W(d,u,s?n:null);if(h)h.oneOff=h.oneOff&&o;else{var p=V(u,t.replace(U,"")),y=s?function(e,t,n){return function r(i){for(var o=e.querySelectorAll(t),a=i.target;a&&a!==this;a=a.parentNode){var s,u=F(o);try{for(u.s();!(s=u.n()).done;){if(s.value===a)return i.delegateTarget=a,r.oneOff&&X.off(e,i.type,t,n),n.apply(a,[i])}}catch(e){u.e(e)}finally{u.f()}}}}(e,n,u):function(e,t){return function n(r){return r.delegateTarget=e,n.oneOff&&X.off(e,r.type,t),t.apply(e,[r])}}(e,u);y.delegationSelector=s?n:null,y.callable=u,y.oneOff=o,y.uidEvent=p,d[p]=y,e.addEventListener(l,y,c)}}}function Z(e,t,n,r,i,o){var a=W(t[n],r,i);a&&(e.removeEventListener(n,a,o),delete t[n][a.uidEvent])}function Y(e,t,n,r){for(var i=t[n]||{},o=0,a=Object.keys(i);o<a.length;o++){var s=a[o];if(s.includes(r)){var u=i[s];Z(e,t,n,u.callable,u.delegationSelector)}}}function ee(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var te,ne=0,re=-1,ie=-2,oe=-3,ae=function(){function e(t,n,r){var i=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.failed=!1,this.progress=0,this.sent=!1,this.delegate=t,this.url=n,this.options=r,this.headers=r.headers||{},this.method=r.method||"GET",this.responseType=r.responseType||"",this.data=r.data,this.timeout=r.timeout||0,this.requestProgressed=function(e){e.lengthComputable&&i.setProgress(e.loaded/e.total)},this.requestLoaded=function(){i.endRequest((function(e){i.processResponseData(e,(function(e,t){var n=e.getResponseHeader("Content-Type"),r=function(e){return(e||"").includes("application/json")}(n)?JSON.parse(t):t;if(i.options.htmlOnly&&!function(e){return(e||"").match(/^text\/html|^application\/xhtml\+xml/)}(n))return i.failed=!0,void i.delegate.requestFailedWithStatusCode(ie);e.status>=200&&e.status<300?i.delegate.requestCompletedWithResponse(r,e.status,function(e,t){if(e.getResponseHeader("X-OCTOBER-LOCATION"))return e.getResponseHeader("X-OCTOBER-LOCATION");var n=t.match(/^(.*)#/);return(n?n[1]:t)!==e.responseURL?e.responseURL:null}(e,i.url)):(i.failed=!0,i.delegate.requestFailedWithStatusCode(e.status,r))}))}))},this.requestFailed=function(){i.endRequest((function(){i.failed=!0,i.delegate.requestFailedWithStatusCode(ne)}))},this.requestTimedOut=function(){i.endRequest((function(){i.failed=!0,i.delegate.requestFailedWithStatusCode(re)}))},this.requestCanceled=function(){i.options.trackAbort?i.endRequest((function(){i.failed=!0,i.delegate.requestFailedWithStatusCode(oe)})):i.endRequest()},this.createXHR()}var t,n,r;return t=e,n=[{key:"send",value:function(){this.xhr&&!this.sent&&(this.notifyApplicationBeforeRequestStart(),this.setProgress(0),this.xhr.send(this.data||null),this.sent=!0,this.delegate.requestStarted())}},{key:"abort",value:function(){this.xhr&&this.sent&&this.xhr.abort()}},{key:"notifyApplicationBeforeRequestStart",value:function(){X.dispatch("ajax:request-start",{detail:{url:this.url,xhr:this.xhr},cancelable:!1})}},{key:"notifyApplicationAfterRequestEnd",value:function(){X.dispatch("ajax:request-end",{detail:{url:this.url,xhr:this.xhr},cancelable:!1})}},{key:"createXHR",value:function(){var e=this.xhr=new XMLHttpRequest;for(var t in e.open(this.method,this.url,!0),e.responseType=this.responseType,e.onprogress=this.requestProgressed,e.onload=this.requestLoaded,e.onerror=this.requestFailed,e.ontimeout=this.requestTimedOut,e.onabort=this.requestCanceled,this.timeout&&(e.timeout=1e3*this.timeout),this.headers)e.setRequestHeader(t,this.headers[t]);return e}},{key:"endRequest",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){};this.xhr&&(this.notifyApplicationAfterRequestEnd(),e(this.xhr),this.destroy())}},{key:"setProgress",value:function(e){this.progress=e,this.delegate.requestProgressed(e)}},{key:"destroy",value:function(){this.setProgress(1),this.delegate.requestFinished()}},{key:"processResponseData",value:function(e,t){if("blob"===this.responseType){var n=e.getResponseHeader("Content-Disposition")||"";if(0!==n.indexOf("attachment")&&0!==n.indexOf("inline")){var r=new FileReader;r.addEventListener("load",(function(){t(e,r.result)})),r.readAsText(e.response)}else t(e,e.response)}else t(e,e.responseText)}}],n&&ee(t.prototype,n),r&&ee(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function se(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function ue(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var le=function(){function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.stylesheetElement=this.createStylesheetElement(),this.progressElement=this.createProgressElement(),this.hiding=!1,this.value=0,this.visible=!1,this.trickle=function(){t.setValue(t.value+Math.random()/100)}}var t,n,r;return t=e,n=[{key:"show",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.cssClass&&this.progressElement.classList.add(e.cssClass),this.visible||(this.visible=!0,this.installStylesheetElement(),this.installProgressElement(),this.startTrickling())}},{key:"hide",value:function(){var e=this;this.visible&&!this.hiding&&(this.hiding=!0,this.fadeProgressElement((function(){e.uninstallProgressElement(),e.stopTrickling(),e.visible=!1,e.hiding=!1})))}},{key:"setValue",value:function(e){this.value=e,this.refresh()}},{key:"installStylesheetElement",value:function(){e.stylesheetReady||(document.head.insertBefore(this.stylesheetElement,document.head.firstChild),e.stylesheetReady=!0)}},{key:"installProgressElement",value:function(){this.progressElement.style.width="0",this.progressElement.style.opacity="1",document.documentElement.insertBefore(this.progressElement,document.body),this.refresh()}},{key:"fadeProgressElement",value:function(t){this.progressElement.style.opacity="0",setTimeout(t,1.5*e.animationDuration)}},{key:"uninstallProgressElement",value:function(){this.progressElement.parentNode&&document.documentElement.removeChild(this.progressElement)}},{key:"startTrickling",value:function(){this.trickleInterval||(this.trickleInterval=setInterval(this.trickle,e.animationDuration))}},{key:"stopTrickling",value:function(){clearInterval(this.trickleInterval),delete this.trickleInterval}},{key:"refresh",value:function(){var e=this;requestAnimationFrame((function(){e.progressElement.style.width="".concat(10+90*e.value,"%")}))}},{key:"createStylesheetElement",value:function(){var t=document.createElement("style");return t.textContent=e.defaultCSS,t}},{key:"createProgressElement",value:function(){var e=document.createElement("div");return e.className="oc-progress-bar",e}}],r=[{key:"defaultCSS",get:function(){return function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var i=C(P(e,n)).split("\n"),o=i[0].match(/^\s+/),a=o?o[0].length:0;return i.map((function(e){return e.slice(a)})).join("\n")}(te||(t=["\n        .oc-progress-bar {\n            position: fixed;\n            display: block;\n            top: 0;\n            left: 0;\n            height: 3px;\n            background: #0076ff;\n            z-index: 9999;\n            transition:\n                width ","ms ease-out,\n                opacity ","ms ","ms ease-in;\n            transform: translate3d(0, 0, 0);\n        }\n    "],n||(n=t.slice(0)),te=Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(n)}}))),e.animationDuration,e.animationDuration/2,e.animationDuration/2);var t,n}},{key:"progressBar",get:function(){return{show:function(){var e=ce();e.setValue(0),e.show()},hide:function(){var e=ce();e.setValue(100),e.hide()}}}}],n&&se(t.prototype,n),r&&se(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function ce(){return le.instance||(le.instance=new le),le.instance}function fe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function de(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fe(Object(n),!0).forEach((function(t){he(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function he(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function pe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}ue(le,"instance",null),ue(le,"stylesheetReady",!1),ue(le,"animationDuration",300);const ye=function(){function e(t,n,r){var i=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.el=t,this.handler=n,this.options=de(de({},this.constructor.DEFAULTS),r||{}),this.context={el:t,handler:n,options:this.options},this.progressBar=new le,this.showProgressBar=function(){i.progressBar.show({cssClass:"is-ajax"})}}var t,n,r;return t=e,r=[{key:"DEFAULTS",get:function(){return{handler:null,update:{},files:!1,bulk:!1,download:!1,browserTarget:null,browserValidate:!1,browserRedirectBack:!1,progressBarDelay:500,progressBar:null}}},{key:"send",value:function(t,n){return new e(document,t,n).start()}},{key:"sendElement",value:function(t,n,r){return"string"==typeof t&&(t=document.querySelector(t)),new e(t,n,r).start()}}],(n=[{key:"start",value:function(){if(this.applicationAllowsSetup()&&(this.initOtherElements(),this.preprocessOptions(),this.actions=new A(this,this.context,this.options),this.validateClientSideForm()&&this.applicationAllowsRequest()&&(!this.options.confirm||this.actions.invoke("handleConfirmMessage",[this.options.confirm]))))return this.sendInternal(),this.options.async?this.wrapInAsyncPromise(this.promise):this.promise}},{key:"sendInternal",value:function(){var e,t=this,n=new q(this.options.data,this.el,this.formEl);e=this.options.files?n.getAsFormData():this.options.bulk?n.getAsJsonData():n.getAsQueryString(),this.options.query&&this.actions.invoke("applyQueryToUrl",[!0!==this.options.query?this.options.query:JSON.parse(n.getAsJsonData())]);var r=a.fetch(this.handler,this.options),i=r.url,o=r.headers,s=r.method,u=r.responseType;this.request=new ae(this,i,{method:s,headers:o,responseType:u,data:e,trackAbort:!0}),this.promise=new f({delegate:this.request}),this.isRedirect=this.options.redirect&&this.options.redirect.length>0,this.notifyApplicationBeforeSend(),this.notifyApplicationAjaxPromise(),this.promise.fail((function(e,n,r){t.isRedirect||t.notifyApplicationAjaxFail(e,n,r)})).done((function(e,n,r){t.isRedirect||t.notifyApplicationAjaxDone(e,n,r)})).always((function(e,n,r){t.notifyApplicationAjaxAlways(e,n,r)})),this.request.send()}},{key:"toggleRedirect",value:function(e){e?(this.options.redirect=e,this.isRedirect=!0):(this.options.redirect=null,this.isRedirect=!1)}},{key:"applicationAllowsSetup",value:function(){return!this.notifyApplicationAjaxSetup().defaultPrevented}},{key:"applicationAllowsRequest",value:function(){return!this.notifyApplicationBeforeRequest().defaultPrevented}},{key:"applicationAllowsUpdate",value:function(e,t,n){return!this.notifyApplicationBeforeUpdate(e,t,n).defaultPrevented}},{key:"applicationAllowsError",value:function(e,t,n){return!this.notifyApplicationRequestError(e,t,n).defaultPrevented}},{key:"notifyApplicationAjaxSetup",value:function(){return R("ajax:setup",{target:this.el,detail:{context:this.context}})}},{key:"notifyApplicationAjaxPromise",value:function(){return R("ajax:promise",{target:this.el,detail:{context:this.context}})}},{key:"notifyApplicationAjaxFail",value:function(e,t,n){return R("ajax:fail",{target:this.el,detail:{context:this.context,data:e,responseCode:t,xhr:n}})}},{key:"notifyApplicationAjaxDone",value:function(e,t,n){return R("ajax:done",{target:this.el,detail:{context:this.context,data:e,responseCode:t,xhr:n}})}},{key:"notifyApplicationAjaxAlways",value:function(e,t,n){return R("ajax:always",{target:this.el,detail:{context:this.context,data:e,responseCode:t,xhr:n}})}},{key:"notifyApplicationAjaxUpdate",value:function(e,t,n,r){return R("ajax:update",{target:e,detail:{context:this.context,data:t,responseCode:n,xhr:r}})}},{key:"notifyApplicationBeforeRedirect",value:function(){return R("ajax:before-redirect",{target:this.el})}},{key:"notifyApplicationBeforeRequest",value:function(){return R("ajax:before-request",{target:this.triggerEl,detail:{context:this.context}})}},{key:"notifyApplicationBeforeUpdate",value:function(e,t,n){return R("ajax:before-update",{target:this.triggerEl,detail:{context:this.context,data:e,responseCode:t,xhr:n}})}},{key:"notifyApplicationRequestSuccess",value:function(e,t,n){return R("ajax:request-success",{target:this.triggerEl,detail:{context:this.context,data:e,responseCode:t,xhr:n}})}},{key:"notifyApplicationRequestError",value:function(e,t,n){return R("ajax:request-error",{target:this.triggerEl,detail:{context:this.context,message:e,responseCode:t,xhr:n}})}},{key:"notifyApplicationRequestComplete",value:function(e,t,n){return R("ajax:request-complete",{target:this.triggerEl,detail:{context:this.context,data:e,responseCode:t,xhr:n}})}},{key:"notifyApplicationBeforeValidate",value:function(e,t){return R("ajax:before-validate",{target:this.triggerEl,detail:{context:this.context,message:e,fields:t}})}},{key:"notifyApplicationBeforeReplace",value:function(e){return R("ajax:before-replace",{target:e})}},{key:"notifyApplicationBeforeSend",value:function(){return R("ajax:before-send",{target:window,detail:{context:this.context}})}},{key:"notifyApplicationUpdateComplete",value:function(e,t,n){return R("ajax:update-complete",{target:window,detail:{context:this.context,data:e,responseCode:t,xhr:n}})}},{key:"notifyApplicationFieldInvalid",value:function(e,t,n,r){return R("ajax:invalid-field",{target:window,detail:{element:e,fieldName:t,errorMsg:n,isFirst:r}})}},{key:"notifyApplicationConfirmMessage",value:function(e,t){return R("ajax:confirm-message",{target:window,detail:{message:e,promise:t}})}},{key:"notifyApplicationErrorMessage",value:function(e){return R("ajax:error-message",{target:window,detail:{message:e}})}},{key:"notifyApplicationCustomEvent",value:function(e,t){return R(e,{target:this.el,detail:t})}},{key:"requestStarted",value:function(){this.markAsProgress(!0),this.toggleLoadingElement(!0),this.options.progressBar&&this.showProgressBarAfterDelay(),this.actions.invoke("start",[this.request.xhr])}},{key:"requestProgressed",value:function(e){this.promise.notify(e)}},{key:"requestCompletedWithResponse",value:function(e,t){this.actions.invoke("success",[e,t,this.request.xhr]),this.actions.invoke("complete",[e,t,this.request.xhr]),this.promise.resolve(e,t,this.request.xhr)}},{key:"requestFailedWithStatusCode",value:function(e,t){e==oe?this.actions.invoke("cancel",[]):this.actions.invoke("error",[t,e,this.request.xhr]),this.actions.invoke("complete",[t,e,this.request.xhr]),this.promise.reject(t,e,this.request.xhr)}},{key:"requestFinished",value:function(){this.markAsProgress(!1),this.toggleLoadingElement(!1),this.options.progressBar&&this.hideProgressBar()}},{key:"initOtherElements",value:function(){"string"==typeof this.options.form?this.formEl=document.querySelector(this.options.form):this.options.form?this.formEl=this.options.form:this.formEl=this.el&&this.el!==document?this.el.closest("form"):null,this.triggerEl=this.formEl?this.formEl:this.el,this.partialEl=this.el&&this.el!==document?this.el.closest("[data-ajax-partial]"):null,this.loadingEl="string"==typeof this.options.loading?document.querySelector(this.options.loading):this.options.loading}},{key:"preprocessOptions",value:function(){void 0===this.options.partial&&this.partialEl&&void 0!==this.partialEl.dataset.ajaxPartial&&(this.options.partial=this.partialEl.dataset.ajaxPartial||!0)}},{key:"validateClientSideForm",value:function(){return!(this.options.browserValidate&&"function"==typeof document.createElement("input").reportValidity&&this.formEl&&!this.formEl.checkValidity()&&(this.formEl.reportValidity(),1))}},{key:"toggleLoadingElement",value:function(e){this.loadingEl&&("function"==typeof this.loadingEl.show&&"function"==typeof this.loadingEl.hide?e?this.loadingEl.show():this.loadingEl.hide():this.loadingEl.style.display=e?"block":"none")}},{key:"showProgressBarAfterDelay",value:function(){this.progressBar.setValue(0),this.progressBarTimeout=window.setTimeout(this.showProgressBar,this.options.progressBarDelay)}},{key:"hideProgressBar",value:function(){this.progressBar.setValue(100),this.progressBar.hide(),null!=this.progressBarTimeout&&(window.clearTimeout(this.progressBarTimeout),delete this.progressBarTimeout)}},{key:"markAsProgress",value:function(e){e?(document.documentElement.setAttribute("data-ajax-progress",""),this.formEl&&this.formEl.setAttribute("data-ajax-progress",this.handler)):(document.documentElement.removeAttribute("data-ajax-progress"),this.formEl&&this.formEl.removeAttribute("data-ajax-progress"))}},{key:"wrapInAsyncPromise",value:function(e){return new Promise((function(t,n,r){e.fail((function(e){n(e)})).done((function(e){t(e)})),r&&r((function(){e.abort()}))}))}}])&&pe(t.prototype,n),r&&pe(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function ve(e){return ve="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ve(e)}function ge(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}window.oc||(window.oc={}),window.oc.AjaxRequest||(window.oc.AjaxRequest=ye,window.oc.AssetManager=n,window.oc.ajax=ye.send,window.oc.request||(window.oc.request=ye.sendElement));var me=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r;return t=e,r=[{key:"paramToObj",value:function(e,t){if(void 0===t&&(t=""),"object"===ve(t))return t;"{"!==t.charAt(0)&&(t="{"+t+"}");try{return this.parseJSON(t)}catch(t){throw new Error("Error parsing the "+e+" attribute value. "+t)}}},{key:"parseJSON",value:function(t){return JSON.parse((new e).parseString(t))}}],(n=[{key:"parseString",value:function(e){if(!(e=e.trim()).length)throw new Error("Broken JSON object.");for(var t="";e&&","===e[0];)e=e.substr(1);if('"'===e[0]||"'"===e[0]){if(e[e.length-1]!==e[0])throw new Error("Invalid string JSON object.");for(var n='"',r=1;r<e.length;r++)if("\\"===e[r])"'"===e[r+1]||(n+=e[r]),n+=e[r+1],r++;else{if(e[r]===e[0])return n+='"';'"'===e[r]?n+='\\"':n+=e[r]}throw new Error("Invalid string JSON object.")}if("true"===e||"false"===e)return e;if("null"===e)return"null";var i=parseFloat(e);if(!isNaN(i))return i.toString();if("{"===e[0]){var o="needKey";for(t="{",r=1;r<e.length;r++)if(!this.isBlankChar(e[r]))if("needKey"!==o||'"'!==e[r]&&"'"!==e[r]){if("needKey"===o&&this.canBeKeyHead(e[r])){var a;t+='"',t+=a=this.parseKey(e,r),t+='"',r+=a.length-1,o="afterKey"}else if("afterKey"===o&&":"===e[r])t+=":",o=":";else if(":"===o)r=r+(n=this.getBody(e,r)).originLength-1,t+=this.parseString(n.body),o="afterBody";else if("afterBody"===o||"needKey"===o){for(var s=r;","===e[s]||this.isBlankChar(e[s]);)s++;if("}"===e[s]&&s===e.length-1){for(;","===t[t.length-1];)t=t.substr(0,t.length-1);return t+="}"}s!==r&&"{"!==t&&(t+=",",o="needKey",r=s-1)}}else t+='"'+(a=this.parseKey(e,r+1,e[r]))+'"',r+=a.length,r+=1,o="afterKey";throw new Error("Broken JSON object near "+t)}if("["===e[0]){for(t="[",o="needBody",r=1;r<e.length;r++)if(" "!==e[r]&&"\n"!==e[r]&&"\t"!==e[r])if("needBody"===o){if(","===e[r]){t+="null,";continue}if("]"===e[r]&&r===e.length-1)return","===t[t.length-1]&&(t=t.substr(0,t.length-1)),t+="]";r=r+(n=this.getBody(e,r)).originLength-1,t+=this.parseString(n.body),o="afterBody"}else if("afterBody"===o)if(","===e[r])for(t+=",",o="needBody";","===e[r+1]||this.isBlankChar(e[r+1]);)","===e[r+1]&&(t+="null,"),r++;else if("]"===e[r]&&r===e.length-1)return t+="]";throw new Error("Broken JSON array near "+t)}}},{key:"parseKey",value:function(e,t,n){for(var r="",i=t;i<e.length;i++){if(n&&n===e[i])return r;if(!n&&(" "===e[i]||":"===e[i]))return r;r+=e[i],"\\"===e[i]&&i+1<e.length&&(r+=e[i+1],i++)}throw new Error("Broken JSON syntax near "+r)}},{key:"getBody",value:function(e,t){if('"'===e[t]||"'"===e[t]){for(var n=e[t],r=t+1;r<e.length;r++)if("\\"===e[r])n+=e[r],r+1<e.length&&(n+=e[r+1]),r++;else{if(e[r]===e[t])return{originLength:(n+=e[t]).length,body:n};n+=e[r]}throw new Error("Broken JSON string body near "+n)}if("t"===e[t]){if(e.indexOf("true",t)===t)return{originLength:"true".length,body:"true"};throw new Error("Broken JSON boolean body near "+e.substr(0,t+10))}if("f"===e[t]){if(e.indexOf("f",t)===t)return{originLength:"false".length,body:"false"};throw new Error("Broken JSON boolean body near "+e.substr(0,t+10))}if("n"===e[t]){if(e.indexOf("null",t)===t)return{originLength:"null".length,body:"null"};throw new Error("Broken JSON boolean body near "+e.substr(0,t+10))}if("-"===e[t]||"+"===e[t]||"."===e[t]||e[t]>="0"&&e[t]<="9"){for(n="",r=t;r<e.length;r++){if(!("-"===e[r]||"+"===e[r]||"."===e[r]||e[r]>="0"&&e[r]<="9"))return{originLength:n.length,body:n};n+=e[r]}throw new Error("Broken JSON number body near "+n)}if("{"===e[t]||"["===e[t]){var i=[e[t]];for(n=e[t],r=t+1;r<e.length;r++){if(n+=e[r],"\\"===e[r])r+1<e.length&&(n+=e[r+1]),r++;else if('"'===e[r])'"'===i[i.length-1]?i.pop():"'"!==i[i.length-1]&&i.push(e[r]);else if("'"===e[r])"'"===i[i.length-1]?i.pop():'"'!==i[i.length-1]&&i.push(e[r]);else if('"'!==i[i.length-1]&&"'"!==i[i.length-1])if("{"===e[r])i.push("{");else if("}"===e[r]){if("{"!==i[i.length-1])throw new Error("Broken JSON "+("{"===e[t]?"object":"array")+" body near "+n);i.pop()}else if("["===e[r])i.push("[");else if("]"===e[r]){if("["!==i[i.length-1])throw new Error("Broken JSON "+("{"===e[t]?"object":"array")+" body near "+n);i.pop()}if(!i.length)return{originLength:r-t,body:n}}throw new Error("Broken JSON "+("{"===e[t]?"object":"array")+" body near "+n)}throw new Error("Broken JSON body near "+e.substr(t-5>=0?t-5:0,50))}},{key:"canBeKeyHead",value:function(e){return"\\"!==e[0]&&(e[0]>="a"&&e[0]<="z"||e[0]>="A"&&e[0]<="Z"||"_"===e[0]||e[0]>="0"&&e[0]<="9"||"$"===e[0]||e.charCodeAt(0)>255)}},{key:"isBlankChar",value:function(e){return" "===e||"\n"===e||"\t"===e}}])&&ge(t.prototype,n),r&&ge(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function be(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function we(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?be(Object(n),!0).forEach((function(t){Ee(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):be(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ee(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ke(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var Ae=function(){function e(t,n,r){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.options=r||{},this.ogElement=t,this.element=this.findElement(t),this.element?(this.assignAsEval("beforeUpdateFunc","requestBeforeUpdate"),this.assignAsEval("afterUpdateFunc","requestAfterUpdate"),this.assignAsEval("successFunc","requestSuccess"),this.assignAsEval("errorFunc","requestError"),this.assignAsEval("cancelFunc","requestCancel"),this.assignAsEval("completeFunc","requestComplete"),this.assignAsData("progressBar","requestProgressBar"),this.assignAsData("message","requestMessage"),this.assignAsData("confirm","requestConfirm"),this.assignAsData("redirect","requestRedirect"),this.assignAsData("loading","requestLoading"),this.assignAsData("form","requestForm"),this.assignAsData("url","requestUrl"),this.assignAsData("bulk","requestBulk",{emptyAsTrue:!0}),this.assignAsData("files","requestFiles",{emptyAsTrue:!0}),this.assignAsData("flash","requestFlash",{emptyAsTrue:!0}),this.assignAsData("download","requestDownload",{emptyAsTrue:!0}),this.assignAsData("update","requestUpdate",{parseJson:!0}),this.assignAsData("query","requestQuery",{emptyAsTrue:!0,parseJson:!0}),this.assignAsData("browserTarget","browserTarget"),this.assignAsData("browserValidate","browserValidate",{emptyAsTrue:!0}),this.assignAsData("browserRedirectBack","browserRedirectBack",{emptyAsTrue:!0}),this.assignAsMetaData("update","ajaxRequestUpdate",{parseJson:!0,mergeValue:!0}),this.assignRequestData(),n||(n=this.getHandlerName()),ye.sendElement(this.element,n,this.options)):ye.send(n,this.options)}var t,n,r;return t=e,n=[{key:"findElement",value:function(e){if(!e||e===document)return null;if(e.matches("[data-request]"))return e;var t=e.closest("[data-request]");return t||e}},{key:"getHandlerName",value:function(){return this.element.dataset.dataRequest?this.element.dataset.dataRequest:this.element.getAttribute("data-request")}},{key:"assignAsEval",value:function(e,t){var n;void 0===this.options[e]&&(n=this.element.dataset[t]?this.element.dataset[t]:this.element.getAttribute("data-"+je(t)))&&(this.options[e]=function(e,t){return new Function("data",n).apply(e,[t])})}},{key:"assignAsData",value:function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=r.parseJson,o=void 0!==i&&i,a=r.emptyAsTrue,s=void 0!==a&&a;void 0===this.options[e]&&null!==(n=this.element.dataset[t]?this.element.dataset[t]:this.element.getAttribute("data-"+je(t)))&&(n=this.castAttrToOption(n,s),o&&"string"==typeof n&&(n=me.paramToObj("data-"+je(t),n)),this.options[e]=n)}},{key:"assignAsMetaData",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.mergeValue,i=void 0===r||r,o=n.parseJson,a=void 0!==o&&o,s=n.emptyAsTrue,u=void 0!==s&&s,l=document.documentElement.querySelector('head meta[name="'+je(t)+'"]');if(l){var c=l.getAttribute("content");c=a?me.paramToObj(je(t),c):this.castAttrToOption(c,u),this.options[e]=i?we(we({},this.options[e]||{}),c):c}}},{key:"castAttrToOption",value:function(e,t){return!(!t||""!==e)||"true"===e||"1"===e||"false"!==e&&"0"!==e&&e}},{key:"assignRequestData",value:function(){var e={};this.options.data&&Object.assign(e,this.options.data);var t=this.ogElement.getAttribute("data-request-data");t&&Object.assign(e,me.paramToObj("data-request-data",t)),function(e,t){var n=[];if(!e.parentNode)return n;for(var r=e.parentNode.closest(t);r;)n.push(r),r=r.parentNode.closest(t);return n}(this.ogElement,"[data-request-data]").reverse().forEach((function(t){Object.assign(e,me.paramToObj("data-request-data",t.getAttribute("data-request-data")))})),this.options.data=e}}],r=[{key:"fromElement",value:function(t,n,r){return"string"==typeof t&&(t=document.querySelector(t)),new e(t,n,r)}}],n&&ke(t.prototype,n),r&&ke(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function je(e){return e.replace(/[A-Z]/g,(function(e){return"-".concat(e.toLowerCase())}))}function Oe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var xe=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.started=!1,this.documentVisible=!0}var t,n,r;return t=e,(n=[{key:"start",value:function(){var e=this;this.started||(window.onbeforeunload=this.documentOnBeforeUnload,addEventListener("DOMContentLoaded",(function(){return e.render()})),addEventListener("page:updated",(function(){return e.render()})),addEventListener("ajax:update-complete",(function(){return e.render()})),addEventListener("visibilitychange",(function(){return e.documentOnVisibilityChange()})),X.on(document,"submit","[data-request]",this.documentOnSubmit),X.on(document,"input","input[data-request][data-track-input]",this.documentOnKeyup),X.on(document,"change","select[data-request], input[type=radio][data-request], input[type=checkbox][data-request], input[type=file][data-request]",this.documentOnChange),X.on(document,"keydown","input[type=text][data-request], input[type=submit][data-request], input[type=password][data-request]",this.documentOnKeydown),X.on(document,"click","a[data-request], button[data-request], input[type=button][data-request], input[type=submit][data-request]",this.documentOnClick),this.started=!0)}},{key:"stop",value:function(){this.started&&(this.started=!1)}},{key:"render",value:function(e){X.dispatch("before-render"),X.dispatch("render"),dispatchEvent(new Event("resize")),this.documentOnRender(e)}},{key:"documentOnVisibilityChange",value:function(e){this.documentVisible=!document.hidden,this.documentVisible&&this.documentOnRender()}},{key:"documentOnRender",value:function(e){this.documentVisible&&document.querySelectorAll("[data-auto-submit]").forEach((function(e){var t=e.dataset.autoSubmit||0;e.removeAttribute("data-auto-submit"),setTimeout((function(){Ae.fromElement(e)}),t)}))}},{key:"documentOnSubmit",value:function(e){e.preventDefault(),Ae.fromElement(e.target)}},{key:"documentOnClick",value:function(e){e.preventDefault(),Ae.fromElement(e.target)}},{key:"documentOnChange",value:function(e){Ae.fromElement(e.target)}},{key:"documentOnKeyup",value:function(e){var t=e.target,n=t.dataset.ocLastValue;if(-1!==["email","number","password","search","text"].indexOf(t.type)&&(void 0===n||n!=t.value)){t.dataset.ocLastValue=t.value,void 0!==this.dataTrackInputTimer&&clearTimeout(this.dataTrackInputTimer);var r=t.getAttribute("data-track-input");r||(r=300);var i=this;this.dataTrackInputTimer=setTimeout((function(){i.lastDataTrackInputRequest&&i.lastDataTrackInputRequest.abort(),i.lastDataTrackInputRequest=Ae.fromElement(t)}),r)}}},{key:"documentOnKeydown",value:function(e){"Enter"===e.key&&(e.preventDefault(),void 0!==this.dataTrackInputTimer&&clearTimeout(this.dataTrackInputTimer),Ae.fromElement(e.target))}},{key:"documentOnBeforeUnload",value:function(e){window.ocUnloading=!0}}])&&Oe(t.prototype,n),r&&Oe(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Se(e){return Se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Se(e)}function Te(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var qe=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r;return t=e,n=[{key:"bind",value:function(){this.bindRequestFunc(),this.bindRenderFunc(),this.bindjQueryEvents()}},{key:"bindRequestFunc",value:function(){var e=$.fn.request;$.fn.request=function(e,t){var n="object"===Se(t)?t:{};return new Ae(this.get(0),e,n)},$.fn.request.Constructor=Ae,$.request=function(e,t){return $(document).request(e,t)},$.fn.request.noConflict=function(){return $.fn.request=e,this}}},{key:"bindRenderFunc",value:function(){$.fn.render=function(e){$(document).on("render",e)}}},{key:"bindjQueryEvents",value:function(){this.migratejQueryEvent(document,"ajax:setup","ajaxSetup",["context"]),this.migratejQueryEvent(document,"ajax:promise","ajaxPromise",["context"]),this.migratejQueryEvent(document,"ajax:fail","ajaxFail",["context","data","responseCode","xhr"]),this.migratejQueryEvent(document,"ajax:done","ajaxDone",["context","data","responseCode","xhr"]),this.migratejQueryEvent(document,"ajax:always","ajaxAlways",["context","data","responseCode","xhr"]),this.migratejQueryEvent(document,"ajax:before-redirect","ajaxRedirect"),this.migratejQueryEvent(document,"ajax:update","ajaxUpdate",["context","data","responseCode","xhr"]),this.migratejQueryEvent(document,"ajax:before-replace","ajaxBeforeReplace"),this.migratejQueryEvent(document,"ajax:before-request","oc.beforeRequest",["context"]),this.migratejQueryEvent(document,"ajax:before-update","ajaxBeforeUpdate",["context","data","responseCode","xhr"]),this.migratejQueryEvent(document,"ajax:request-success","ajaxSuccess",["context","data","responseCode","xhr"]),this.migratejQueryEvent(document,"ajax:request-complete","ajaxComplete",["context","data","responseCode","xhr"]),this.migratejQueryEvent(document,"ajax:request-error","ajaxError",["context","message","responseCode","xhr"]),this.migratejQueryEvent(document,"ajax:before-validate","ajaxValidation",["context","message","fields"]),this.migratejQueryEvent(window,"ajax:before-send","ajaxBeforeSend",["context"]),this.migratejQueryEvent(window,"ajax:update-complete","ajaxUpdateComplete",["context","data","responseCode","xhr"]),this.migratejQueryEvent(window,"ajax:invalid-field","ajaxInvalidField",["element","fieldName","errorMsg","isFirst"]),this.migratejQueryEvent(window,"ajax:confirm-message","ajaxConfirmMessage",["message","promise"]),this.migratejQueryEvent(window,"ajax:error-message","ajaxErrorMessage",["message"]),this.migratejQueryAttachData(document,"ajax:setup","a[data-request], button[data-request], form[data-request], a[data-handler], button[data-handler]")}},{key:"migratejQueryEvent",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],i=this;$(e).on(t,(function(e){i.triggerjQueryEvent(e.originalEvent,n,r)}))}},{key:"triggerjQueryEvent",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=$.Event(t),i=this.buildDetailArgs(e,n);$(e.target).trigger(r,i),r.isDefaultPrevented()&&e.preventDefault()}},{key:"buildDetailArgs",value:function(e,t){var n=[];return t.forEach((function(t){n.push(e.detail[t])})),n}},{key:"migratejQueryAttachData",value:function(e,t,n){$(e).on(t,n,(function(e){var t=$(this).data("request-data");if(t){var n=e.detail.context.options;t.constructor==={}.constructor?Object.assign(n.data,t):"string"==typeof t&&Object.assign(n.data,me.paramToObj("request-data",t))}}))}}],n&&Te(t.prototype,n),r&&Te(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Re=new xe;const Ce={controller:Re,parseJSON:me.parseJSON,serializeJSON:S.serializeJSON,requestElement:Ae.fromElement,start:function(){Re.start(),window.jQuery&&(new qe).bind()},stop:function(){Re.stop()}};function Pe(e){return Pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pe(e)}window.oc||(window.oc={}),window.oc.AjaxFramework||(window.oc.AjaxFramework=Ce,window.oc.request=Ce.requestElement,window.oc.parseJSON=Ce.parseJSON,window.oc.serializeJSON=Ce.serializeJSON,window.oc.Events=X,window.oc.waitFor=function(e,t){return new Promise((function(n,r){var i=function(){e()&&(clearInterval(o),n())},o=setInterval(i,100);i(),t&&setTimeout((function(){clearInterval(o),r()}),t)}))},window.oc.pageReady=function(){return new Promise((function(e){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e()})):e()}))},window.oc.visit=function(e){return window.location.assign(e)},"function"==typeof define&&e.amdO||"object"==("undefined"==typeof exports?"undefined":Pe(exports))||Ce.start())})();