// Basic grid system
@import "../../../backend/assets/less/core/boot.less";
@import "../../../backend/assets/foundation/elements/icons/icons.less";
@import "../../../backend/assets/vendor/bootstrap/bootstrap-lite.css";

@font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
@color-base: #405261;
@color-warning: #c84530;
@font-path: "../../ui/font"; // For icon fonts

html,
body {
    font-size: 14px;
    line-height: 1.42857143;
}

body {
    padding-top: 40px;
    font-family: @font-family-base;
    background: #f3f3f3;
    color: @color-base;
}

code {
    color: inherit;
}

h1, h2, h3, h4, h5 {
    font-family: @font-family-base;
    text-transform: uppercase;
}

h1 {
    font-weight: 300;
    font-size: 50px;
    margin-bottom: 15px;

    i[class^="icon-"] {
        &:before {
            font-size: 46px;
        }
    }
}

i[class^="icon-"].warning {
    color: @color-warning;
}

h3 {
    font-size: 24px;
    font-weight: 300;
}

p.lead {
    font-size: 16px;
    font-weight: 300;
}

ul.indicators {
    list-style: none;
    padding: 0;
    margin: 0;
    .clearfix;

    li {
        float: left;
        margin-right: 50px;

        &:last-child {
            margin-right: 0;
        }

        h3 {
            font-weight: 300;
            font-size: 12px;
            line-height: 100%;
            margin-bottom: 7px;
        }

        p {
            font-size: 20px;
            font-weight: 600;
            word-wrap: break-word;
        }
    }
}

/*
 * Tables
 */

table.data-table {
    width: 100%;
    font-size: 12px;
    border-collapse: collapse;
    margin-bottom: 20px;

    tr {
        &:first-child th {
          border-top: 0;
        }

        &:last-child td {
          border-bottom: 0;
        }

        td:first-child,
        th:first-child {
          border-left: 0;
        }

        td:last-child,
        th:last-child {
          border-right: 0;
        }
    }

    td, th {
        vertical-align: top;
        text-align: left;
        font-weight: 300;
        border: 1px solid #fff;
        padding: 6px 8px;
        &.right {
            text-align: right;
        }
    }

    thead {
        th, th {
            text-transform: uppercase;
            background: #7b8892;
            color: white;
        }
    }

    tbody {
        td, th {
            background-color: white;
        }

        td.file-column {
            word-break: break-all;
        }

        tr:nth-child(2n) {
            td, th {
                background-color: #d8d9db;
            }
        }
    }
}

/*
 * Error page
 */

.exception-name-block {
    margin-bottom: 15px;

    div, p {
        font-weight: 300;
        padding: 10px 15px;
    }

    div {
        background: @color-warning;
        color: white;
        position: relative;

        &:after {
            position: absolute;
            display: block;
            content: " ";
            bottom: -6px;
            left: 20px;
            width: 0px;
            height: 0px;
            border-style: solid;
            border-width: 8px 8px 0 8px;
            border-color: @color-warning transparent transparent transparent;
        }
    }

    p {
        background: #ebebeb;
        margin-bottom: 0;
        span {
            color: lighten(@color-base, 30%);
        }
    }
}

.syntaxhighlighter {
    margin-bottom: 30px;
    background: white;
    font: 12px/170% Monaco, Menlo, 'Ubuntu Mono', 'Droid Sans Mono', 'Courier New', monospace!important;

    table {
        width: 100%;
        table-layout: fixed;

        td {
            padding: 5px 0;

            &.gutter {
                padding: 5px 10px;
                width: 35px;
                background: #7b8892;
                color: white;
            }

            &.code {
                overflow-x: auto;
                width: 100% !important;

                .container {
                    width: 100% !important;
                    padding: 0;

                    div.line {
                        white-space: nowrap;
                        padding-left: 10px;

                        &.highlighted {
                            background: @color-warning;
                            color: white;
                        }

                        code {
                            font: 12px/170% Monaco, Menlo, 'Ubuntu Mono', 'Droid Sans Mono', 'Courier New', monospace!important;
                        }
                    }
                }
            }
        }
    }
}