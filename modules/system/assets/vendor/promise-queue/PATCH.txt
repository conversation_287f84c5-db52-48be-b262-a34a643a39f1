TheQueue.prototype._dequeue method was patched to support BluebirdJs promise cancellation.

diff --git a/assets/vendor/promise-queue/promise-queue.js b/assets/vendor/promise-queue/promise-queue.js
index 7c5ffb3..9e1fcfb 100644
--- a/assets/vendor/promise-queue/promise-queue.js
+++ b/assets/vendor/promise-queue/promise-queue.js
@@ -91,7 +91,7 @@
      */
     Queue.prototype.add = function (promiseGenerator) {
         var self = this;
-        return new LocalPromise(function (resolve, reject, notify) {
+        return new LocalPromise(function (resolve, reject, onCancel) {
             // Do not queue to much promises
             if (self.queue.length >= self.maxQueuedPromises) {
                 reject(new Error('Queue limit reached'));
@@ -103,7 +103,7 @@
                 promiseGenerator: promiseGenerator,
                 resolve: resolve,
                 reject: reject,
-                notify: notify || noop
+                onCancel: onCancel
             });
 
             self._dequeue();
@@ -150,24 +150,27 @@
         try {
             this.pendingPromises++;
 
-            resolveWith(item.promiseGenerator())
+            var itemPromise = resolveWith(item.promiseGenerator())
             // Forward all stuff
                 .then(function (value) {
-                    // It is not pending now
-                    self.pendingPromises--;
                     // It should pass values
                     item.resolve(value);
-                    self._dequeue();
                 }, function (err) {
-                    // It is not pending now
-                    self.pendingPromises--;
                     // It should not mask errors
                     item.reject(err);
-                    self._dequeue();
                 }, function (message) {
                     // It should pass notifications
                     item.notify(message);
+                })
+                .finally(function() {
+                    // It is not pending now
+                    self.pendingPromises--;
+                    self._dequeue();
                 });
+
+            item.onCancel(function() {
+                itemPromise.cancel();
+            });
         } catch (err) {
             self.pendingPromises--;
             item.reject(err);
