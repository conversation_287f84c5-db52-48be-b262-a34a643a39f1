{% if paginator.hasPages %}
    <ul class="pagination">
        {#-- Previous Page Link --#}
        {% if paginator.onFirstPage %}
            <li class="page-item disabled">
                <span class="page-link">{{ 'system::lang.pagination.previous'|trans }}</span>
            </li>
        {% else %}
            <li class="page-item">
                <a class="page-link" href="{{ paginator.previousPageUrl }}" rel="prev">{{ 'system::lang.pagination.previous'|trans }}</a>
            </li>
        {% endif %}

        {#-- Next Page Link --#}
        {% if paginator.hasMorePages %}
            <li class="page-item">
                <a class="page-link" href="{{ paginator.nextPageUrl }}" rel="next">{{ 'system::lang.pagination.next'|trans }}</a>
            </li>
        {% else %}
            <li class="page-item disabled" aria-disabled="true">
                <span class="page-link">{{ 'system::lang.pagination.next'|trans }}</span>
            </li>
        {% endif %}
    </ul>
{% endif %}
