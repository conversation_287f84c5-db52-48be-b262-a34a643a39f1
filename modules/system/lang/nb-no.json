{"Check For Updates": "<PERSON> etter oppdate<PERSON>er", "Manage Plugins": "Administrere plugins", "Project": "Prosjekt", "Owner": "<PERSON><PERSON>", "Plugins": "Plugins", "Disabled": "Deaktivert", "Current Build": "Nåværende build", "System Updates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update the system modules and plugins.": "Oppdatere systemet, administrer og installere plugins og temaer.", "Settings": "Innstillinger", "Unable to find the specified settings.": "Fant ikke spesifiserte innstilling.", "The settings page is missing a Model definition.": "Innstillingssiden mangler en modell-definisjon.", ":name settings updated": "Innstillingene for :name har blitt lagret.", "Return to System Settings": "Tilbake til systeminnstillinger", "There is no documentation provided.": "Ingen dokumentasjon å vise.", "Documentation": "Dokumentasjon", "Upgrade Guide": "Oppgraderingsguide", "Attach to Project": "Tilkoble prosjekt", "Manage Updates": "Administrer op<PERSON><PERSON><PERSON><PERSON>", "Software Update": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Return to System Updates": "Tilbake til oppdateringer", "Try Again": "<PERSON><PERSON><PERSON><PERSON> ig<PERSON>n", "Unpacking application files": "Pakker ut applikasjonsfiler", "Update Failed": "Oppdateringen mislyktes", "Unpacking plugin: :name": "Pakker ut plugin: :name", "Updating application files": "Laster ned applikasjonsfiler", "Finishing update process": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Installing plugin: :name": "Laster ned plugin: :name", "Finishing installation process": "<PERSON><PERSON><PERSON><PERSON>", "Please specify a Theme name to install.": "Oppgi tema-navn for å installere.", "Installing theme: :name": "Laster ned tema: :name", "Please specify a Plugin name to install.": "Vennligst oppgi pluginens navn.", "Update process complete": "<PERSON><PERSON><PERSON><PERSON> har fullført.", "Package installed successfully": "Plugin har blitt installert.", "View the Dashboard": "Se dashboard", "Log File": "Loggfil", "Sendmail": "Sendmail", "SMTP": "SMTP", "Mailgun": "Mailgun", "SES": "SES", "No encryption": "Ingen kryptering", "TLS": "TLS", "Mail Configuration": "E-postinnstillinger", "Manage email configuration.": "Administrere e-postinnstillinger.", "Sender Name": "Avsendernavn", "General": "Generelt", "Sender Email": "Avsenderens e-postadresse", "Mail Method": "E-postmetode", "SMTP Address": "SMTP-adresse", "SMTP Port": "SMTP-port", "SMTP Encryption Protocol": "SMTP krypteringsprotkoll", "SMTP Authorization Required": "SMTP-autentisering kreves", "Use this checkbox if your SMTP server requires authorization.": "Kryss av dersom SMTP-tjeneren krever autentisering.", "Username": "Brukernavn", "Password": "Passord", "Sendmail Path": "Sendmail-sti", "Please specify the path of the sendmail program.": "Vennligst oppgi stien til sendmail-programmet.", "Mailgun Domain": "Mailgun-domene", "Please specify the Mailgun domain name.": "Vennligst oppgi Mailgun-domenenavnet.", "Mailgun Secret": "Mailgun Secret", "Enter your Mailgun API key.": "Oppgi din Mailgun-API-nøkkel.", "SES Key": "SES nøkkel", "Enter your SES API key": "Oppgi din SES API-nøkkel", "SES Secret": "SES secret", "Enter your SES API secret key": "Oppgi din hemmelige SES API-nøkkel", "SES Region": "SES region", "Enter your SES region (e.g. us-east-1)": "Legg inn din SES region (feks. us-east-1)", "Manage Backend Preferences": "Administrer backend-innstillinger", "Manage Code Editor Preferences": "Administrer kodeeditor-innstillinger", "Customize Backend Styles": "Tilpasse backend", "Mail Templates": "E-postmaler", "Modify the mail templates that are sent to users and administrators, manage email layouts.": "Modifisere e-postmalene som blir sendt til brukere og administratorer, administrere e-postlayouts.", "Event Log": "Hendelseslogg", "View system log messages with their recorded time and details.": "Se systemloggmeldinger med registrert tid og detaljer.", "Request Log": "Forespørselslogg", "View bad or redirected requests, such as Page not found (404).": "Se feilaktige fore<PERSON><PERSON><PERSON><PERSON>, for eksempel Ikke funnet (404).", "Test message sent.": "Testmeldingen er sendt.", "New Layout": "Ny layout", "New Template": "Ny mal", "Template": "Mal", "Templates": "Maler", "Layouts": "Layouts", "Layout": "Layout", "Mail Layouts": "E-postlayouts", "Name": "Navn", "Code": "<PERSON><PERSON>", "Unique code used to refer to this template": "Unik kode som tilknyttes denne malen", "-- No layout --": "-- ingen layout --", "HTML": "HTML", "CSS": "CSS", "Plaintext": "<PERSON> tekst", "Subject": "<PERSON><PERSON>", "Email message subject": "Emnet til e-posten", "Description": "Beskrivelse", "Drivers Not Installed": "<PERSON><PERSON> ikke installert", "This mail method requires the plugin \":plugin\" be installed before you can send mail.": "<PERSON>ne mailmetoden krever at plugin \":plugin\" er installert før du kan sende mail.", "ID": "ID", "Event ID": "Hendelses-ID", "Level": "<PERSON><PERSON><PERSON>", "Date & Time": "Tid", "Message": "Melding", "Version": "Versjon", "Status": "Status", "Log ID": "Logg-ID", "Counter": "<PERSON><PERSON><PERSON>", "Referers": "Referers", "URL": "URL", "This log displays a list of potential errors that occur in the application, such as exceptions and debugging information.": "Denne loggen viser en liste over potensielle feilmeldinger som oppstår i applikasjonen, for eksempel unntak og debugginginformasjon.", "Event": "<PERSON><PERSON><PERSON><PERSON>", "This log displays a list of browser requests that may require attention. For example, if a visitor opens a CMS page that cannot be found, a record is created with the status code 404.": "<PERSON>ne loggen viser en liste over nettleserforespørsler som kan kreve oppmerksomhet. For eksempel, hvis en bruker besøker en side som ikke eksisterer, vil det bli oppført her med statuskode 404.", "Request": "Forespørsel", "Event log emptied": "Hendelsesloggen er tømt.", "Empty Event Log": "<PERSON><PERSON><PERSON> he<PERSON>lses<PERSON>g", "Emptying Event Log...": "Tømmer hendelseslogg...", "Return to Event Log": "Tilbake til hendelseslogg", "Are you sure you want to reset the selected plugins? This will reset each plugin's data, restoring it to the initial install state.": "<PERSON>r du sikker?", "Plugin has been removed from the file system.": "Plugins har blitt fjernet fra systemet.", "search plugins to install...": "søk etter plugins å installere...", "search themes to install...": "søk etter temaer å installere...", "Install Theme": "Installér tema", "Theme Name": "Navn på tema", "Install Plugin": "Installér", "Plugin Name": "Navn på plugin", "Name the plugin by its unique code. For example, RainLab.Blog": "Navngi pluginen ved et unikt navn. For eksempel, RainLab.Blog", "Name the theme by its unique code. For example, RainLab.Vanilla": "Navngi temaet ved et unikt navn. For eksempel, RainLab.Vanilla", "The combiner file ':name' is not found.": "Kombinasjonsfilen ':name' ble ikke funnet."}