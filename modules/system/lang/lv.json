{"Check For Updates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Install Packages": "Instalējiet pakotnes", "Manage Themes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> motī<PERSON>", "Manage Plugins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Project": "Projekts", "Owner": "Īpašnieks", "Plugins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Recommended": "Ieteicams", "Disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Current Build": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> versija", "Updates Available": "<PERSON><PERSON><PERSON>", "Up to Date": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Latest Build": "Jaunākā versija", "View Changelog": "S<PERSON>īt izmaiņu žurnālu", "System Updates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Update the system modules and plugins.": "<PERSON><PERSON><PERSON><PERSON><PERSON> sistēmu, pārvaldiet un instalējiet spraudņus un tēmas.", "General": "Galvenie", "Mail": "Pasts", "Utilities": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Settings": "Iestatījumi", "Show All Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus iestatījumus", "Unable to find the specified settings.": "Nebija iespējams atrast norādī<PERSON> i<PERSON>tīju<PERSON>.", "The settings page is missing a Model definition.": "Iestatīju<PERSON> lapa nav nor<PERSON><PERSON><PERSON><PERSON> definīcijā.", ":name settings updated": "Iestatījumi priekš :name tika ve<PERSON><PERSON><PERSON><PERSON>.", "Return to System Settings": "Atgriezties sistēmas iesta<PERSON>īju<PERSON>", "Find a Setting...": "Atrodiet iestatījumu...", "Disable mail branding CSS": "Atspējot pasta zīmola CSS", "Manage Sites": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "Manage the websites available for this application.": "Pārvaldiet šai lietojumprogrammai pieejamās viet<PERSON>.", "Marketplace": "<PERSON><PERSON><PERSON> la<PERSON>", "There is no documentation provided.": "Dokumentācija nav pievienota.", "Documentation": "Dokumentācija", "Upgrade Guide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "License": "License", "Attach to Project": "<PERSON><PERSON><PERSON> projekt<PERSON>", "Manage Updates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Software Update": "<PERSON><PERSON><PERSON><PERSON>", "Return to System Updates": "Atgriezties sistēmas at<PERSON>āju<PERSON>", "Try Again": "Mēģināt vēlreiz", "Unpacking application files": "Atpakojam aplikācijas failus", "Update Failed": "Atjauninā<PERSON><PERSON> neizdev<PERSON>s", "Unpacking plugin: :name": "Atpakojam spraudni: :name", "The primary site is used by default and cannot be deleted.": "<PERSON><PERSON><PERSON><PERSON><PERSON> vietne tiek i<PERSON>ta pēc no<PERSON>, un to nevar i<PERSON>.", "Disabled sites are not shown on the frontend.": "Atspējotās viet<PERSON> p<PERSON>kšgalā netiek rādītas.", "Enabled in the Admin Panel": "Iespējots administratora panelī", "Configuration": "Konfigurācija", "Use this if you want the site to be enabled in the admin panel.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ja v<PERSON><PERSON><PERSON>, lai vietne tiktu iespējota administr<PERSON><PERSON><PERSON> panelī.", "Install": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sync Project": "Sinhronizācijas projekts", "Name": "Nosa<PERSON>ms", "Unique Code": "Unikāls kods", "Theme": "<PERSON><PERSON><PERSON>", "Sites": "Viet<PERSON>", "Create Site": "Izveidot vietni", "Base URL": "Pamata URL", "Status": "Statuss", "Current default value: :value": "Pašre<PERSON>ē<PERSON>ā noklusējuma vērtība: :value", "Locale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Timezone": "Laika zona", "Custom application URL": "Pielāgotas lietojumprogrammas URL", "Override the application URL when this site is active.": "Ignorēt lietojumprogrammas URL, kad <PERSON><PERSON> vietne ir aktīva.", "Use a CMS route prefix": "Izmantojiet CMS maršruta prefiksu", "A prefix can identify this site when using a shared hostname.": "Prefikss var identificēt š<PERSON> vietni, ja tiek izmantots koplietots saimniekdatora nosaukums.", "Define matching hostnames": "Definējiet atbilstošus resursdatora nosaukumus", "Specify domain names and patterns that must be used to serve this site.": "Norādiet domēna nosaukumus un modeļ<PERSON>, kas j<PERSON><PERSON><PERSON><PERSON> v<PERSON> a<PERSON>.", "Display a style for this site": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> krāsu", "To help identify this site, display a color in the admin panel.": "<PERSON> palīdzētu identificēt š<PERSON> v<PERSON>, administ<PERSON><PERSON><PERSON><PERSON> panelī parādiet krāsu.", "Save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Save and Close": "Saglabāt un aizvērt", "Use Default": "Izmantojiet no<PERSON>lusējuma i<PERSON>ījumu", "Use Custom": "Izmantojiet Custom", "Specify a custom locale code.": "Norādiet pielāgotu lokalizācijas kodu.", "Failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "or": "vai", "Code": "Kods", "October CMS Marketplace": "Oktobra CMS tirgus", "Visit the :link to add some.": "Projektam nav spraudņu vai motīvu. Apmeklējiet saiti :, lai pievie<PERSON>u da<PERSON>.", "Buy Now": "<PERSON><PERSON><PERSON>", "Updating package manager": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Updating application files": "Lejupie<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>us", "Setting build number": "Notiek būvējuma numura iestatīšana", "Finishing update process": "<PERSON><PERSON><PERSON><PERSON> procesu", "Installing plugin: :name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> spraudni: :name", "Finishing installation process": "<PERSON><PERSON><PERSON><PERSON> instalācijas procesu", "Removing theme: :name": "Notiek motīva noņemšana: :name", "Please specify a Theme name to install.": "<PERSON><PERSON><PERSON><PERSON><PERSON>t <PERSON>, kuru instalēt.", "Installing theme: :name": "Le<PERSON><PERSON>lādējam tēmu: :name", "Removing plugin: :name": "<PERSON><PERSON> spraudņa no<PERSON>: :name", "Please specify a Plugin name to install.": "<PERSON><PERSON><PERSON><PERSON><PERSON>t <PERSON>, kuru instalēt.", "Update process complete": "Atjaunin<PERSON><PERSON><PERSON> process noritēja veiksmīgi.", "Package installed successfully": "<PERSON><PERSON><PERSON><PERSON> tika veiksmīgi instalēts.", "Check Dependencies": "Pārbaudiet atkarības", "Install Dependencies": "Instalējiet <PERSON>", "There are missing dependencies needed for the system to run correctly.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, lai sistē<PERSON> darb<PERSON> par<PERSON>.", "License Key": "Licences atslēga", "How to find your License Key": "Kā atrast savu licences atslēgu", "The password attribute is required.": "Parol<PERSON> atri<PERSON> ir <PERSON>.", "The login attribute is required.": "Nepieciešams pieteikšanās atribū<PERSON>.", "Cannot login user since they are not activated.": "Nevar pieteikties lietotājam, jo tie nav aktivizēti.", "Cannot login user since they are banned.": "Nevar piete<PERSON> lieto<PERSON>, jo viņi ir aizliegti.", "Cannot login user since they are suspended.": "Nevar piete<PERSON> lieto<PERSON>, jo viņu darb<PERSON> ir aptur<PERSON>ta.", "A user was not found with the given credentials.": "Lietotājs ar nor<PERSON><PERSON><PERSON><PERSON><PERSON> akreditācijas datiem netika atrasts.", "A user was found but the password did not match.": "Lietot<PERSON><PERSON><PERSON> t<PERSON>, bet <PERSON> nesa<PERSON>.", "User is not logged in": "Lietotājs nav pieteicies", "Register Software": "Reģistrēt programmatūru", "View the Dashboard": "<PERSON><PERSON><PERSON><PERSON> mērinstrumentu paneli", "Set the Default Dashboard": "Iestatiet noklusējuma informācijas paneli", "Log File": "<PERSON><PERSON><PERSON><PERSON> fails", "Sendmail": "Sendmail", "SMTP": "SMTP", "Mailgun": "Mailgun", "SES": "VIŅU", "Postmark": "Pasta zīmogs", "No encryption": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TLS": "TLS", "Mail Configuration": "Epasta konfigurā<PERSON>ja", "Manage email configuration.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> epasta kon<PERSON>gu<PERSON>.", "Sender Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vā<PERSON>", "Sender Email": "Sūtītāja Epasts", "Mail Method": "Epasta metode", "SMTP Address": "SMTP Adrese", "SMTP Port": "SMTP Ports", "SMTP Encryption Protocol": "SMTP šifrēšanas protokols", "SMTP Authorization Required": "Nepieciešama SMTP autorizācija", "Use this checkbox if your SMTP server requires authorization.": "Lieto<PERSON><PERSON>,ja SMTP serverim nepieciešama autorizācija.", "Username": "Lietotājvārds", "Password": "Parole", "Sendmail Path": "<PERSON><PERSON>", "Please specify the path of the sendmail program.": "<PERSON><PERSON><PERSON><PERSON> nor<PERSON>t ceļu uz sendmail programmu.", "Mailgun Domain": "<PERSON><PERSON>", "Please specify the Mailgun domain name.": "<PERSON><PERSON><PERSON><PERSON> norādiet Mailgun domēna no<PERSON>.", "Mailgun Secret": "Mailgun Secret", "Enter your Mailgun API key.": "Ievadiet savu Mailgun API kodu.", "SES Key": "SES atslēga", "Enter your SES API key": "Ievadiet savu SES API atslēgu", "SES Secret": "SES noslēpums", "Enter your SES API secret key": "Ievadiet savu SES API slepeno atslēgu", "SES Region": "SES reģions", "Enter your SES region (e.g. us-east-1)": "Ievadiet savu SES reģionu (piemēram, us-east-1)", "Postmark Token": "Pasta zīmoga žetons", "Enter your Postmark API secret key": "Ievadiet savu Postmark API slepeno atslēgu", "Define administrator roles": "Definējiet administratora lomas", "Restrict access to this site to only administrator with the following roles.": "Ierobežot piekļuvi šai vietnei tikai administratoram ar tālāk norād<PERSON>taj<PERSON>m lomām.", "Customize Backend Styles": "<PERSON><PERSON>ā<PERSON> back-end", "Mail Templates": "<PERSON><PERSON><PERSON> ve<PERSON>nes", "Modify the mail templates that are sent to users and administrators, manage email layouts.": "Pielāgo<PERSON><PERSON>, kura<PERSON> tiek sūtītas lietotājiem un administrātoriem, pārvldiet Epastu izkārtojumus.", "Event Log": "Notikum<PERSON>", "View system log messages with their recorded time and details.": "<PERSON><PERSON><PERSON> sist<PERSON><PERSON> ziņoju<PERSON> ar notikuma laiku un detaļām.", "Request Log": "Piepras<PERSON><PERSON><PERSON>", "View bad or redirected requests, such as Page not found (404).": "<PERSON><PERSON><PERSON> s<PERSON> vai pārs<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON> <PERSON><PERSON> netika atras<PERSON> (404).", "Test message sent.": "Testa vēstule tika veiksmīgi nosūtīta.", "New Layout": "Jauns <PERSON>", "New Template": "<PERSON><PERSON><PERSON>", "Template": "<PERSON><PERSON><PERSON><PERSON>", "Templates": "<PERSON><PERSON><PERSON><PERSON>", "Layouts": "<PERSON>zk<PERSON><PERSON><PERSON>ju<PERSON>", "Layout": "Izkārtojums", "Mail Layouts": "Epasta izkārtojumi", "Unique code used to refer to this template": "Unikāls kods, lai identificētu šo veidni", "HTML": "HTML", "CSS": "CSS", "Plaintext": "T<PERSON>i teksts", "Subject": "<PERSON><PERSON><PERSON>", "Email message subject": "Epasta vēstules temats", "Description": "<PERSON><PERSON><PERSON>", "Drivers Not Installed": "Dziņi nav instalēti", "This mail method requires the plugin \":plugin\" be installed before you can send mail.": "Šai pasta metodei nepieciešams spraudnis \":plugin\" instalējiet to pirms pasta sūtīšanas.", "ID": "ID", "Event ID": "Notikuma ID", "Level": "<PERSON><PERSON><PERSON><PERSON>", "Date & Time": "Datums & Laiks", "Message": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>", "Version": "<PERSON><PERSON><PERSON>", "Log ID": "Žurnāla ID", "Counter": "Skaits", "Referers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "URL": "URL", "This log displays a list of potential errors that occur in the application, such as exceptions and debugging information.": "<PERSON><PERSON> attēlo sarak<PERSON>u ar poten<PERSON><PERSON><PERSON><PERSON> k<PERSON>, k<PERSON><PERSON>, tai skaitā izņēmumus un atkļūdošanas informāciju.", "This log displays a list of browser requests that may require attention. For example, if a visitor opens a CMS page that cannot be found, a record is created with the status code 404.": "<PERSON><PERSON> attēlo sarak<PERSON>u ar pārlūkprogrammas pieprasī<PERSON>m, k<PERSON><PERSON> vaja<PERSON> pievē<PERSON> u<PERSON>. <PERSON><PERSON><PERSON><PERSON>, ja apmekl<PERSON>t<PERSON><PERSON><PERSON> pie<PERSON> CMS lapu, kura nav pieejama, tiek veikts ieraksts ar kodu 404.", "Event log emptied": "Notikumu ž<PERSON> ve<PERSON> notī<PERSON>.", "Empty Event Log": "Iztukšot notikumu žurnālu", "Emptying Event Log...": "Iztukšojam notikumu žurnālu...", "Return to Event Log": "Atgriezties notikumu žurnālā", "Are you sure you want to reset the selected plugins? This will reset each plugin's data, restoring it to the initial install state.": "Vai esat pārlie<PERSON>āts?", "Plugin has been removed from the file system.": "<PERSON><PERSON><PERSON><PERSON>i tika noņemti no failu sistēmas.", "search plugins to install...": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lai instalētu...", "search themes to install...": "mek<PERSON><PERSON><PERSON> tēmas, kuras instalēt...", "Install Theme": "Instalēt Tēmu", "Theme Name": "<PERSON><PERSON><PERSON>", "Install Plugin": "Instalēt Spraudni", "Plugin Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Name the plugin by its unique code. For example, RainLab.Blog": "<PERSON><PERSON><PERSON><PERSON> spraudņa unik<PERSON> kod<PERSON>. <PERSON><PERSON><PERSON><PERSON>, RainLab.Blog", "Name the theme by its unique code. For example, RainLab.Vanilla": "Norādiet tēmas unik<PERSON>lo kodu. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "The combiner file ':name' is not found.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fails ':name' net<PERSON> at<PERSON>."}