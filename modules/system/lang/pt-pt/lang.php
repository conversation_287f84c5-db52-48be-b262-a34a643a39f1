<?php return [
  'app' => [
    'name' => 'October CMS',
    'tagline' => 'Voltando ao básico',
  ],
  'locale' => [
    'be' => 'Bielorusso',
    'bg' => 'Búlgaro',
    'cs' => 'Checo',
    'da' => 'Dinamarquês',
    'en' => 'Inglês (Estados Unidos)',
    'en-au' => 'Inglês (Austrália)',
    'en-ca' => 'Inglês (Canadá)',
    'en-gb' => 'Inglês (Reino Unido)',
    'de' => 'Alemão',
    'el' => 'Greg<PERSON>',
    'es' => 'Espanhol',
    'es-ar' => 'Espanhol (Argentina)',
    'fa' => 'Persa (Farsi)',
    'fr' => 'Françês',
    'fr-ca' => 'Françês (Canadá)',
    'hu' => 'Húngaro',
    'id' => 'Indonésio',
    'it' => 'Italiano',
    'ja' => '<PERSON>apon<PERSON><PERSON>',
    'lv' => 'Letão',
    'nb-no' => 'Nor<PERSON>gu<PERSON><PERSON>',
    'nl' => '<PERSON>land<PERSON><PERSON>',
    'pl' => 'Polaco',
    'pt-pt' => 'Português (Portugal)',
    'pt-br' => 'Português (Brasil)',
    'ro' => 'Romeno',
    'rs' => 'Srpski',
    'ru' => 'Russo',
    'sv' => 'Suéco',
    'sk' => 'Esloveno',
    'sl' => 'Slovenščina',
    'tr' => 'Turco',
    'zh-cn' => 'Chinês',
    'zh-tw' => 'Tailandês',
    'vn' => 'Tiếng việt',
  ],
  'directory' => [
    'create_fail' => 'Não é possível criar a diretoria: :name',
  ],
  'file' => [
    'create_fail' => 'Não é possível criar o ficheiro: :name',
  ],
  'page' => [
    'invalid_token' => [
      'label' => 'Token de segurança inválido',
    ],
  ],
  'combiner' => [],
  'system' => [
    'name' => 'Sistema',
    'menu_label' => 'Sistema',
    'categories' => [
      'cms' => 'CMS',
      'misc' => 'Diversos',
      'logs' => 'Registos',
      'mail' => 'E-mail',
      'shop' => 'Loja ',
      'team' => 'Equipa',
      'users' => 'Utilizadores',
      'system' => 'Sistema',
      'social' => 'Social',
      'events' => 'Eventos',
      'customers' => 'Clientes',
      'my_settings' => 'Configurações',
    ],
  ],
  'theme' => [
    'label' => 'Tema',
    'unnamed' => 'Tema sem nome',
    'name' => [],
  ],
  'themes' => [
    'install' => 'Instalar tema',
    'installed' => 'Temas instalados',
    'no_themes' => 'Não há temas instalados.',
    'recommended' => 'Recomendado',
    'remove_confirm' => 'Tem a certeza que deseja remover este tema?',
  ],
  'plugin' => [
    'label' => 'Extensão',
    'unnamed' => 'Extensão sem nome',
    'name' => [],
  ],
  'plugins' => [
    'enable_or_disable' => 'Activar ou desactivar',
    'enable_or_disable_title' => 'Activar ou desactivar extensões',
    'install' => 'Instalar extensões',
    'install_products' => 'Instalar produtos',
    'installed' => 'Extensões instaladas',
    'no_plugins' => 'Não há extensões instaladas.',
    'recommended' => 'Recomendada',
    'remove' => 'Remover',
    'refresh' => 'Actualizar',
    'disabled_label' => 'Desactivado',
    'disabled_help' => 'Extensões que estão desactivadas são ignoradas pela aplicação.',
    'frozen_label' => 'Congelar actualizações',
    'frozen_help' => 'Extensões congeladas serão ignoradas pelo processo de atualização.',
    'selected_amount' => 'Extensões selecionadas: :amount',
    'remove_confirm' => 'Tem a certeza?',
    'remove_success' => 'Extensões removidas com sucesso do sistema.',
    'refresh_success' => 'Extensões atualizadas com sucesso.',
    'disable_confirm' => 'Tem a certeza?',
    'disable_success' => 'Extensões desactivadas com sucesso.',
    'enable_success' => 'Extensões desactivadas com sucesso.',
  ],
  'project' => [
    'attach' => 'Anexar Projecto',
    'detach' => 'Desanexar Projecto',
    'none' => 'Nenhum',
    'id' => [
      'missing' => 'Por favor, forneça um identificador de projecto para utilizar.',
    ],
    'detach_confirm' => 'Tem a certeza que deseja desanexar este projecto?',
    'unbind_success' => 'Projecto desanexado com sucesso.',
  ],
  'settings' => [
    'search' => 'Procurar',
  ],
  'mail' => [
    'smtp_ssl' => 'Conexão SSL obrigatória',
  ],
  'mail_templates' => [
    'name_comment' => 'Nome exclusivo utilizado para se referir a este modelo',
    'test_send' => 'Enviar mensagem de teste',
    'test_confirm' => 'Enviar uma mensagem de teste para :email. Continuar?',
    'creating' => 'Criando modelo...',
    'creating_layout' => 'Criando esboço...',
    'saving' => 'Guardando modelo...',
    'saving_layout' => 'Guardando esboço...',
    'delete_confirm' => 'Apagar este modelo?',
    'delete_layout_confirm' => 'Apagar este esboço?',
    'deleting' => 'Apagando modelo...',
    'deleting_layout' => 'Apagando esboço...',
    'sending' => 'Enviando mensagem de teste...',
    'return' => 'Regressar à lista de modelos',
  ],
  'install' => [],
  'updates' => [
    'plugin_author' => 'Autor',
    'plugin_not_found' => 'Extensão não encontrada',
    'core_build' => 'Compilação :build',
    'core_build_help' => 'Última versão está disponível.',
    'themes' => 'Temas',
    'plugin_version_none' => 'Nova extensão',
    'plugin_current_version' => 'Versão actual',
    'theme_new_install' => 'Instalação do novo tema.',
    'theme_extracting' => 'Descomprimindo o tema: :name',
    'update_label' => 'Actualizar',
    'update_loading' => 'Carregando atualizações disponíveis...',
    'force_label' => 'Forçar actualização',
    'found' => [
      'label' => 'Actualizações encontradas!',
      'help' => 'Clique em Actualizar para iniciar o processo de actualização.',
    ],
    'none' => [
      'label' => 'Nenhuma actualização',
      'help' => 'Não há novas actualizações.',
    ],
    'important_action' => [
      'empty' => 'Selecionar acção',
      'confirm' => 'Confirmar actualização',
      'skip' => 'Ignorar esta actualização (apenas uma vez)',
      'ignore' => 'Ignorar esta actualização (sempre)',
    ],
    'important_action_required' => 'Acção requerida',
    'important_view_guide' => 'Exibir guia de actualização',
    'important_view_release_notes' => 'Ver notas da actualização',
    'important_alert_text' => 'Algumas actualizações precisam de sua atenção.',
    'details_title' => 'Detalhes da extensão',
    'details_view_homepage' => 'Visualizar página',
    'details_changelog' => 'Registo de alterações',
    'details_changelog_missing' => 'Não foi fornecido registo de alterações.',
    'details_current_version' => 'Versão actual',
    'details_author' => 'Autor',
  ],
  'server' => [
    'connect_error' => 'Erro ao conectar-se com o servidor.',
    'response_not_found' => 'O servidor de actualização não foi encontrado.',
    'response_invalid' => 'Resposta inválida do servidor.',
    'response_empty' => 'Resposta vazia do servidor.',
    'file_error' => 'Servidor não conseguiu entregar o pacote.',
    'file_corrupt' => 'O ficheiro do servidor está corrompido.',
  ],
  'behavior' => [
    'missing_property' => 'Classe :class deve definir a propriedade $:property usada pelo comportamento :behavior.',
  ],
  'config' => [
    'not_found' => 'Não foi possível localizar o ficheiro de configuração :file definido para :location.',
    'required' => 'Configuração utilizada em :location deve fornecer um valor :property.',
  ],
  'zip' => [
    'extract_failed' => 'Não foi possível extrair ficheiro do núcleo ":file".',
  ],
  'event_log' => [],
  'request_log' => [
    'empty_link' => 'Esvaziar registo de requisições.',
    'empty_loading' => 'Esvaziando registo de requisições...',
    'empty_success' => 'Registo de requisições esvaziado com sucesso.',
    'return_link' => 'Regressar ao registo de requisições',
    'id' => 'ID',
  ],
  'permissions' => [
    'name' => 'Sistema',
    'manage_system_settings' => 'Gerir configurações do sistema',
    'manage_software_updates' => 'Gerir actualizações',
    'access_logs' => 'Exibir registos de sistema',
    'manage_mail_templates' => 'Gerir modelos de e-mail',
    'manage_mail_settings' => 'Gerir configurações de e-mail',
    'manage_other_administrators' => 'Gerir outros administradores',
  ],
  'log' => [],
  'media' => [
    'invalid_path' => 'Caminho especificado inválido: \':path\'.',
    'folder_size_items' => 'item(s)',
  ],
];
