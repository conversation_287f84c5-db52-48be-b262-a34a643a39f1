<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Client-side Language Lines
    |--------------------------------------------------------------------------
    |
    | These are messages made available to the client browser via JavaScript.
    | To compile this file run: php artisan october:util compile lang
    |
    */

    'markdowneditor' => [
        'formatting' => 'Formatering',
        'quote' => 'Citat',
        'code' => 'Kod',
        'header1' => 'Rubrik 1',
        'header2' => 'Rubrik 2',
        'header3' => 'Rubrik 3',
        'header4' => 'Rubrik 4',
        'header5' => 'Rubrik 5',
        'header6' => 'Rubrik 6',
        'bold' => 'Fet',
        'italic' => 'Kursiv',
        'unorderedlist' => 'Oordnad lista',
        'orderedlist' => 'Ordnad lista',
        'video' => 'Video',
        'image' => 'Bild',
        'link' => 'Länk',
        'horizontalrule' => 'Infoga horisontiell linje',
        'fullscreen' => 'Fullskärm',
        'preview' => 'Förhandsgranska',
    ],

    'mediamanager' => [
        'insert_link' => "Infoga medialänk",
        'insert_image' => "Infoga bild",
        'insert_video' => "Infoga video",
        'insert_audio' => "Infoga ljud",
        'invalid_file_empty_insert' => "Vänligen välj en fil att infoga till länken.",
        'invalid_file_single_insert' => "Vänligen välj en enskild fil.",
        'invalid_image_empty_insert' => "Vänligen välj bild(er) att infoga.",
        'invalid_video_empty_insert' => "Vänligen välj en video att infoga.",
        'invalid_audio_empty_insert' => "Vänligen välj en ljudfil att infoga.",
    ],

    'alert' => [
        'confirm_button_text' => 'OK',
        'cancel_button_text' => 'Avbryt',
    ],

];
