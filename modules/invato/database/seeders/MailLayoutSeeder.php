<?php

namespace Invato\Database\Seeders;

use File;
use Illuminate\Database\Seeder;
use JsonException;
use October\Rain\Support\Facades\Schema;
use System\Models\MailLayout;

class MailLayoutSeeder extends Seeder
{
    /**
     * @throws JsonException
     */
    public function run(): void
    {
        $path = __DIR__.'/data/ocms-mail-layouts.json';
        $data = json_decode(File::get($path), true, 512, JSON_THROW_ON_ERROR);

        if (Schema::hasTable('system_mail_layouts')) {
            foreach ($data as $item) {
                MailLayout::updateOrCreate([
                    'id' => $item['id'],
                ], $item);
            }
        }
    }
}
