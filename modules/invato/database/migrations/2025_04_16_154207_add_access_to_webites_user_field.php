<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        $table = 'backend_users';
        $column = 'access_to_websites';

        if (! Schema::hasColumn($table, $column)) {
            Schema::table('backend_users', function ($table) {
                $table->json('access_to_websites')->nullable()->after('is_intranet_user');
            });
        }
    }

    public function down()
    {
        Schema::table('backend_users', function ($table) {
            $table->dropColumn('access_to_websites');
        });
    }
};
