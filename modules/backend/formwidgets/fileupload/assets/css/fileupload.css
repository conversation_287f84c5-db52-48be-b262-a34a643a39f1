.field-fileupload.is-image .upload-object.upload-object-image .file-data-container .file-data-container-inner{min-height:95px;padding-left:103px}.field-fileupload.is-image .upload-object.upload-object-image .icon-container{-moz-align-items:center;-ms-align-items:center;align-items:center;background:var(--oc-primary-bg);border:1px solid var(--bs-border-color);border-radius:4px;display:-moz-flex;display:-ms-flex;display:flex;height:95px;-moz-justify-content:center;-ms-justify-content:center;justify-content:center;left:0;overflow:hidden;position:absolute;top:0;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;width:95px}.field-fileupload.is-image .upload-object.upload-object-image .icon-container img{display:block;max-height:100%;max-width:100%}.field-fileupload.is-image .upload-object.upload-object-image.mode-multi .file-data-container{margin-left:23px}.field-fileupload.is-image .upload-object.upload-object-image.is-error .icon-container:after{speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-animation:none;animation:none;background:none;color:var(--bs-danger);content:"";content:"\f071";font-family:octo-icon!important;font-size:40px;font-style:normal;font-variant:normal;font-weight:400;line-height:1;margin-left:-20px;margin-top:-20px;text-shadow:1px 1px 0 hsla(0,0%,100%,.5);text-transform:none}.field-fileupload.is-image.is-preview .upload-object.upload-object-image.mode-multi .file-data-container{margin-left:0}.field-fileupload.is-file .upload-object.upload-object-file .file-data-container-inner{padding-top:4px}.field-fileupload.is-file .upload-object.upload-object-file .icon-container,.field-fileupload.is-file .upload-object.upload-object-file .info,.field-fileupload.is-file .upload-object.upload-object-file .info h4,.field-fileupload.is-file .upload-object.upload-object-file .info h4 p{display:inline-block;vertical-align:top}.field-fileupload.is-file .upload-object.upload-object-file .icon-container{height:20px;margin-top:-2px;position:absolute;width:20px}.field-fileupload.is-file .upload-object.upload-object-file .icon-container i{display:none;margin-left:3px;margin-top:4px}.field-fileupload.is-file .upload-object.upload-object-file .icon-container:after{background-size:20px 20px;height:20px;margin-left:-10px;margin-top:-10px;opacity:1;width:20px}.field-fileupload.is-file .upload-object.upload-object-file .info{padding-left:22px}.field-fileupload.is-file .upload-object.upload-object-file h4,.field-fileupload.is-file .upload-object.upload-object-file h4 p,.field-fileupload.is-file .upload-object.upload-object-file p.description{margin-bottom:0}.field-fileupload.is-file .upload-object.upload-object-file h4 p{font-weight:400}.field-fileupload.is-file .upload-object.upload-object-file h4 p:before{content:" ("}.field-fileupload.is-file .upload-object.upload-object-file h4 p:after{content:")"}.field-fileupload.is-file .upload-object.upload-object-file .form-check{top:6px}.field-fileupload.is-file .upload-object.upload-object-file.mode-multi .file-data-container{margin-left:43px}.field-fileupload.is-file .upload-object.upload-object-file.mode-multi .drag-handle{display:block;left:24px;top:0}.field-fileupload.is-file .upload-object.upload-object-file.is-success .icon-container i{display:inline-block}.field-fileupload.is-file .upload-object.upload-object-file.is-loading .icon-container{opacity:1}.field-fileupload.is-file .upload-object.upload-object-file.is-loading .icon-container>i{display:none}.field-fileupload.is-file .upload-object.upload-object-file.is-loading .icon-container>i:after{display:block}.field-fileupload.is-file .upload-object.upload-object-file.is-error .icon-container,.field-fileupload.is-file .upload-object.upload-object-file.is-error .icon-container>i{opacity:1}.field-fileupload.is-file .upload-object.upload-object-file.is-error .icon-container>i{display:inline-block}.field-fileupload.is-file .upload-object.upload-object-file.is-error .icon-container:after{display:none}.field-fileupload.is-file .upload-object.upload-object-file.is-error div.error-message{margin-left:22px}.field-fileupload.is-file.is-preview .upload-object.upload-object-file.mode-multi .file-data-container{margin-left:0}.field-fileupload .uploader-control-container{background:var(--oc-form-control-bg);border:1px solid var(--bs-border-color);border-radius:4px}.field-fileupload .uploader-control-container .uploader-control-toolbar{font-size:0;padding:3px;white-space:nowrap}.field-fileupload .uploader-control-container .uploader-control-toolbar .backend-toolbar-button.populated-only{display:none}.field-fileupload .uploader-control-container.external-toolbar{background:transparent}.field-fileupload .uploader-control-container.external-toolbar .uploader-control-toolbar{display:none}.field-fileupload.file-drag-over .uploader-control-container{border:1px solid var(--bs-primary);box-shadow:0 0 0 1px var(--bs-primary)}.field-fileupload.is-populated .uploader-control-toolbar{border-bottom:1px solid var(--oc-toolbar-border)}.field-fileupload.is-populated .uploader-control-toolbar .backend-toolbar-button.populated-only{display:inline-block}.field-fileupload.is-preview .uploader-control-container{background-color:var(--oc-form-control-disabled-bg)}.field-fileupload.is-preview .uploader-control-toolbar{display:none}:root,[data-bs-theme=light]{--oc-fileupload-checkerboard-bg:#fefefe;--oc-fileupload-checkerboard-alt-bg:#cbcbcb}[data-bs-theme=dark]{--oc-fileupload-checkerboard-bg:#010101;--oc-fileupload-checkerboard-alt-bg:#343434}.fileupload-config-form .fileupload-url-button{padding-left:0}.fileupload-config-form .fileupload-url-button>i{color:#666;margin-right:0}.fileupload-config-form .file-upload-modal-image-header{background-color:var(--oc-fileupload-checkerboard-bg);background-image:linear-gradient(45deg,var(--oc-fileupload-checkerboard-alt-bg) 25%,transparent 25%,transparent 75%,var(--oc-fileupload-checkerboard-alt-bg) 75%,var(--oc-fileupload-checkerboard-alt-bg)),linear-gradient(45deg,var(--oc-fileupload-checkerboard-alt-bg) 25%,transparent 25%,transparent 75%,var(--oc-fileupload-checkerboard-alt-bg) 75%,var(--oc-fileupload-checkerboard-alt-bg));background-position:0 0,10px 10px;background-size:20px 20px}.fileupload-config-form .file-upload-modal-image-header,.fileupload-config-form .file-upload-modal-image-header img{border-top-left-radius:2px;border-top-right-radius:2px}.fileupload-config-form .file-upload-modal-image-header .btn-close{background-color:hsla(0,0%,100%,.5);border-radius:100px;height:24px;position:absolute;right:10px;top:10px;width:22px}.fileupload-config-form .file-upload-modal-image-header+.modal-body{padding-top:20px}.field-fileupload.size-tiny .upload-files-container{max-height:100px;overflow:auto}.field-fileupload.size-small .upload-files-container{max-height:200px;overflow:auto}.field-fileupload.size-large .upload-files-container{max-height:400px;overflow:auto}.field-fileupload.size-huge .upload-files-container{max-height:500px;overflow:auto}.field-fileupload.size-giant .upload-files-container{max-height:700px;overflow:auto}.field-fileupload.size-auto{overflow:auto}.field-fileupload.size-adaptive{background:var(--bs-body-bg);height:100%;position:absolute;width:100%}.field-fileupload.size-adaptive .uploader-control-container{border:none!important;padding:14px}.field-fileupload.size-adaptive .empty-state{display:block;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%)}.field-fileupload.size-adaptive.is-populated .empty-state{display:none}.field-fileupload .upload-object{display:block;margin:4px 4px 0;outline:none;position:relative}.field-fileupload .upload-object:last-child{margin-bottom:4px}.field-fileupload .upload-object h4.filename,.field-fileupload .upload-object p{color:var(--oc-toolbar-color);font-size:14px;line-height:1.4;margin-bottom:8px;word-break:break-all}.field-fileupload .upload-object h4.filename{font-weight:600;margin-top:-2px}.field-fileupload .upload-object p.description{margin-top:-4px}.field-fileupload .upload-object p.description:empty{display:none}.field-fileupload .upload-object p.size{color:var(--bs-secondary-color);font-size:.875em}.field-fileupload .upload-object.upload-object-image h4.filename{padding-top:10px}.field-fileupload .upload-object .file-data-container{border-radius:4px;padding:3px}.field-fileupload .upload-object .file-data-container:hover{background:var(--oc-toolbar-hover-bg)}.field-fileupload .upload-object .file-data-container .file-data-container-inner{position:relative}.field-fileupload .upload-object.selected .icon-container i{color:#fff}.field-fileupload .upload-object.selected .file-data-container{background:var(--oc-selection)}.field-fileupload .upload-object.selected .file-data-container div.error-message,.field-fileupload .upload-object.selected .file-data-container h4.filename,.field-fileupload .upload-object.selected .file-data-container p{color:#fff}.field-fileupload .upload-object .drag-handle{color:#72809d;cursor:move;display:none;font-size:24px;left:-1px;position:absolute;text-decoration:none;top:23px}.field-fileupload .upload-object:hover .drag-handle{display:block}.field-fileupload .upload-object .form-check{left:3px;position:absolute;top:2px}.field-fileupload .empty-state,.field-fileupload.is-preview .upload-object .form-check{display:none}.field-fileupload .upload-object .icon-container:after{-webkit-animation:spin 1s linear infinite;animation:spin 1s linear infinite;background-image:url(../../../../../backend/assets/images/loader-transparent.svg);background-position:50% 50%;background-size:40px 40px;content:" ";display:block;height:40px;left:50%;margin-left:-20px;margin-top:-20px;position:absolute;top:50%;width:40px}.field-fileupload .upload-object.is-success .icon-container{opacity:1}.field-fileupload .upload-object.is-success .icon-container:after{display:none}.field-fileupload .upload-object.is-loading .icon-container{opacity:.6}.field-fileupload .upload-object.is-loading .icon-container:after{display:block}.field-fileupload .upload-object.is-success .file-data-container-inner{cursor:pointer}.field-fileupload .upload-object.is-success .progress-bar{opacity:0;transition:opacity .3s ease}.field-fileupload .upload-object.is-error .file-data-container-inner{cursor:pointer}.field-fileupload .upload-object.is-error .icon-container{opacity:1}.field-fileupload .upload-object.is-error .icon-container>i,.field-fileupload .upload-object.is-error .icon-container>img{opacity:.5}.field-fileupload .upload-object.is-error .info h4{color:var(--bs-danger)}.field-fileupload .upload-object.is-error .info h4 a{display:none}.field-fileupload .upload-object.is-error div.error-message{font-size:.875em}.field-fileupload .upload-object.is-error .drag-handle,.field-fileupload.is-preview .drag-handle,.field-fileupload.is-preview .upload-button{display:none!important}@media (min-width:768px){.field-fileupload.is-grid .upload-files-container{display:flex;flex-wrap:wrap}.field-fileupload.is-grid .upload-files-container .upload-object{width:300px}.field-fileupload.is-grid .upload-files-container .upload-object h4.filename,.field-fileupload.is-grid .upload-files-container .upload-object p{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}}
