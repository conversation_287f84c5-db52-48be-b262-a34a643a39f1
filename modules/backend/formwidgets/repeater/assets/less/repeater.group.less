.field-repeater > .field-repeater-builder > .field-repeater-groups {
    padding-top: 10px !important;
}

.field-repeater > .field-repeater-builder > ul > li.field-repeater-group {
    position: relative;
    min-height: 95px;
    display: block;
    background: @repeater-item-bg;
    border: 1px solid @input-border;
    border-radius: @border-radius-base;
    box-shadow: @input-box-shadow;
    cursor: pointer;

    &.is-placeholder {
        padding: 15px 10px 0;
    }

    &.is-selected {
        background: @repeater-item-bg;
    }

    > .group-controls {
        background: @repeater-menu-bg;
        padding: 5px;
        padding-right: 3px;

        display: flex;
        flex-direction: column-reverse;
        width: auto;
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        border-bottom: none;
        border-left: 1px solid @input-border;
        border-top-left-radius: 0;
        border-top-right-radius: @border-radius-base;
        border-bottom-right-radius: @border-radius-base;

        .repeater-item-title {
            display: none;
        }

        a.repeater-item-menu,
        a.repeater-item-handle {
            margin: 2px 0;
        }

        .repeater-item-checkbox {
            flex-grow: 1;
            .checkbox {
                padding-left: 26px;
            }
        }
    }

    > .group-image > i {
        position: absolute;
        left: 11px;
        top: 11px;
        font-size: 20px;
        color: @toolbar-color;
    }

    > .group-title,
    > .group-description {
        display: block;
        padding-left: 10px;
        padding-right: 45px;
    }

    > .group-title {
        padding-left: 40px;
        padding-top: 10px;
        color: @toolbar-color;
    }

    > .group-description {
        font-size: .875em;
        padding-top: 10px;
        padding-bottom: 10px;
        color: @color-grey-2;
    }
}

// Selected
.field-repeater > .field-repeater-builder > ul > li.field-repeater-group.is-selected {
    border-color: @brand-primary;
}
