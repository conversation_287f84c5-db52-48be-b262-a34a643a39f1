//
// Basic Loader
// --------------------------------------------------

a.oc-loading, button.oc-loading, span.oc-loading {
    &:after {
        content: '';
        display: inline-block;
        vertical-align: middle;
        margin-left: .4em;
        height: 1em;
        width: 1em;
        animation: spin 0.8s infinite linear;
        border: .2em solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        opacity: .5;
    }
}

//
// Loader Container
// --------------------------------------------------

.control-loader-container {
    position: relative;

    &.is-loading {
        opacity: .5;
    }

    .loader-mask {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: rgba(255,255,255,0);
    }
}

//
// Bar loading indicator
//
// Usage:
//   <div class="progress bar-loading-indicator">
//       <div class="progress-bar" role="progressbar"></div>
//   </div>
//
// --------------------------------------------------

.bar-loading-indicator {
    transition: opacity .4s linear;

    .progress-bar {
        animation: infinite-loader 90s ease-in forwards;
        transition-duration: 0s;
    }

    &.bar-loaded {
        opacity: 0;
        transition-delay: .3s;

        .progress-bar {
            width: 100% !important;
            transition: width .3s linear;
            animation: none;
        }
    }
}

//
// Infinite loading animation
// --------------------------------------------------

@keyframes infinite-loader {
    0% { width: 0%; }
    10% { width: 42%; }
    20% { width: 63%; }
    30% { width: 78.75%; }
    40% { width: 88.59375%; }
    50% { width: 94.13085938%; }
    60% { width: 97.07244873%; }
    70% { width: 98.58920574%; }
    80% { width: 99.35943391%; }
    90% { width: 99.7475567%; }
    100% { width: 99.94237615%; }
}

//
// Spin animation
// --------------------------------------------------

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(359deg); }
}

@keyframes rspin {
    0% { transform: rotate(359deg); }
    100% { transform: rotate(0deg); }
}
