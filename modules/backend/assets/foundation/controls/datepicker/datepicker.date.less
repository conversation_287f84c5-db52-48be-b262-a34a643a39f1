//
// Date Picker
// --------------------------------------------------

.pika-single {
    display: block;
    position: relative;
    width: 240px;
    padding: 8px;

    &.is-hidden {
        display: none;
    }
    &.is-bound {
        position: absolute;
        box-shadow: 0 5px 15px -5px rgba(0,0,0,.5);
    }
}

.pika-title, .pika-footer {
    position: relative;
    text-align: center;
}

.pika-footer {
    margin: 5px auto 0;
}

.pika-label {
    display: inline-block;
    position: relative;
    z-index: @zindex-datepicker;
    overflow: hidden;
    margin: 0;
    padding: 5px 3px;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;
    background-color: @datepicker-bg;
}

.pika-footer, .pika-title {
    select {
        cursor: pointer;
        position: absolute;
        z-index: @zindex-datepicker - 1;
        margin: 0;
        left: 0;
        top: 5px;
        opacity: 0;
    }
}

.pika-prev,
.pika-next {
    display: block;
    cursor: pointer;
    position: relative;
    outline: none;
    border: 0;
    padding: 0;
    width: 20px;
    height: 30px;
    background-color: transparent;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 75% 75%;
    white-space: nowrap;
    text-indent: 100%;
    overflow: hidden;
    opacity: .5;

    &:hover {
        opacity: 1;
    }
}

.pika-prev,
.is-rtl .pika-next {
    float: left;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAUklEQVR42u3VMQoAIBADQf8Pgj+OD9hG2CtONJB2ymQkKe0HbwAP0xucDiQWARITIDEBEnMgMQ8S8+AqBIl6kKgHiXqQqAeJepBo/z38J/U0uAHlaBkBl9I4GwAAAABJRU5ErkJggg==');
}

.pika-next,
.is-rtl .pika-prev {
    float: right;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAU0lEQVR42u3VOwoAMAgE0dwfAnNjU26bYkBCFGwfiL9VVWoO+BJ4Gf3gtsEKKoFBNTCoCAYVwaAiGNQGMUHMkjGbgjk2mIONuXo0nC8XnCf1JXgArVIZAQh5TKYAAAAASUVORK5CYII=');
}

.pika-prev.is-disabled,
.pika-next.is-disabled {
    cursor: default;
    opacity: .2;
}

.pika-select {
    display: inline-block;
}

.pika-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    border: 0;

    th, td {
        width: 14.285714285714286%;
    }

    th {
        color: #999;
        font-size: 12px;
        line-height: 25px;
        font-weight: bold;
        text-align: center;
    }
}

.pika-button {
    cursor: pointer;
    display: block;
    outline: none;
    border: 0;
    margin: 0;
    width: 100%;
    padding: 5px;
    color: #666;
    font-size: 12px;
    line-height: 15px;
    text-align: right;
    background: #f5f5f5;
}

.is-today .pika-button {
    color: #33aaff;
    font-weight: bold;
}

.is-selected .pika-button {
    color: #fff;
    font-weight: bold;
    background: #33aaff;
    box-shadow: inset 0 1px 3px #178fe5;
    border-radius: 3px;
}

.is-disabled .pika-button {
    pointer-events: none;
    cursor: default;
    color: #999;
    opacity: .3;
}

.pika-button:hover {
    color: #fff !important;
    background: #ff8000 !important;
    box-shadow: none !important;
    border-radius: 3px !important;
}
