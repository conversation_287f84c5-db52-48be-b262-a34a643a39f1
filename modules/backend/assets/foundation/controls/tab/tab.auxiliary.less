//
// Auxiliary tabs
//

.control-tabs.auxiliary-tabs {
    background: white;

    .border-top(@color) {
        content: ' ';
        display: block;
        position: absolute;
        width: 100%;
        height: 1px;
        background: @color;
        top: 0;
        left: 0;
    }

    > ul.nav-tabs, > div > ul.nav-tabs {
        padding-left: 20px;
        padding-bottom: 2px;
        background: white;
        position: relative;

        &:before {
            .border-top(#95a5a6);
        }

        > li {
            margin-right: 2px;

            > a {
                background: white;
                color: #bdc3c7;
                border-left: 1px solid #ecf0f1!important;
                border-right: 1px solid #ecf0f1!important;
                border-bottom: 1px solid #ecf0f1!important;
                padding: 4px 10px;
                line-height: 100%;
                .border-radius(0 0 4px 4px);

                > span.title > span {
                    margin-bottom: 0;
                    font-size: 13px;
                    height: auto;
                }
            }

            &.active{
                top: 0;

                &:before {
                    .border-top(white);
                    top: -1px;
                }

                a {
                    padding-top: 5px;
                    border-left: 1px solid #95a5a6!important;
                    border-right: 1px solid #95a5a6!important;
                    border-bottom: 1px solid #95a5a6!important;
                    color: #95a5a6;
                }
            }
        }
    }

    > div.tab-content > .tab-pane {
        background: white;
    }
}
