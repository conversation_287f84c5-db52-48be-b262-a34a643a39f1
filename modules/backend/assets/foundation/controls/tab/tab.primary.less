//
// Primary tabs
//

.control-tabs.primary-tabs {
    margin-bottom: 5px;

    > ul.nav-tabs, > div > ul.nav-tabs, > div > div > ul.nav-tabs {
        position: relative;
        margin-left: 0;
        margin-right: 0;

        &:before {
            position: absolute;
            bottom: 0;
            height: 2px;
            width: 100%;
            z-index: @zindex-tab - 1;
            content: ' ';
            border-bottom: 2px solid @tab-border;
        }

        > li {
            padding-right: 0;
            padding-left: 0;
            margin-left: 0;
            margin-right: -10px;
            background: transparent;

            &:first-child {
                padding-left: @padding-standard !important;
            }

            &:last-child {
                margin-right: 0;
            }

            a {
                font-size: @font-size-base;
                font-weight: 500;
                padding-bottom: 3px;
                margin: 0;
                position: relative;
                top: -1px;
                z-index: @zindex-tab + 1;
                background: transparent;
                overflow: visible;
                border-bottom: 1px solid transparent;

                > span.title {
                    position: relative;
                    display: inline-block;
                    padding: 4px 25px 0px;
                    box-sizing: border-box;
                    z-index: @zindex-tab;

                    > span {
                        padding-top: 7px;
                        margin-top: -4px;
                        border-top: 2px solid transparent;
                    }

                    &:before, &:after {
                        content: '';
                        display: none;
                        border-top: 2px solid @tab-border;
                        position: absolute;
                        background: transparent;
                        top: 0;
                        z-index: -1;
                        width: 20px;
                        bottom: -2px;
                        transform-origin: bottom;
                    }

                    &:before {
                        left: 0;
                        border-left: 2px solid @tab-border;
                        border-radius: 8px 0 0 0;
                        transform: skewX(-10deg);
                    }

                    &:after {
                        right: 0;
                        border-right: 2px solid @tab-border;
                        border-radius: 0 8px 0 0;
                        transform: skewX(10deg);
                    }
                }

            }

            // &:hover {
            //     a, a > span.title {
            //         color: @tab-active-color;
            //     }
            // }

            &:not(.active) {
                a:before {
                    content: '';
                    left: 12px;
                    right: 12px;
                    top: 6px;
                    bottom: 3px;
                    border-radius: 4px;
                    position: absolute;
                    background: @tab-hover-bg;
                    opacity: 0;
                    transition: none;

                }
                &:hover a:before {
                    transition: opacity 0.1s ease-out, color 0.1s ease-out;
                    opacity: 1;
                }
            }

            &:last-child {
                background-image: none;
                margin-right: 0;
                padding-right: 5px;
            }

            &:first-child {
                padding-left: 0;
            }

            &.active a {
                z-index: @zindex-tab + 3;
                top: 0;

                > span.title {
                    z-index: @zindex-tab + 2;
                    border-top-color: #d6d6d6;

                    &:before, &:after {
                        display: block;
                        border-color: @tab-border;
                    }

                    span {
                        border-top-color: @tab-border;
                    }
                }

                &:before {
                    position: absolute;
                    bottom: -1px;
                    height: 2.4px;
                    right: 2px;
                    left: 2px;
                    content: ' ';
                    background-color: @body-bg;
                }
            }
        }
    }

    // Primary tabs should use inset by default, unless otherwise specified
    > ul.nav-tabs, > div > ul.nav-tabs, > div > div > ul.nav-tabs {
        margin-left: -(@padding-standard);
        margin-right: -(@padding-standard);
    }

    // Tab divider to sit inset the standard padding (20px)
    &.tabs-no-inset {
        > ul.nav-tabs, > div > ul.nav-tabs, > div > div > ul.nav-tabs {
            margin-left: 0;
            margin-right: 0;
        }
    }
}
