&.style-inspector {
    > .tablist-container {
        background: transparent;
        padding-top: 15px;

        .tablist {
            &:after {
                .full-width-border();
                background-color: @primary-border;
                z-index: 0;
            }

            button {
                overflow: visible;
                text-overflow: clip;
                color: @toolbar-color;
                font-weight: 600;

                // Important - setting anything else for z-index
                // breaks styles for Inspector dropdown controls
                // overlapping tabs.
                //
                z-index: auto;

                margin-right: 15px;
                padding: 0 0 4px;

                &.active {
                    color: @brand-primary;

                    &:after {
                        .full-width-border();
                        z-index: 1;
                        background-color: @brand-primary;
                    }
                }

                &:first-child {
                    margin-left: 0;
                }

                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }
}