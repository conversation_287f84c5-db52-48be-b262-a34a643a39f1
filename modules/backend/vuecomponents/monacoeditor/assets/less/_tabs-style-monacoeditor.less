.component-backend-tabs {
    &.style-monaco-editor {
        > .tablist-container {
            .tablist {
                button {
                    font-weight: 600;
                    padding-top: 7px;
                    padding-bottom: 7px;

                    span.tab-icon {
                        top: 9px;
                        box-shadow: none !important;

                        .tab-icon-background {
                            background-color: transparent !important;

                            i {
                                left: 0;
                                top: 0;
                            }
                        }
                    }
                }
            }
        }

        &.monaco-tabs-vs-dark {
            > .tablist-container {
                background: #2d2d2d;

                .tablist {
                    button {
                        border-right: 1px solid @darkThemeEditorBackground;
                        color: #969696;

                        &.active {
                            color: white;
                            background: @darkThemeEditorBackground;
                        }
                    }
                }
            }
        }

        &.monaco-tabs-hc-black {
            > .tablist-container {
                background: #2d2d2d;

                .tablist {
                    button {
                        border-right: 1px solid @blackThemeEditorBackground;
                        color: #969696;

                        &.active {
                            color: white;
                            background: @blackThemeEditorBackground;
                        }
                    }
                }
            }
        }

        &.monaco-tabs-vs {
            > .tablist-container {
                .tabs-scrollable {
                    overflow: visible;
                }

                border-bottom: 1px solid @primary-border;

                .tablist {
                    button {
                        border-right: 1px solid @primary-border;
                        overflow: visible;

                        &.active {
                            color: @color-grey-7;
                            span[role=text]:after {
                                position: absolute;
                                content: '';
                                width: 100%;
                                height: 1px;
                                bottom: -1px;
                                left: 0;

                                background-color: @whiteThemeEditorBackground;
                            }
                        }
                    }
                }
            }
        }
    }
}
