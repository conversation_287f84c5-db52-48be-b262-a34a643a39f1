@import "../../../../assets/less/core/boot.less";

@import "_header.less";
@import "_contents.less";
@import "_toolbar.less";

.component-backend-document {
    &.full-screen {
        background: @body-bg;
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: @zindex-dropdown; // Should this go to the document LESS?
    }

    .document-fade-in-enter-active {
        transition: opacity 0.3s;
    }

    .document-fade-in-enter {
        opacity: 0;
    }
}

body.backend-document-layout {
    // Fixes Edge/IE11 page horizontal toolbars
    //
    overflow-x: hidden;
}

body.component-backend-document-fullscreen {
    overflow: hidden;
}