ul {
    li {
        display: block;
        white-space: nowrap;

        .node-label {
            display: block;
            color: @toolbar-color;
            position: relative;
            z-index: 1;
            vertical-align: top;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
        }

        > .item-label-outer-container {
            // Important: the `.scrollable > div` element must be an offset
            // parent for .item-label-outer-container
            //
            &:before {
                content: '';
                position: absolute;
                left: 0;
                width: 100%;
                height: @treeview-node-height;
                border-radius: 4px;
                z-index: 0;
            }

            > .item-label-container {
                cursor: default;
                position: relative;
                line-height: @treeview-node-height;
                font-size: @font-size-base;
                vertical-align: top;
            }
        }

        &.root-node > .item-label-outer-container > .item-label-container .node-label {
            color: @editor-section-color;
        }

        &:focus {
            outline: none;

            > .item-label-outer-container:before {
                background-color: @toolbar-focus-bg;
            }
        }

        &.selectable-node:not(.selected-node) {
            > .item-label-outer-container:hover:before {
                background-color: @toolbar-focus-bg;
            }

            &.context-menu-visible > .item-label-outer-container:before {
                background-color: @toolbar-focus-bg;
            }
        }

        &.selected-node {
            > .item-label-outer-container {
                &:before {
                    background: @brand-selection;
                }

                > .item-label-container {
                    &,
                    &.root-node {
                        .node-label {
                            color: white;
                        }
                    }
                }
            }

            &:focus > .item-label-outer-container:before {
                border: 2px solid rgba(0, 0, 0, 0.1);
            }
        }

        &.selectable-node > .item-label-outer-container > .item-label-container {
            cursor: pointer;
        }

        // Multi-node selection - remove round corners between adjacent nodes
        //
        &.selected-node:not(.expanded-with-children) + li.selected-node,
        &.selected-node > ul[data-subtree] > li.selected-node:first-child {
            > .item-label-outer-container:before {
                border-top-left-radius: 0;
                border-top-right-radius: 0;
            }
        }

        &.selected-node.next-is-selected {
            > .item-label-outer-container:before {
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
            }
        }

        // Alignment of the collapse button, icon and label text
        //
        &.mode-tree,
        &.root-node {
            > .item-label-outer-container > .item-label-container {
                .node-label {
                    padding-left: @treeview-toggle-control-width + @treeview-toggle-control-margin;
                }

                &.has-icon .node-label {
                    padding-left: @treeview-toggle-control-width + @treeview-toggle-control-margin + @treeview-icon-size +
                        @treeview-icon-margin;
                }
            }
        }

        > .item-label-outer-container > .item-label-container.has-icon .node-label {
            padding-left: @treeview-icon-size + @treeview-icon-margin;
        }
    }
}

.treeview-readonly {
    ul {
        li {
            .node-label {
                opacity: 0.7;
            }
        }
    }
}
