ul li {
    &.mode-tree, &.root-node {
        > ul {
            padding-left: @treeview-toggle-control-width + @treeview-toggle-control-margin;
        }
    }

    .node-toggle-control {
        -webkit-appearance: none;
        display: inline-block;
        border: none;
        background: transparent;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 2;
        padding: 0;
        margin: 0;
        width: @treeview-toggle-control-width;
        height: @treeview-node-height;
        margin-right: @treeview-toggle-control-margin;
        vertical-align: top;
        outline: none;

        &:before {
            position: absolute;
            width: 10px;
            height: 6px;
            left: 3px;
            top: 12px;
            background-position: -182px 0;
            transform: rotate(-90deg);
            transition: transform @treeview-transition-duration;
        }

        &.no-child-nodes {
            opacity: 0;
            cursor: inherit;
        }
    }

    &.expanded-node > .item-label-outer-container > .item-label-container .node-toggle-control {
        &:before {
            transform: rotate(0);
        }
    }

    &.selected-node > .item-label-outer-container > .item-label-container .node-toggle-control:before {
        background-position: -182px -8px;
    }
}