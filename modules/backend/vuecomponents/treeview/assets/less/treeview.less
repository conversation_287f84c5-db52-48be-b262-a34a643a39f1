@import "../../../../assets/less/core/boot.less";

.component-backend-treeview {
    @treeview-node-height: 30px;
    @treeview-transition-duration: 0.1s;
    @treeview-toggle-control-width: 16px;
    @treeview-toggle-control-margin: 3px;
    @treeview-contents-padding: 8px;
    @treeview-icon-size: 16px;
    @treeview-icon-margin: 3px;

    @import "_node.less";
    @import "_section.less";
    @import "_node-expand-toggle.less";
    @import "_node-icon.less";
    @import "_node-menu.less";
    @import "_drag-drop.less";
    @import "_search.less";
    @import "_quick-access.less";

    background: @editor-bg;
    user-select: none;

    .scrollable-panel-container {
        background: @editor-bg;

        &.scrolled:before {
            z-index: 1;
        }
    }

    .treeview-container {
        ul {
            padding: 0;
            margin: 0;
            list-style: none;
            font-size: 0;
        }

        .scrollable {
            > div {
                margin: 0 @treeview-contents-padding @treeview-contents-padding @treeview-contents-padding;

                // Important: this element must be an offset
                // parent for .item-label-container inside nodes
                //
                position: relative;
            }
        }
    }
}
