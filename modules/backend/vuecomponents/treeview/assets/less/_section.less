ul {
    li {
        > .item-label-outer-container {
            &.section-label {
                background: @editor-section-bg;
                margin: 0 -@treeview-contents-padding;
                padding: 0 @treeview-contents-padding;
                position: sticky;
                margin-bottom: 2px;
                z-index: 3;
                top: 0;

                .node-label {
                    cursor: pointer;
                    color: @editor-section-color;
                    font-weight: 600;
                    padding-left: @treeview-toggle-control-width + @treeview-toggle-control-margin;
                }
            }

            > .item-label-container.has-menu {
                .node-menu-trigger.section-create-button {
                    right: @treeview-node-height;
                    z-index: 2;

                    &:before {
                        width: 11px;
                        height: 11px;
                        top: 9px;
                        left: 9px;
                        position: absolute;
                        background-position: -267px -40px;
                    }
                }
            }
        }
    }
}
