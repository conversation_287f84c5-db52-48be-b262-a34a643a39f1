<div
    v-bind:tabindex="containerTabIndex"
    @focus="onContainerFocus"
    class="inspector-table-dropdown-container"
>
    <backend-component-dropdown
        :options="options"
        :placeholder="column.placeholder"
        :tabindex="0"
        :disabled="inspectorPreferences.readOnly"
        :allow-empty="true"
        track-by="code"
        label="label"
        ref="editor"
        v-model="selectedValue"
        select-label=""
        selected-label=""
        deselect-label=""
        @input="updateValue"
        @open="onFocus"
        @close="onBlur"
    >
        <span slot="noResult"><?= e(trans('backend::lang.form.no_options_found')) ?></span>
    </backend-component-dropdown>

    <div class="dropdown-placeholder" v-if="!hasValue" v-text="column.placeholder"></div>
</div>