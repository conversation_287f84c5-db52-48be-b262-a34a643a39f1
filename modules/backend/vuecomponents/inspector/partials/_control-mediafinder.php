<div
    data-lang-error-not-image="<?= e(trans('backend::lang.mediafinder.not_an_image')) ?>"
    data-lang-placeholder="<?= e(trans('backend::lang.mediafinder.click_to_select_file')) ?>"
    class="inspector-control-clearable"
>
    <div
        class="inspector-control clickable act-as-text-input"
        :class="cssClass"
        :tabindex="0"
        @focus="onFocus"
        @blur="onBlur"
        ref="input"
        v-text:value="displayedText"
        v-bind:readonly="inspectorPreferences.readOnly"
        @click.stop="onClick"
        @keydown.stop="onKeyDown"
    ></div>

    <button v-if="this.value && !inspectorPreferences.readOnly" @click.stop="onClearClick" class="clear-value"></button>
</div>