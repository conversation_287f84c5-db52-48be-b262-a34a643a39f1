<div class="component-backend-inspector-grouphost">
    <backend-component-inspector-controlhost
        :controls="groupedControls.ungrouped"
        :obj="obj"
        :parent-obj="parentObj"
        :splitter-data="splitterData"
        :depth="depth"
        :panel-update-data="panelUpdateData"
        :layout-update-data="layoutUpdateData"
        :inspector-unique-id="inspectorUniqueId"
        :inspector-preferences="inspectorPreferences"
    >
    </backend-component-inspector-controlhost>

    <backend-component-inspector-group
        v-for="(controls, groupName) in groupedControls"
        v-if="groupName != 'ungrouped'"
        :key="groupName"
        :group-name="groupName"
        :obj="obj"
        :parent-obj="parentObj"
        :controls="controls"
        :splitter-data="splitterData"
        :depth="depth"
        :panel-update-data="panelUpdateData"
        :layout-update-data="layoutUpdateData"
        :inspector-unique-id="inspectorUniqueId"
        :inspector-preferences="inspectorPreferences"
    >
    </backend-component-inspector-group>
</div>