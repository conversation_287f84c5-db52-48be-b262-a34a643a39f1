div.multiselect {
    font-size: @font-size-base;
    color: inherit;
    min-height: 0;

    &, .multiselect__input, .multiselect__single {
        font-size: @font-size-base;
    }

    &.multiselect--disabled, .multiselect--disabled {
        &, .multiselect__select {
            background: transparent;
            opacity: 1;
            color: @color-grey-6-darker;
        }

        .multiselect__select {
            opacity: 0.5;
        }
    }

    .multiselect__content-wrapper {
        z-index: 1000;
    }

    .multiselect__single {
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        white-space: nowrap;
    }

    // Placeholder
    span > .multiselect__single {
        color: @inspector-placeholder-color;
    }

    .multiselect__input, .multiselect__single {
        line-height: @inspector-contol-height;
        height: @inspector-contol-height;
        padding: 0;
        margin: 0;
    }

    .multiselect__input::placeholder {
        color: @inspector-placeholder-color!important;
    }

    .multiselect__content-wrapper {
        margin-left: -1px;
        margin-top: 3px;
        border-radius: 4px;
        box-sizing: content-box;
        border-color: @primary-border;
        border-top: 1px solid @primary-border;
        padding: 3px;
        .box-shadow(@box-shadow-z1);

        ul.multiselect__content {
            display: block!important;

            li span.multiselect__option {
                min-height: 30px;
                line-height: 30px;
                padding: 0 @inspector-base-control-side-padding;
                border-radius: 2px;

                &.multiselect__option--highlight {
                    background: @brand-selection;
                }

                &.multiselect__option--selected {
                    font-weight: 600;
                }

                span {
                    display: block;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }
            }
        }
    }

    .multiselect__tags {
        border: none;
        padding: 0 15px 0 @inspector-base-control-side-padding;
        margin: 0;
        line-height: @inspector-contol-height;
        min-height: @inspector-contol-height;
        height: @inspector-contol-height;
    }

    .multiselect__select {
        top: 0;
        right: 0;
        height: @inspector-contol-height;
        width: 15px;
        margin: 0;
        padding: 0;
        transition: none;

        &:before {
            width: 10px;
            height: 6px;
            margin: 0;
            right: 0;
            top: 9px;
            border: none;
            display: inline-block;
            background-position: -182px 0;
        }
    }

    &.multiselect--above {
        .multiselect__content-wrapper {
            margin-top: 0;
            margin-bottom: 3px;
            border-bottom: 1px solid @primary-border;
        }
    }

    &.multiselect--active {
        .multiselect__select:before {
            top: 7px;
        }
    }

    .multiselect-enter-active,
    .multiselect-leave-active {
        transition: none;
    }

    .option-with-icon {
        display: flex;

        .option-icon {
            width: 26px;
            font-size: 22px;
            position: relative;

            &:before {
                top: 4px;
                left: -4px;
                position: absolute;
            }
        }
    }

    .option-with-color {
        display: flex;

        .option-color {
            width: 18px;
            height: 18px;
            position: relative;
            top: 5px;
            border: 1px solid white;
            margin-right: 5px;
        }
    }
}

// Override vendor styles
div.multiselect {
    .multiselect__input,
    .multiselect__single,
    .multiselect__tags {
        background: transparent;
        color: @input-color;
    }
    .multiselect__content-wrapper {
        background: @input-bg;
    }
    .multiselect__option--selected {
        color: @secondary-color;
        background: @tertiary-bg;
    }
    .multiselect__option--highlight {
        color: #fff;
    }
}
