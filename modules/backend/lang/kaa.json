{"Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "You are viewing as": "You are viewing as", "Preview Role": "Preview Role", "This lets you test the actions this role can perform.": "This lets you test the actions this role can perform.", "View As Role": "View As Role", "Return to Role Settings": "Return to <PERSON> Settings", "Password must have a minimum of length of :min characters": "Password must have a minimum of length of :min characters", "Password must contain at least one uppercase character.": "Password must contain at least one uppercase character.", "Password must contain at least one lowercase character.": "Password must contain at least one lowercase character.", "Password must contain at least one number.": "Password must contain at least one number.", "Password must contain at least one nonalphanumeric character.": "Password must contain at least one nonalphanumeric character.", "Welcome to your Administration Area, :name": ":name, administrator maydanıńızǵa xosh keld<PERSON>", "Duplicate": "Nusqalaw", "Expand": "Keń<PERSON>tiw", "Collapse": "Qısqartıw", "Move Up": "Move Up", "Move Down": "Move Down", "Remove": "<PERSON><PERSON><PERSON><PERSON>", "Manage Item": "<PERSON><PERSON><PERSON>", "Clear Filters": "<PERSON><PERSON>rdi tazalaw", "Filter Setup": "Filtr sazlawı", "Apply": "Qollanıw", "Clear": "Tazalaw", "is equal to": "teń", "is between": "aralıǵında", "contains": "óz ishine aladı", "is before": "deyin", "is after": "keyin", "is greater than": "den kóp", "is less than": "den az", "1. Export output format": "1. Eksport formatın <PERSON>", "File Format": "Fayl formatı", "Delimiter Character": "Delimiter Character", "Enclosure Character": "Enclosure Character", "Escape Character": "Escape Character", "2. Select columns to export": "2. Select columns to export", "Columns": "Columns", "3. Set export options": "3. Set export options", "CSV Custom": "Arnawlı CSV", "1. Upload an Import File": "1. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Import File": "Fayldı importlaw", "File Encoding": "File Encoding", "First row contains column titles": "First row contains column titles", "Leave this checked if the first row in the CSV is used as the column titles.": "Leave this checked if the first row in the CSV is used as the column titles.", "2. Match the File Columns to Database Fields": "2. Match the File Columns to Database Fields", "File columns": "File columns", "Database fields": "Maǵlıwmatlar bazasın<PERSON>ń qatarları", "3. Set Import Options": "3. Set Import Options", "Export progress": "Export progress", "Processing": "Processing", "Import progress": "Import progress", "Import error": "<PERSON><PERSON><PERSON><PERSON>", "Export error": "Eksportlaw qátesi", "Column preview": "Column preview", "Column": "Qatar", "Disable": "<PERSON><PERSON><PERSON><PERSON>", "Enable": "Qosıw", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Export": "Eksportlaw", "Import": "Importlaw", "Select Action": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "Please create a new account for logging in to the Administration Area.": "<PERSON><PERSON><PERSON>, administrator paneline kiriw ushın ja<PERSON>a akkaunt jaratıw.", "First Name": "Atı", "Last Name": "Familiyası", "Email Address": "Elektron pochta m<PERSON>", "Pick a Username": "Paydalanıwshı atın tańlań", "Enter New Password": "Jańa parol kirgiziń", "Confirm Password": "<PERSON><PERSON><PERSON>", "Create Account": "<PERSON><PERSON><PERSON>", "Getting Ready": "Getting Ready", "Just a few more minutes": "<PERSON><PERSON><PERSON> minut", "Migrating Database": "Migrating Database", "Access the Backend Panel": "Access the Backend Panel", "View Backend During Maintenance": "Xızmet kórsetiw waqtında administrator panelin kóriw", "Perform Software Updates": "Perform Software Updates", "In Maintenance": "Xızmet kórsetiw rejiminde", "The site is currently in maintenance mode, please check back later.": "<PERSON>ul sayt házirgi waqıtta xızmet kórsetiw rejiminde, <PERSON><PERSON><PERSON>, key<PERSON><PERSON> kiri<PERSON>.", "Dashboard": "Dashboard", "Administrators": "<PERSON><PERSON>", "Manage Admins": "<PERSON><PERSON><PERSON><PERSON> basqa<PERSON><PERSON>w", "Create Admins": "<PERSON><PERSON><PERSON>", "Moderate Admins": "Moderate Admins", "Manage account suspension and ban admin accounts": "Manage account suspension and ban admin accounts", "Manage Roles": "Manage Roles", "Manage Groups": "Toparlardı basqarıw", "Manage Other Admins": "<PERSON><PERSON><PERSON> admin<PERSON>di basqar<PERSON>w", "Allow users to reset passwords and update emails.": "Paydalanıwshılarǵa parolin tiklewge hám elektron pochtasın jańalawǵa ruqsat beriw.", "Delete Admins": "<PERSON><PERSON><PERSON>", "Preferences": "Preferences", "Settings": "Sazlawlar", "Background Color": "<PERSON><PERSON>", "Allow users to create new roles and edit or delete roles lower than their highest role.": "Allow users to create new roles and edit or delete roles lower than their highest role.", "Save Changes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "This log displays a list of successful sign in attempts by administrators. Records are kept for a total of :days days.": "This log displays a list of successful sign in attempts by administrators. Records are kept for a total of :days days.", "Access Log": "<PERSON><PERSON><PERSON>", "View a list of successful back-end user sign ins.": "View a list of successful back-end user sign ins.", "ID": "ID", "Date & Time": "<PERSON><PERSON><PERSON> h<PERSON> wa<PERSON>ıt", "Type": "<PERSON><PERSON><PERSON>", "IP address": "IP mánzil", "Username": "Paydalanıwshı atı", "Login": "<PERSON><PERSON><PERSON>", "First name": "Atı", "Last name": "Familiyası", "Email": "Elektron pochta", "A custom schema is used for this file format.": "A custom schema is used for this file format.", "Select": "Tań<PERSON>", "Replace": "Replace", "and": "h<PERSON>m", "Go": "<PERSON><PERSON><PERSON>", "Go to page": "<PERSON><PERSON>", "Copy to Clipboard": "Almasıw buferine k<PERSON> alıw", "Reveal Contents": "Reveal Contents", "Selected Site": "Tańlanǵan sayt", "Use Emmet abbreviations": "Use Emmet abbreviations", "A record does not exist for the selected site. Create one?": "A record does not exist for the selected site. Create one?", "The details you entered did not match our records. Please double-check and try again.": "The details you entered did not match our records. Please double-check and try again.", "Administration Area": "Administration Area", "Password Reset": "<PERSON><PERSON><PERSON>", "Please enter a new password.": "<PERSON><PERSON><PERSON>, ja<PERSON>a paroldi k<PERSON>.", "Password": "<PERSON><PERSON>", "Password Restore": "<PERSON><PERSON><PERSON>", "Please enter your login to restore the password.": "<PERSON><PERSON><PERSON>, paroldi tiklew ushın paydalanıwshı atın kirgiziń.", "A user could not be found with a login value of ':login'": "Paydalanıwshı ':login' tabılmadı", "If your account was found, a message has been sent to your email address with instructions.": "<PERSON>ger siziń akkauntı<PERSON>ız bar bolsa, elektron pochtańızǵa paroldi tiklew boyınsha kórsetpe jiberiledi.", "Invalid password reset data supplied. Please try again!": "Nadurıs parol. Qayta urınıp kóriń!", "Unable to reset your password!": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tiklew múmkin emes!", "Password has been reset. You may now sign in.": "<PERSON>rol tabı<PERSON>lı tiklendi. Endi siz kiriwińiz múmkin.", "Restore": "<PERSON><PERSON><PERSON>", "Reset": "<PERSON><PERSON><PERSON>", "Stay logged in": "Stay logged in", "Forgot password?": "Pa<PERSON>ińizdi umıttıńız ba?", "Global Editor Settings": "Redaktordıń global sazlawları", "Manage Widgets": "<PERSON><PERSON><PERSON><PERSON><PERSON> basqa<PERSON><PERSON>w", "Add Widget": "Vidjet qosıw", "Set the current layout as the default?": "Set the current layout as the default?", "Make Default": "Make Default", "Reset layout back to default?": "Reset layout back to default?", "Reset Layout": "Reset Layout", "Widget": "Vidjet", "please select": "<PERSON><PERSON><PERSON>, tańlań", "Width": "<PERSON><PERSON><PERSON>", "full width": "full width", "Create Widgets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "not equal to": "<PERSON><PERSON> emes", "Light Mode": "Jaq<PERSON>ı tema", "Dark Mode": "Qarańǵı tema", "OS Default": "OS Default", "Create a Custom Palette": "Create a Custom Palette", "Custom Palette": "Custom Palette", "Primary Color": "Tiykarǵı reń", "Secondary Color": "<PERSON><PERSON><PERSON>", "Selection Color": "Selection Color", "Nav Text": "Nav Text", "Nav Background": "Navigaciya fonı", "Sidebar Text": "Sidebar Text", "Sidebar Background": "Sidebar Background", "Sidebar Active Text": "Sidebar Active Text", "Sidebar Active Background": "Sidebar Active Background", "Sidebar Hover Background": "Sidebar Hover Background", "Settings Text": "Settings Text", "Settings Background": "Settings Background", "Settings Item Background": "Settings Item Background", "Settings Active Color": "Settings Active Color", "Settings Active Background": "Settings Active Background", "Settings Hover Background": "Settings Hover <PERSON>", "This name is shown in the title area of the back-end.": "This name is shown in the title area of the back-end.", "App Name": "Qosımsha ataması", "This name is shown on the sign in screen for the back-end.": "This name is shown on the sign in screen for the back-end.", "App Tagline": "App Tagline", "Brand": "Brand", "Upload a custom logo to use in the back-end.": "Upload a custom logo to use in the back-end.", "Logo": "Logotip", "Upload a custom favicon to use in the back-end": "Upload a custom favicon to use in the back-end", "Favicon": "Favicon", "Navigation Logo": "Navigaciya logotipi", "Replaces the dashboard link with a custom logo in the navigation.": "Replaces the dashboard link with a custom logo in the navigation.", "Dashboard Icon": "Dashboard Icon", "Use a custom image for the dashboard icon (60x60).": "Use a custom image for the dashboard icon (60x60).", "This message is shown on the sign in screen for the back-end.": "Bul xabar administrator paneline kiri<PERSON> <PERSON>.", "Welcome Message": "Welcome Message", "Login Page": "<PERSON><PERSON><PERSON> beti", "Background Type": "<PERSON><PERSON> tú<PERSON>", "Flat Color": "Flat Color", "Wallpaper": "Wallpaper", "Wallpaper Size": "Wallpaper Size", "Auto": "Avto", "Cover": "Cover", "Background Wallpaper Image": "Background Wallpaper Image", "Image": "<PERSON><PERSON><PERSON><PERSON>", "Random Autumn Images": "Random Autumn Images", "Custom Image": "Arnawlı súwret", "Login Page Image": "<PERSON><PERSON><PERSON> beti súwreti", "Colors": "<PERSON><PERSON><PERSON>", "Color Mode": "<PERSON><PERSON><PERSON> re<PERSON>", "Color Preset": "Color Preset", "Menu Style": "<PERSON><PERSON> stili", "Navigation": "Navigaciya", "Custom Stylesheet": "Qosımsha CSS stiller", "Styles": "Stiller", "Inline": "Inline", "Text": "Tekst", "Tiles": "Tiles", "Collapsed": "Qısqartılǵan", "Icons": "Icons", "Left Side": "Qap<PERSON><PERSON>", "Add New Item": "Jańa element qosıw", "Link Color": "<PERSON><PERSON><PERSON><PERSON>", "Current Password": "Ámeldegi parol", "New Password": "<PERSON><PERSON><PERSON><PERSON> parol", "Current password does not match. Please try again!": "Ámeldegi parol sáykes emes. <PERSON><PERSON><PERSON>, qayta urınıp kó<PERSON>!", "Your password has expired, please create a new one for security reasons.": "Your password has expired, please create a new one for security reasons.", "Region": "Aymaq", "Locale": "Til", "Select your desired locale for language use.": "Basqarıw paneliniń tilin tańlań.", "Timezone": "Timezone", "Adjust displayed dates to this timezone.": "Adjust displayed dates to this timezone.", "Code Editor": "Kod redaktorı", "Color Scheme": "Color Scheme", "Word Wrap": "Word Wrap", "Off": "<PERSON><PERSON><PERSON><PERSON>", "40 Characters": "40 Characters", "80 Characters": "80 Characters", "Fluid": "Fluid", "Font Size": "Shrift ólshemi", "Tab Size": "<PERSON><PERSON>", "Code Folding": "Kodtı qısqartıw", "Mark begin": "<PERSON><PERSON> basında", "Mark begin and end": "<PERSON><PERSON> basında hám sońında", "Autocompletion": "Avtomat tárizde toltırıw", "Basic Autocompletion (Ctrl + Space)": "Basic Autocompletion (Ctrl + Space)", "Live Autocompletion": "Live Autocompletion", "Show gutter": "Show gutter", "Highlight active line": "Highlight active line", "Automatically close tags": "Avtomat tárizde teglerdi jabıw", "Indent using tabs": "Indent using tabs", "Show indent guides": "Show indent guides", "Show invisible characters": "Show invisible characters", "Show print margin": "Show print margin", "Custom styles to include in the HTML editor.": "Custom styles to include in the HTML editor.", "Markup Styles": "<PERSON><PERSON>", "Paragraph Formats": "Jańa jol <PERSON>ı", "Markup Classes": "Markup Classes", "Label": "Label", "Paragraph": "Paragraph", "Class Name": "Klass ataması", "Link": "<PERSON><PERSON><PERSON><PERSON>", "Table": "<PERSON><PERSON>", "Table Cell": "Table Cell", "Allowed Tags": "Ruqsat berilgen tegler", "The list of allowed tags.": "Ruqsat berilgen tegler dizimi.", "Markup Tags": "Markup Tags", "Allowed Empty Tags": "<PERSON><PERSON><PERSON><PERSON> berilgen bos tegler", "The list of tags that are not removed when they have no content inside.": "The list of tags that are not removed when they have no content inside.", "Do Not Wrap Tags": "Do Not Wrap Tags", "The list of tags that should not be wrapped inside block tags.": "The list of tags that should not be wrapped inside block tags.", "Remove Tags": "<PERSON><PERSON><PERSON>", "The list of tags that are removed together with their content.": "The list of tags that are removed together with their content.", "Line Breaker Tags": "Line Breaker Tags", "The list of tags that are used to place a line breaker element between.": "The list of tags that are used to place a line breaker element between.", "Toolbar Buttons": "Ásbaplar panelindegi tú<PERSON>ler", "The Toolbar Buttons to be displayed in the Rich Editor by default.": "The Toolbar Buttons to be displayed in the Rich Editor by default.", "Insert a preset toolbar button configuration": "Insert a preset toolbar button configuration", "Allowed Attributes": "Allowed Attributes", "The list of allowed HTML attributes, in addition to the normal allowances.": "The list of allowed HTML attributes, in addition to the normal allowances.", "Enable code snippets (Tab)": "Enable code snippets (Tab)", "Title": "Ataması", "Description": "Táriyp", "Attachment": "Attachment", "Attachment URL": "Attachment URL", "Add a title and description for this attachment.": "Add a title and description for this attachment.", "Upload": "<PERSON><PERSON><PERSON><PERSON>", "Are you sure?": "Isenimińiz kámil me?", "Delete Selected": "Delete Selected", "Upload Error": "<PERSON><PERSON><PERSON><PERSON>", "Remove File": "Fayldı óshiriw", "Show Light Switch": "Show Light Switch", "Allow users to toggle the color mode from the navigation.": "Allow users to toggle the color mode from the navigation.", "Create :name": "Create :name", "Update :name": "Update :name", "Add :name": "Add :name", "Link :name": "Link :name", "Unlink": "Unlink", "Preview :name": "Preview :name", "Link a New :name": "Link a New :name", "Add a New :name": "Add a New :name", "Related :name Data": "Related :name Data", ":name Created": ":name Created", ":name Updated": ":name Updated", ":name Deleted": ":name Deleted", ":name Added": ":name Added", ":name Linked": ":name Linked", ":name Removed": ":name Removed", ":name Unlinked": ":name Unlinked", "Form record with an ID of :id could not be found.": "Form record with an ID of :id could not be found.", "Remove Selected": "Remove Selected", "Unlink Selected": "Unlink Selected", "Preview the Website": "Preview the Website", "Manage back-end administrator users, groups and permissions.": "Manage back-end administrator users, groups and permissions.", "Define permissions for administrators based on their role.": "Define permissions for administrators based on their role.", "Add administrators to groups used for notifications and features.": "Add administrators to groups used for notifications and features.", "Customize Backend": "Customize Backend", "Customize the administration area such as name, colors and logo.": "Customize the administration area such as name, colors and logo.", "Editor Settings": "Redaktor sazlawları", "Change the global editor preferences.": "Change the global editor preferences.", "My Account": "Meniń akkauntım", "Update your account details such as name, email address and password.": "Update your account details such as name, email address and password.", "Backend Preferences": "Basqarıw panelinıń sazlawları", "Manage your account preferences such as desired language.": "Manage your account preferences such as desired language.", "security login": "qáwipsiz kiriw", "List Setup": "List Setup", "Use checkboxes to select columns you want to see in the list. You can change position of columns by dragging them up or down.": "Use checkboxes to select columns you want to see in the list. You can change position of columns by dragging them up or down.", "Records Per Page": "Records Per Page", "Select the number of records per page to display. Please note that high number of records on a single page can reduce performance.": "Select the number of records per page to display. Please note that high number of records on a single page can reduce performance.", "Reset to Default": "Reset to De<PERSON>ult", "Add Selected": "Add Selected", "Create & Close": "Create & Close", "Creating :name...": "Creating :name...", "Save & Close": "Save & Close", "Saving :name...": "Saving :name...", "Delete this record?": "Delete this record?", "Deleting :name...": "Deleting :name...", "Loading...": "Loading...", "Complete": "Complete", "Please specify the modelClass property for :type": "Please specify the modelClass property for :type", "Please specify some columns to export.": "Please specify some columns to export.", "Missing column identifier": "Missing column identifier", "Unknown column": "Unknown column", "Please specify some columns to import.": "Please specify some columns to import.", "Please match some columns first.": "Please match some columns first.", "Please specify a match for the required field :label.": "Please specify a match for the required field :label.", "Source file encoding is not recognized. Please select the custom file format option with the proper encoding to import your file.": "Source file encoding is not recognized. Please select the custom file format option with the proper encoding to import your file.", "You must implement the controller behavior ListController with the export 'useList' option enabled.": "You must implement the controller behavior ListController with the export 'useList' option enabled.", "File export process completed!": "File export process completed!", "The browser will now redirect to the file download.": "The browser will now redirect to the file download.", "Ignore this column": "Ignore this column", "Drop column here...": "Drop column here...", "Please upload a valid CSV file.": "Please upload a valid CSV file.", "Created": "Created", "Updated": "Updated", "Skipped": "Skipped", "Warnings": "Warnings", "Errors": "Errors", "Skipped Rows": "Skipped Rows", "Show Ignored Columns": "Show Ignored Columns", "Auto Match Columns": "Auto Match Columns", "File not found": "File not found", "There was no data supplied to export": "There was no data supplied to export"}