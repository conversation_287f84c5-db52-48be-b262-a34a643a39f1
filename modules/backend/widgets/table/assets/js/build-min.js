!function(t){"use strict";void 0===t.oc&&(t.oc={}),void 0===t.oc.table&&(t.oc.table={});var e=function(e,o){if(this.el=e,this.$el=t(e),this.options=o,this.disposed=!1,this.dataSource=null,this.cellProcessors={},this.activeCellProcessor=null,this.activeCell=null,this.tableContainer=null,this.dataTableContainer=null,this.editedRowKey=null,this.dataTable=null,this.headerTable=null,this.toolbar=null,this.clickHandler=this.onClick.bind(this),this.keydownHandler=this.onKeydown.bind(this),this.documentClickHandler=this.onDocumentClick.bind(this),this.toolbarClickHandler=this.onToolbarClick.bind(this),this.options.postback&&"client"==this.options.clientDataSourceClass){if(!this.options.postbackHandlerName){var i=this.$el.closest("form").data("request");this.options.postbackHandlerName=i||"onSave"}this.formSubmitHandler=this.onFormSubmit.bind(this)}this.navigation=null,this.search=null,this.recordsAddedOrDeleted=0,this.disposeBound=this.dispose.bind(this),this.init(),t.oc.foundation.controlUtils.markDisposable(e)};e.prototype.init=function(){this.createDataSource(),this.initCellProcessors(),this.navigation=new t.oc.table.helper.navigation(this),this.search=new t.oc.table.helper.search(this),this.buildUi(),this.registerHandlers()},e.prototype.disposeCellProcessors=function(){for(var t=0,e=this.options.columns.length;t<e;t++){var o=this.options.columns[t].key;this.cellProcessors[o].dispose(),this.cellProcessors[o]=null}this.cellProcessors=null,this.activeCellProcessor=null},e.prototype.setColumnConfiguration=function(t,e){this.cellProcessors[t].columnConfiguration={...this.cellProcessors[t].columnConfiguration||{},...e}},e.prototype.createDataSource=function(){var e=this.options.clientDataSourceClass;if(void 0===t.oc.table.datasource||null==t.oc.table.datasource[e])throw new Error('The table client-side data source class "'+e+'" is not found in the $.oc.table.datasource namespace.');this.dataSource=new t.oc.table.datasource[e](this)},e.prototype.registerHandlers=function(){this.el.addEventListener("click",this.clickHandler),this.el.addEventListener("keydown",this.keydownHandler),this.$el.one("dispose-control",this.disposeBound),document.addEventListener("click",this.documentClickHandler),this.options.postback&&"client"==this.options.clientDataSourceClass&&this.$el.closest("form").bind("oc.beforeRequest",this.formSubmitHandler);var t=this.getToolbar();t&&t.addEventListener("click",this.toolbarClickHandler)},e.prototype.unregisterHandlers=function(){this.el.removeEventListener("click",this.clickHandler),document.removeEventListener("click",this.documentClickHandler),this.clickHandler=null,this.el.removeEventListener("keydown",this.keydownHandler),this.keydownHandler=null;var t=this.getToolbar();t&&t.removeEventListener("click",this.toolbarClickHandler),this.toolbarClickHandler=null,this.formSubmitHandler&&(this.$el.closest("form").unbind("oc.beforeRequest",this.formSubmitHandler),this.formSubmitHandler=null)},e.prototype.initCellProcessors=function(){for(var e=0,o=this.options.columns.length;e<o;e++){var i=this.options.columns[e],a=i.key,n=i.type;if(void 0===n&&(n="string",this.options.columns[e].type=n),void 0===t.oc.table.processor||null==t.oc.table.processor[n])throw new Error('The table cell processor for the column type "'+n+'" is not found in the $.oc.table.processor namespace.');this.cellProcessors[a]=new t.oc.table.processor[n](this,a,i)}},e.prototype.getCellProcessor=function(t){return this.cellProcessors[t]},e.prototype.buildUi=function(){var t=this.el.querySelector("div.table-container");t&&t.remove(),this.tableContainer=document.createElement("div"),this.tableContainer.setAttribute("class","table-container"),this.options.toolbar&&this.buildToolbar(),this.tableContainer.appendChild(this.buildHeaderTable()),this.el.insertBefore(this.tableContainer,this.el.children[0]),this.options.height?this.dataTableContainer=this.buildScrollbar():this.dataTableContainer=this.tableContainer,this.updateDataTable()},e.prototype.buildToolbar=function(){(this.options.adding||this.options.deleting)&&(this.toolbar=t(t("[data-table-toolbar]",this.el).html()).appendTo(this.tableContainer).get(0),this.options.adding?this.navigation.paginationEnabled()||!this.options.rowSorting?t("[data-cmd=record-add-below], [data-cmd=record-add-above]",this.toolbar).remove():t("[data-cmd=record-add]",this.toolbar).remove():t('[data-cmd^="record-add"]',this.toolbar).remove(),this.options.deleting||t('[data-cmd="record-delete"]',this.toolbar).remove())},e.prototype.buildScrollbar=function(){var e=document.createElement("div"),o=document.createElement("div");return e.setAttribute("class","control-scrollbar"),this.options.dynamicHeight?e.setAttribute("style","max-height: "+this.options.height+"px"):e.setAttribute("style","height: "+this.options.height+"px"),e.appendChild(o),this.tableContainer.appendChild(e),t(e).scrollbar({animation:!1}),o},e.prototype.buildHeaderTable=function(){var t=document.createElement("table"),e=document.createElement("tr");t.className="headers",t.appendChild(e);for(var o=0,i=this.options.columns.length;o<i;o++){var a=document.createElement("th");this.options.columns[o].width&&a.setAttribute("style","width: "+this.options.columns[o].width),void 0!==a.textContent?a.textContent=this.options.columns[o].title:a.innerText=this.options.columns[o].title,e.appendChild(a)}return this.headerTable=t,t},e.prototype.updateDataTable=function(t){var e=this;this.unfocusTable(),this.fetchRecords((function(o,i){e.buildDataTable(o,i),t&&t(),0==i&&e.options.adding&&e.addRecord("above",!0),e.$el.trigger("oc.tableUpdateData",[o,i]),e=null}))},e.prototype.updateColumnWidth=function(){for(var t=this.headerTable.querySelectorAll("th"),e=this.dataTable.querySelectorAll("tr:first-child td"),o=0,i=t.length;o<i;o++)e[o]&&e[o].setAttribute("style",t[o].getAttribute("style"))},e.prototype.buildDataTable=function(t,e){var o=document.createElement("table"),i=document.createElement("tbody"),a=this.options.keyColumn;o.setAttribute("class","data");for(var n=0,r=t.length;n<r;n++){var s=document.createElement("tr");if(void 0===t[n][a])throw new Error("The row attribute "+a+" is not set for the row #"+n);s.setAttribute("data-row",t[n][a]);for(var l=0,c=this.options.columns.length;l<c;l++){var h=document.createElement("td"),d=document.createElement("input"),u=document.createElement("div"),p=this.options.columns[l],b=p.key,f=this.getCellProcessor(b);h.setAttribute("data-column",b),h.setAttribute("data-column-type",p.type),d.setAttribute("type","hidden"),d.setAttribute("data-container","data-container"),d.value=this.formatDataContainerValue(t[n][b]),u.setAttribute("class","content-container"),h.appendChild(u),s.appendChild(h),h.appendChild(d),f.renderCell(t[n][b],u)}i.appendChild(s)}o.appendChild(i),null!==this.dataTable?this.dataTableContainer.replaceChild(o,this.dataTable):this.dataTableContainer.appendChild(o),this.dataTable=o,this.updateColumnWidth(),this.updateScrollbar(),this.navigation.buildPagination(e),this.search.buildSearchForm()},e.prototype.formatDataContainerValue=function(t){return void 0===t?"":"boolean"==typeof t?t?1:"":t},e.prototype.fetchRecords=function(t){this.search.hasQuery()?this.dataSource.searchRecords(this.search.getQuery(),this.navigation.getPageFirstRowOffset(),this.options.recordsPerPage,t):this.dataSource.getRecords(this.navigation.getPageFirstRowOffset(),this.options.recordsPerPage,t)},e.prototype.updateScrollbar=function(){this.options.height&&t(this.dataTableContainer.parentNode).data("oc.scrollbar").update()},e.prototype.scrollCellIntoView=function(){this.options.height&&this.activeCell&&t(this.dataTableContainer.parentNode).data("oc.scrollbar").gotoElement(this.activeCell)},e.prototype.disposeScrollbar=function(){this.options.height&&(t(this.dataTableContainer.parentNode).data("oc.scrollbar").dispose(),t(this.dataTableContainer.parentNode).data("oc.scrollbar",null))},e.prototype.setActiveProcessor=function(t){this.activeCellProcessor&&this.activeCellProcessor.onUnfocus(),this.activeCellProcessor=t},e.prototype.commitEditedRow=function(){if(null!==this.editedRowKey){var t=this.dataTable.querySelector('tr[data-row="'+CSS.escape(this.editedRowKey)+'"]');if(t&&1==t.getAttribute("data-dirty")){for(var e=t.children,o={},i=0,a=e.length;i<a;i++){var n=e[i];o[n.getAttribute("data-column")]=this.getCellValue(n)}this.dataSource.updateRecord(this.editedRowKey,o),t.setAttribute("data-dirty",0)}}},e.prototype.unfocusTable=function(){this.elementRemoveClass(this.el,"active"),this.activeCellProcessor&&this.activeCellProcessor.onUnfocus(),this.commitEditedRow(),this.activeCellProcessor=null,this.activeCell&&this.activeCell.setAttribute("class",""),this.activeCell=null},e.prototype.focusTable=function(){this.elementAddClass(this.el,"active")},e.prototype.focusCell=function(t,e){var o=t.getAttribute("data-column");if(null!==o){this.focusTable();var i=this.getCellProcessor(o);if(!i)throw new Error("Cell processor not found for the column "+o);this.activeCell!==t&&(this.activeCell&&this.elementRemoveClass(this.activeCell,"active"),this.setActiveProcessor(i),this.activeCell=t,i.isCellFocusable()&&this.elementAddClass(this.activeCell,"active"));var a=this.getCellRowKey(t);null!==this.editedRowKey&&a!=this.editedRowKey&&this.commitEditedRow(),this.editedRowKey=a,i.onFocus(t,e),this.scrollCellIntoView()}},e.prototype.markCellRowDirty=function(t){t.parentNode.setAttribute("data-dirty",1)},e.prototype.addRecord=function(t,e){this.activeCell&&!this.navigation.paginationEnabled()&&this.options.rowSorting||(t="bottom");var o=null,i=null;if("above"!=t&&"below"!=t||(o=this.getCellRowKey(this.activeCell),i=this.getCellRowIndex(this.activeCell)),this.unfocusTable(),this.navigation.paginationEnabled()){var a=this.navigation.getNewRowPage(t,i);if(a!=this.navigation.pageIndex&&!this.validate())return;this.navigation.pageIndex=a}this.recordsAddedOrDeleted++;var n=this.options.keyColumn,r={},s=this;r[n]=-1*this.recordsAddedOrDeleted,this.$el.trigger("oc.tableNewRow",[r]),this.dataSource.createRecord(r,t,o,this.navigation.getPageFirstRowOffset(),this.options.recordsPerPage,(function(t,o){s.buildDataTable(t,o);var i=s.findRowByKey(r[n]);if(!i)throw new Error("New row is not found in the updated table: "+r[n]);e||s.navigation.focusCell(i,0),s=null}))},e.prototype.deleteRecord=function(){if(this.activeCell){var t=this,e=this.getCellRowKey(this.activeCell),o=this.getCellRowIndex(this.activeCell),i=this.navigation.paginationEnabled(),a=this.navigation.pageIndex,n=this.activeCell.cellIndex;i&&(this.navigation.pageIndex=this.navigation.getPageAfterDeletion(o)),this.recordsAddedOrDeleted++;var r={};r[this.options.keyColumn]=-1*this.recordsAddedOrDeleted,this.dataSource.deleteRecord(e,r,this.navigation.getPageFirstRowOffset(),this.options.recordsPerPage,(function(e,r){t.buildDataTable(e,r),i&&a!=t.navigation.pageIndex?t.navigation.focusCell("bottom",n):t.navigation.focusCellInReplacedRow(o,n),t=null}))}},e.prototype.notifyRowProcessorsOnChange=function(t){for(var e=t.getAttribute("data-column"),o=t.parentNode,i=0,a=o.children.length;i<a;i++){var n=this.options.columns[i].key;this.cellProcessors[n].onRowValueChanged(e,o.children[i])}},e.prototype.getToolbar=function(){return this.tableContainer.querySelector("div.toolbar")},e.prototype.validate=function(){for(var e=this.dataTable.querySelectorAll("tbody tr[data-row]"),o=0,i=e.length;o<i;o++){var a=e[o];this.elementRemoveClass(a,"error")}o=0;for(var n=e.length;o<n;o++){a=e[o];for(var r=this.getRowData(a),s=0,l=a.children.length;s<l;s++)this.elementRemoveClass(a.children[s],"error");for(var c in r){var h=this.getCellProcessor(c),d=h.validate(r[c],r);if(void 0!==d){var u=a.querySelector('td[data-column="'+c+'"]'),p=this;return this.elementAddClass(a,"error"),this.elementAddClass(u,"error"),t.oc.flashMsg({text:d,class:"error"}),window.setTimeout((function(){p.focusCell(u,!1),u=null,p=null,h=null}),100),!1}}}return!0},e.prototype.onClick=function(t){if(this.focusTable(),!1!==this.navigation.onClick(t)&&!1!==this.search.onClick(t)){for(var e=0,o=this.options.columns.length;e<o;e++){var i=this.options.columns[e].key;this.cellProcessors[i].onClick(t)}var a=this.getEventTarget(t,"TD");a&&"TD"==a.tagName?this.focusCell(a,!0):this.unfocusTable()}},e.prototype.onKeydown=function(t){if(("a"===t.key||"A"===t.key)&&t.altKey&&this.options.adding)return t.shiftKey?this.addRecord("above"):this.addRecord("below"),void this.stopEvent(t);if(("d"===t.key||"D"===t.key)&&t.altKey&&this.options.deleting)return this.deleteRecord(),void this.stopEvent(t);for(var e=0,o=this.options.columns.length;e<o;e++){var i=this.options.columns[e].key;if(!1===this.cellProcessors[i].onKeyDown(t))return}!1!==this.navigation.onKeydown(t)&&this.search.onKeydown(t)},e.prototype.onFormSubmit=function(t,e){var o=e.handler===this.options.postbackHandlerName;if(!o||(this.unfocusTable(),this.validate())){if(o||this.options.postbackHandlerWild){var i=this.options.fieldName.indexOf("[")>-1?this.options.fieldName+"[TableData]":this.options.fieldName+"TableData";e.options.data[i]=JSON.stringify(this.dataSource.getAllData())}}else t.preventDefault()},e.prototype.onToolbarClick=function(t){var e=this.getEventTarget(t,"BUTTON"),o=e&&e.getAttribute("data-cmd");if(o){switch(o){case"record-add":case"record-add-below":this.addRecord("below");break;case"record-add-above":this.addRecord("above");break;case"record-delete":this.deleteRecord()}this.stopEvent(t)}},e.prototype.onDocumentClick=function(t){var e=this.getEventTarget(t);this.parentContainsElement(this.el,e)||this.activeCellProcessor&&this.activeCellProcessor.elementBelongsToProcessor(e)||this.unfocusTable()},e.prototype.dispose=function(){this.disposed||(this.disposed=!0,this.disposeBound=!0,this.unfocusTable(),this.dataSource.dispose(),this.dataSource=null,this.unregisterHandlers(),this.dataTable=null,this.headerTable=null,this.toolbar=null,this.disposeCellProcessors(),this.navigation.dispose(),this.navigation=null,this.disposeScrollbar(),this.el=null,this.tableContainer=null,this.$el=null,this.dataTableContainer=null,this.activeCell=null)},e.prototype.setRowValues=function(t,e){var o=this.findRowByIndex(t);if(!o)return!1;for(var i=!1,a=0,n=o.children.length;a<n;a++){var r=o.children[a],s=this.getCellColumnName(r);for(var l in e)l==s&&(this.setCellValue(r,e[l],!0),i=!0)}if(i){var c=this.editedRowKey;this.editedRowKey=this.getRowKey(o),this.commitEditedRow(),this.editedRowKey=c}return!0},e.prototype.getElement=function(){return this.el},e.prototype.getAlias=function(){return this.options.alias},e.prototype.getTableContainer=function(){return this.tableContainer},e.prototype.getDataTableBody=function(){return this.dataTable.children[0]},e.prototype.getEventTarget=function(t,e){var o=t.target?t.target:t.srcElement;if(void 0===e)return o;for(var i=o.tagName;i!=e;){if(!(o=o.parentNode))return null;i=o.tagName}return o},e.prototype.stopEvent=function(t){t.stopPropagation?t.stopPropagation():t.cancelBubble=!0,t.preventDefault?t.preventDefault():t.returnValue=!1},e.prototype.elementHasClass=function(t,e){return t.classList?t.classList.contains(e):new RegExp("(^| )"+e+"( |$)","gi").test(t.className)},e.prototype.elementAddClass=function(t,e){this.elementHasClass(t,e)||(t.classList?t.classList.add(e):t.className+=" "+e)},e.prototype.elementRemoveClass=function(t,e){t.classList?t.classList.remove(e):t.className=t.className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ")},e.prototype.parentContainsElement=function(t,e){for(;e&&e!=t;)e=e.parentNode;return!!e},e.prototype.getCellValue=function(t){return t.querySelector("[data-container]").value},e.prototype.getCellRowKey=function(t){return t.parentNode.getAttribute("data-row")},e.prototype.getRowKey=function(t){return t.getAttribute("data-row")},e.prototype.findRowByKey=function(t){return this.dataTable.querySelector('tbody tr[data-row="'+t+'"]')},e.prototype.findRowByIndex=function(t){return this.getDataTableBody().children[t]},e.prototype.getCellRowIndex=function(t){return parseInt(t.parentNode.rowIndex)},e.prototype.getRowCellValueByColumnName=function(t,e){var o=t.querySelector('td[data-column="'+e+'"]');return o?this.getCellValue(o):o},e.prototype.getRowData=function(t){for(var e={},o=0,i=t.children.length;o<i;o++){var a=t.children[o];e[a.getAttribute("data-column")]=this.getCellValue(a)}return e},e.prototype.getCellColumnName=function(t){return t.getAttribute("data-column")},e.prototype.setCellValue=function(t,e,o){var i=t.querySelector("[data-container]");i.value!=e&&(i.value=e,this.markCellRowDirty(t),this.notifyRowProcessorsOnChange(t),void 0!==o&&o||this.$el.trigger("oc.tableCellChanged",[this.getCellColumnName(t),e,this.getCellRowIndex(t)]))},e.DEFAULTS={clientDataSourceClass:"client",keyColumn:"id",recordsPerPage:!1,data:null,postback:!0,postbackHandlerName:null,postbackHandlerWild:!1,adding:!0,deleting:!0,toolbar:!0,searching:!1,rowSorting:!1,height:!1,dynamicHeight:!1};var o=t.fn.table;t.fn.table=function(o){var i=Array.prototype.slice.call(arguments,1),a=void 0;return this.each((function(){var n=t(this),r=n.data("oc.table"),s=t.extend({},e.DEFAULTS,n.data(),"object"==typeof o&&o);if(r||n.data("oc.table",r=new e(this,s)),"string"==typeof o&&(a=r[o].apply(r,i)),void 0!==a)return!1})),a||this},t.fn.table.Constructor=e,t.oc.table.table=e,t.fn.table.noConflict=function(){return t.fn.table=o,this},t(document).on("render",(function(){t("div[data-control=table]").table()}))}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");void 0===t.oc.table.helper&&(t.oc.table.helper={});var e=function(t){this.tableObj=t,this.pageIndex=0,this.pageCount=0,this.init()};e.prototype.init=function(){},e.prototype.dispose=function(){this.tableObj=null},e.prototype.paginationEnabled=function(){return null!=this.tableObj.options.recordsPerPage&&0!=this.tableObj.options.recordsPerPage},e.prototype.getPageFirstRowOffset=function(){return this.pageIndex*this.tableObj.options.recordsPerPage},e.prototype.buildPagination=function(t){if(this.paginationEnabled()){var e=this.tableObj.getElement().querySelector(".pagination"),o=!1,i=0;if(this.pageCount=this.calculatePageCount(t,this.tableObj.options.recordsPerPage),e?i=this.getRecordCount(e):((e=document.createElement("div")).setAttribute("class","pagination"),o=!0),o||i!=t){e.setAttribute("data-record-count",t);var a=this.buildPaginationLinkList(t,this.tableObj.options.recordsPerPage,this.pageIndex);o?(e.appendChild(a),this.tableObj.getElement().appendChild(e)):e.replaceChild(a,e.children[0])}else this.markActiveLinkItem(e,this.pageIndex)}},e.prototype.calculatePageCount=function(t,e){var o=Math.ceil(t/e);return o||(o=1),o},e.prototype.getRecordCount=function(t){var e=t||this.tableObj.getElement().querySelector(".pagination");return parseInt(e.getAttribute("data-record-count"))},e.prototype.buildPaginationLinkList=function(e,o,i){for(var a=this.calculatePageCount(e,o),n=document.createElement("ul"),r=0;r<a;r++){var s=document.createElement("li"),l=document.createElement("a");r==i&&s.setAttribute("class","active"),l.innerText=r+1,l.setAttribute("data-page-index",r),l.setAttribute("href","#"),s.appendChild(l),n.appendChild(s),t(l).addClass("pagination-link")}return n},e.prototype.markActiveLinkItem=function(t,e){var o=t.querySelector(".active"),i=t.children[0];o.setAttribute("class","");for(var a=0,n=i.children.length;a<n;a++)a==e&&i.children[a].setAttribute("class","active")},e.prototype.gotoPage=function(t,e){this.tableObj.unfocusTable(),this.tableObj.validate()&&(this.pageIndex=t,this.tableObj.updateDataTable(e))},e.prototype.getRowCountOnPage=function(t){return this.tableObj.getDataTableBody().children.length},e.prototype.getNewRowPage=function(t,e){var o=this.getRecordCount();return"bottom"===t?this.calculatePageCount(o+1,this.tableObj.options.recordsPerPage)-1:"above"==t?this.pageIndex:"below"==t&&e==this.tableObj.options.recordsPerPage-1?this.pageIndex+1:this.pageIndex},e.prototype.getPageAfterDeletion=function(t){return 0==t&&1==this.getRowCountOnPage()?0==this.pageIndex?0:this.pageIndex-1:this.pageIndex},e.prototype.navigateDown=function(t,e){if(this.tableObj.activeCell&&(!this.tableObj.activeCellProcessor||this.tableObj.activeCellProcessor.keyNavigationAllowed(t,"down"))){var o=this.tableObj.activeCell.parentNode,i=t.shiftKey?o.parentNode.children[o.parentNode.children.length-1]:o.nextElementSibling,a=void 0!==e?e:this.tableObj.activeCell.cellIndex;if(i){var n=i.children[a];n&&this.tableObj.focusCell(n)}else{if(!this.paginationEnabled())return;if(this.pageIndex<this.pageCount-1){var r=this;this.gotoPage(this.pageIndex+1,(function(){r.focusCell("top",a),r=null}))}}}},e.prototype.navigateUp=function(t,e,o){if(this.tableObj.activeCell&&(!this.tableObj.activeCellProcessor||this.tableObj.activeCellProcessor.keyNavigationAllowed(t,"up"))){var i=this.tableObj.activeCell.parentNode,a=!t.shiftKey||o?i.previousElementSibling:i.parentNode.children[0],n=void 0!==e?e:this.tableObj.activeCell.cellIndex;if(a){var r=a.children[n];r&&this.tableObj.focusCell(r)}else{if(!this.paginationEnabled())return;if(this.pageIndex>0){var s=this;this.gotoPage(this.pageIndex-1,(function(){s.focusCell("bottom",n),s=null}))}}}},e.prototype.navigateLeft=function(t,e){if(this.tableObj.activeCell&&(e||!this.tableObj.activeCellProcessor||this.tableObj.activeCellProcessor.keyNavigationAllowed(t,"left"))){var o=this.tableObj.activeCell.parentNode,i=!t.shiftKey||e?this.tableObj.activeCell.cellIndex-1:0,a=o.children[i];a?this.tableObj.focusCell(a):this.navigateUp(t,o.children.length-1,e)}},e.prototype.navigateRight=function(t,e){if(this.tableObj.activeCell&&(e||!this.tableObj.activeCellProcessor||this.tableObj.activeCellProcessor.keyNavigationAllowed(t,"right"))){var o=this.tableObj.activeCell.parentNode,i=t.shiftKey?o.children.length-1:this.tableObj.activeCell.cellIndex+1,a=o.children[i];a?this.tableObj.focusCell(a):this.navigateDown(t,0)}},e.prototype.navigateNext=function(t){this.tableObj.activeCell&&(this.tableObj.activeCellProcessor&&!this.tableObj.activeCellProcessor.keyNavigationAllowed(t,"tab")||(t.shiftKey?this.navigateLeft(t,!0):this.navigateRight(t,!0),this.tableObj.stopEvent(t)))},e.prototype.focusCell=function(t,e){var o=null,i=this.tableObj.getDataTableBody();if("object"==typeof t?o=t:"bottom"==t?o=i.children[i.children.length-1]:"top"==t&&(o=i.children[0]),o){var a=o.children[e];a&&this.tableObj.focusCell(a)}},e.prototype.focusCellInReplacedRow=function(t,e){if(0==t)this.focusCell("top",e);else{var o=this.tableObj.findRowByIndex(t);o||(o=this.tableObj.findRowByIndex(t-1)),o?this.focusCell(o,e):this.focusCell("top",e)}},e.prototype.onKeydown=function(t){return"ArrowDown"===t.key?this.navigateDown(t):"ArrowUp"===t.key?this.navigateUp(t):"ArrowLeft"===t.key?this.navigateLeft(t):"ArrowRight"===t.key?this.navigateRight(t):"Tab"===t.key?this.navigateNext(t):void 0},e.prototype.onClick=function(e){var o=this.tableObj.getEventTarget(e,"A");if(o&&t(o).hasClass("pagination-link")){var i=parseInt(o.getAttribute("data-page-index"));if(null!==i)return this.gotoPage(i),this.tableObj.stopEvent(e),!1}},t.oc.table.helper.navigation=e}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");void 0===t.oc.table.helper&&(t.oc.table.helper={});var e=function(t){this.tableObj=t,this.searchForm=null,this.searchInput=null,this.inputTrackTimer=null,this.activeQuery=null,this.isActive=!1,this.init()};e.prototype.init=function(){},e.prototype.dispose=function(){this.tableObj=null,this.searchForm=null,this.searchInput=null},e.prototype.buildSearchForm=function(){if(this.searchEnabled()){var e=this.tableObj.getElement(),o=this.tableObj.getToolbar();o.querySelector(".table-search")||(this.searchForm=t(t("[data-table-toolbar-search]",e).html()).appendTo(o).get(0),this.searchInput=t(".table-search-input",this.searchForm).get(0))}},e.prototype.getQuery=function(){return t.trim(this.activeQuery)},e.prototype.hasQuery=function(){return this.searchEnabled()&&t.trim(this.activeQuery).length>0},e.prototype.searchEnabled=function(){return this.tableObj.options.searching},e.prototype.performSearch=function(t,e){var o=this.activeQuery!=t;this.activeQuery=t,o&&this.tableObj.updateDataTable(e)},e.prototype.onKeydown=function(t){if("Tab"!==t.key){if(this.isActive){var e=this;this.inputTrackTimer=window.setTimeout((function(){e.performSearch(e.searchInput.value)}),300)}}else this.onClick(t)},e.prototype.onClick=function(e){var o=this.tableObj.getEventTarget(e,"INPUT");this.isActive=o&&t(o).hasClass("table-search-input")},t.oc.table.helper.search=e}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");void 0===t.oc.table.datasource&&(t.oc.table.datasource={});var e=function(t){this.tableObj=t};e.prototype.dispose=function(){this.tableObj=null},e.prototype.getRecords=function(t,e,o){o([])},e.prototype.searchRecords=function(t,e,o,i){i([])},e.prototype.createRecord=function(t,e,o,i,a,n){n([],0)},e.prototype.updateRecord=function(t,e){},e.prototype.deleteRecord=function(t,e,o,i,a){a([],0)},t.oc.table.datasource.base=e}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.datasource)throw new Error("The $.oc.table.datasource namespace is not defined. Make sure that the table.datasource.base.js script is loaded.");var e=t.oc.table.datasource.base,o=e.prototype,i=function(t){e.call(this,t);var o=t.getElement().getAttribute("data-data");if(null==o)throw new Error("The required data-data attribute is not found on the table control element.");this.data=JSON.parse(o)};(i.prototype=Object.create(o)).constructor=i,i.prototype.dispose=function(){o.dispose.call(this),this.data=null},i.prototype.getRecords=function(t,e,o){o(e?this.data.slice(t,t+e):this.data,this.data.length)},i.prototype.createRecord=function(t,e,o,i,a,n){if("bottom"===e)this.data.push(t);else if("above"==e||"below"==e){var r=this.getIndexOfKey(o);"below"==e&&r++,this.data.splice(r,0,t)}this.getRecords(i,a,n)},i.prototype.updateRecord=function(t,e){var o=this.getIndexOfKey(t);if(-1===o)throw new Error("Record with they key "+t+" is not found in the data set");e[this.tableObj.options.keyColumn]=t,this.data[o]=e},i.prototype.deleteRecord=function(t,e,o,i,a){var n=this.getIndexOfKey(t);if(-1===n)throw new Error("Record with they key "+t+" is not found in the data set");this.data.splice(n,1),0==this.data.length&&this.data.push(e),this.getRecords(o,i,a)},i.prototype.getIndexOfKey=function(t){var e=this.tableObj.options.keyColumn;return this.data.map((function(t){return t[e]+""})).indexOf(t+"")},i.prototype.getAllData=function(){return this.data},t.oc.table.datasource.client=i}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.datasource)throw new Error("The $.oc.table.datasource namespace is not defined. Make sure that the table.datasource.base.js script is loaded.");var e=t.oc.table.datasource.base,o=e.prototype,i=function(t){e.call(this,t);var o=t.getElement().getAttribute("data-data");if(null==o)throw new Error("The required data-data attribute is not found on the table control element.");this.data=JSON.parse(o)};(i.prototype=Object.create(o)).constructor=i,i.prototype.dispose=function(){o.dispose.call(this),this.data=null},i.prototype.getRecords=function(t,e,o){var i=this.tableObj.getAlias()+"::onServerGetRecords";this.tableObj.$el.request(i,{data:{offset:t,count:e}}).done((function(t){o(t.records,t.count)}))},i.prototype.searchRecords=function(t,e,o,i){var a=this.tableObj.getAlias()+"::onServerSearchRecords";this.tableObj.$el.request(a,{data:{query:t,offset:e,count:o}}).done((function(t){i(t.records,t.count)}))},i.prototype.createRecord=function(t,e,o,i,a,n){var r=this.tableObj.getAlias()+"::onServerCreateRecord";this.tableObj.$el.request(r,{data:{recordData:t,placement:e,relativeToKey:o,offset:i,count:a}}).done((function(t){n(t.records,t.count)}))},i.prototype.updateRecord=function(t,e){var o=this.tableObj.getAlias()+"::onServerUpdateRecord";this.tableObj.$el.request(o,{data:{key:t,recordData:e}})},i.prototype.deleteRecord=function(t,e,o,i,a){var n=this.tableObj.getAlias()+"::onServerDeleteRecord";this.tableObj.$el.request(n,{data:{key:t,offset:o,count:i}}).done((function(t){a(t.records,t.count)}))},t.oc.table.datasource.server=i}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");void 0===t.oc.table.processor&&(t.oc.table.processor={});var e=function(t,e,o){this.tableObj=t,this.columnName=e,this.columnConfiguration=o,this.activeCell=null,this.validators=[],this.registerHandlers(),this.initValidators()};e.prototype.dispose=function(){this.unregisterHandlers(),this.tableObj=null,this.activeCell=null},e.prototype.renderCell=function(t,e){},e.prototype.registerHandlers=function(){},e.prototype.unregisterHandlers=function(){},e.prototype.onFocus=function(t,e){},e.prototype.onUnfocus=function(){},e.prototype.onKeyDown=function(t){},e.prototype.onClick=function(t){},e.prototype.onRowValueChanged=function(t,e){},e.prototype.keyNavigationAllowed=function(t,e){return!0},e.prototype.isCellFocusable=function(){return!0},e.prototype.getCellContentContainer=function(t){return t.querySelector(".content-container")},e.prototype.createViewContainer=function(t,e){var o=document.createElement("div");return o.setAttribute("data-view-container","data-view-container"),o.textContent=void 0===e?"":e,t.appendChild(o),o},e.prototype.getViewContainer=function(t){return t.querySelector("[data-view-container]")},e.prototype.showViewContainer=function(t){return this.getViewContainer(t).setAttribute("class","")},e.prototype.hideViewContainer=function(t){return this.getViewContainer(t).setAttribute("class","oc-hide")},e.prototype.setViewContainerValue=function(t,e){return this.getViewContainer(t).textContent=e},e.prototype.elementBelongsToProcessor=function(t){return!1},e.prototype.initValidators=function(){if(void 0!==this.columnConfiguration.validation)for(var e in this.columnConfiguration.validation){if(void 0===t.oc.table.validator||null==t.oc.table.validator[e])throw new Error('The table cell validator "'+e+'" for the column "'+this.columnName+'" is not found in the $.oc.table.validator namespace.');var o=new t.oc.table.validator[e](this.columnConfiguration.validation[e]);this.validators.push(o)}},e.prototype.validate=function(t,e){for(var o=0,i=this.validators.length;o<i;o++){var a=this.validators[o].validate(t,e);if(void 0!==a)return a}},t.oc.table.processor.base=e}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.processor)throw new Error("The $.oc.table.processor namespace is not defined. Make sure that the table.processor.base.js script is loaded.");var e=t.oc.table.processor.base,o=e.prototype,i=function(t,o,i){this.focusTimeoutHandler=this.onFocusTimeout.bind(this),e.call(this,t,o,i)};(i.prototype=Object.create(o)).constructor=i,i.prototype.dispose=function(){o.dispose.call(this),this.focusTimeoutHandler=null},i.prototype.renderCell=function(t,e){this.createViewContainer(e,t)},i.prototype.onFocus=function(t,e){this.activeCell!==t&&(this.activeCell=t,this.buildEditor(t,this.getCellContentContainer(t)))},i.prototype.onUnfocus=function(){if(this.activeCell){var t=this.activeCell.querySelector(".string-input");t&&(this.tableObj.setCellValue(this.activeCell,t.value),this.setViewContainerValue(this.activeCell,t.value),t.parentNode.removeChild(t)),this.showViewContainer(this.activeCell),this.activeCell=null}},i.prototype.buildEditor=function(t,e){this.hideViewContainer(this.activeCell);var o=document.createElement("input");o.setAttribute("type","text"),o.setAttribute("class","string-input"),o.value=this.tableObj.getCellValue(t),this.columnConfiguration.readOnly&&o.setAttribute("readonly",!0),e.appendChild(o),this.setCaretPosition(o,0),window.setTimeout(this.focusTimeoutHandler,0)},i.prototype.keyNavigationAllowed=function(t,e){if("left"!=e&&"right"!=e)return!0;if(!this.activeCell)return!0;var o=this.activeCell.querySelector(".string-input");if(!o)return!0;var i=this.getCaretPosition(o);return"left"==e?0==i:"right"!=e||i==o.value.length},i.prototype.onRowValueChanged=function(t,e){if(t==this.columnName){var o=this.tableObj.getCellValue(e);this.setViewContainerValue(e,o)}},i.prototype.onFocusTimeout=function(){if(this.activeCell){var t=this.activeCell.querySelector(".string-input");t&&(t.focus(),this.setCaretPosition(t,0))}},i.prototype.getCaretPosition=function(t){if(document.selection){var e=document.selection.createRange();return e.moveStart("character",-t.value.length),e.text.length}return void 0!==t.selectionStart?t.selectionStart:0},i.prototype.setCaretPosition=function(t,e){if(document.selection){var o=t.createTextRange();setTimeout((function(){o.collapse(!0),o.moveStart("character",e),o.moveEnd("character",0),o.select()}),0)}return void 0!==t.selectionStart&&setTimeout((function(){t.selectionStart=e,t.selectionEnd=e}),0),0},t.oc.table.processor.string=i}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.processor)throw new Error("The $.oc.table.processor namespace is not defined. Make sure that the table.processor.base.js script is loaded.");var e=t.oc.table.processor.base,o=e.prototype,i=function(t,o,i){e.call(this,t,o,i)};(i.prototype=Object.create(o)).constructor=i,i.prototype.dispose=function(){o.dispose.call(this)},i.prototype.isCellFocusable=function(){return!1},i.prototype.renderCell=function(t,e){var o=document.createElement("input");o.setAttribute("data-checkbox-element","true"),o.setAttribute("tabindex","0"),o.setAttribute("type","checkbox"),o.setAttribute("class","form-check-input"),t&&0!=t&&"false"!=t&&(o.checked=!0),e.appendChild(o)},i.prototype.onFocus=function(t,e){t.querySelector("input[data-checkbox-element]").focus()},i.prototype.onClick=function(e){var o=e.target;if(o.getAttribute("data-checkbox-element")){if(this.getCheckboxContainerNode(o).getAttribute("data-column")!==this.columnName)return;this.changeState(o),t(o).trigger("change")}},i.prototype.changeState=function(t){var e=this.getCheckboxContainerNode(t);this.tableObj.setCellValue(e,t.checked?1:0)},i.prototype.getCheckboxContainerNode=function(t){return t.parentNode.parentNode},i.prototype.onRowValueChanged=function(t,e){if(t===this.columnName){var o=e.querySelector("input[data-checkbox-element]"),i=this.tableObj.getCellValue(e);o.checked=!(!i||0==i||"false"==i)}},t.oc.table.processor.checkbox=i}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.processor)throw new Error("The $.oc.table.processor namespace is not defined. Make sure that the table.processor.base.js script is loaded.");var e=t.oc.table.processor.base,o=e.prototype,i=function(t,o,i){this.itemListElement=null,this.cachedOptionPromises={},this.searching=!1,this.searchQuery=null,this.searchInterval=null,this.itemClickHandler=this.onItemClick.bind(this),this.itemKeyDownHandler=this.onItemKeyDown.bind(this),this.itemMouseMoveHandler=this.onItemMouseMove.bind(this),e.call(this,t,o,i)};(i.prototype=Object.create(o)).constructor=i,i.prototype.dispose=function(){this.unregisterListHandlers(),this.itemClickHandler=null,this.itemKeyDownHandler=null,this.itemMouseMoveHandler=null,this.itemListElement=null,this.cachedOptionPromises=null,o.dispose.call(this)},i.prototype.unregisterListHandlers=function(){this.itemListElement&&(this.itemListElement.removeEventListener("click",this.itemClickHandler),this.itemListElement.removeEventListener("keydown",this.itemKeyDownHandler),this.itemListElement.removeEventListener("mousemove",this.itemMouseMoveHandler))},i.prototype.renderCell=function(t,e){var o=this.createViewContainer(e,"...");this.fetchOptions(e.parentNode,(function(i){void 0!==i[t]&&(o.textContent=i[t]),e.setAttribute("tabindex",0)}))},i.prototype.onFocus=function(t,e){if(this.activeCell!==t){this.activeCell=t;var o=this.getCellContentContainer(t);this.buildEditor(t,o,e),e||o.focus()}else this.showDropdown()},i.prototype.onUnfocus=function(){this.activeCell&&(this.unregisterListHandlers(),this.hideDropdown(),this.itemListElement=null,this.activeCell=null)},i.prototype.buildEditor=function(t,e,o){var i=this.tableObj.getCellValue(t),a=this.getAbsolutePosition(e);self=this,this.itemListElement=document.createElement("div"),this.itemListElement.addEventListener("click",this.itemClickHandler),this.itemListElement.addEventListener("keydown",this.itemKeyDownHandler),this.itemListElement.addEventListener("mousemove",this.itemMouseMoveHandler),this.itemListElement.setAttribute("class","table-control-dropdown-list"),this.itemListElement.style.width=e.offsetWidth+2+"px",this.itemListElement.style.left=a.left+"px",this.itemListElement.style.top=a.top-2+e.offsetHeight+"px",this.fetchOptions(t,(function(t){var e=document.createElement("ul");for(var a in t){var n=document.createElement("li");n.setAttribute("data-value",a),n.textContent=t[a],n.setAttribute("tabindex",0),a==i&&n.setAttribute("class","selected"),e.appendChild(n)}self.itemListElement.appendChild(e),o&&self.showDropdown(),self=null}))},i.prototype.hideDropdown=function(){if(this.itemListElement&&this.activeCell&&this.itemListElement.parentNode){var t=this.getCellContentContainer(this.activeCell);t.setAttribute("data-dropdown-open","false"),this.itemListElement.parentNode.removeChild(this.itemListElement),t.focus()}},i.prototype.showDropdown=function(){if(this.itemListElement&&this.itemListElement.parentNode!==document.body){this.getCellContentContainer(this.activeCell).setAttribute("data-dropdown-open","true"),document.body.appendChild(this.itemListElement);var t=this.itemListElement.querySelector("ul li.selected");t||(t=this.itemListElement.querySelector("ul li:first-child"))&&t.setAttribute("class","selected"),t&&window.setTimeout((function(){t.focus()}),0)}},i.prototype.fetchOptions=function(t,e){if(this.columnConfiguration.options)e(this.columnConfiguration.options);else{var o=t.parentNode,i=this.createOptionsCachingKey(o),a=this.getViewContainer(t);if(a.setAttribute("class","loading"),!this.cachedOptionPromises[i]){var n={column:this.columnName,rowData:this.tableObj.getRowData(o)},r=this.tableObj.getAlias()+"::onGetDropdownOptions";this.cachedOptionPromises[i]=this.tableObj.$el.request(r,{data:n})}this.cachedOptionPromises[i].done((function(t){e(t.options)})).always((function(){a.setAttribute("class","")}))}},i.prototype.createOptionsCachingKey=function(t){var e="non-dependent",o=this.columnConfiguration.dependsOn;if(o)if("object"==typeof o)for(var i=0,a=o.length;i<a;i++)e+=o[i]+this.tableObj.getRowCellValueByColumnName(t,o[i]);else e=o+this.tableObj.getRowCellValueByColumnName(t,o);return e},i.prototype.getAbsolutePosition=function(t){var e=document.body.scrollTop,o=0;do{e+=t.offsetTop||0,e-=t.scrollTop||0,o+=t.offsetLeft||0,t=t.offsetParent}while(t);return{top:e,left:o}},i.prototype.updateCellFromFocusedItem=function(t){t||(t=this.findFocusedItem()),this.setSelectedItem(t)},i.prototype.findSelectedItem=function(){return this.itemListElement?this.itemListElement.querySelector("ul li.selected"):null},i.prototype.setSelectedItem=function(t){if(!this.itemListElement)return null;"LI"==t.tagName&&this.itemListElement.contains(t)&&(this.itemListElement.querySelectorAll("ul li").forEach((function(t){t.removeAttribute("class")})),t.setAttribute("class","selected")),this.tableObj.setCellValue(this.activeCell,t.getAttribute("data-value")),this.setViewContainerValue(this.activeCell,t.textContent)},i.prototype.findFocusedItem=function(){return this.itemListElement?this.itemListElement.querySelector("ul li:focus"):null},i.prototype.onItemClick=function(t){var e=this.tableObj.getEventTarget(t);"LI"==e.tagName&&(e.focus(),this.updateCellFromFocusedItem(e),this.hideDropdown())},i.prototype.onItemKeyDown=function(t){if(this.itemListElement){if("ArrowDown"===t.key||"ArrowUp"===t.key){var e=this.findFocusedItem(),o=e.nextElementSibling;return"ArrowUp"===t.key&&(o=e.previousElementSibling),void(o&&o.focus())}return"Enter"===t.key||"(Space character)"===t.key||"Spacebar"===t.key||" "===t.key?(this.updateCellFromFocusedItem(),void this.hideDropdown()):"Tab"===t.key?(this.updateCellFromFocusedItem(),this.tableObj.navigation.navigateNext(t),void this.tableObj.stopEvent(t)):void("Escape"!==t.key?this.searchByTextInput(t,!0):this.hideDropdown())}},i.prototype.onItemMouseMove=function(t){if(this.itemListElement){var e=this.tableObj.getEventTarget(t);"LI"==e.tagName&&e.focus()}},i.prototype.onKeyDown=function(t){if(this.itemListElement)if("(Space character)"!==t.key&&"Spacebar"!==t.key&&" "!==t.key||this.searching){if("ArrowDown"===t.key||"ArrowUp"===t.key){var e,o=this.findSelectedItem();if(o)e=o.nextElementSibling,"ArrowUp"===t.key&&(e=o.previousElementSibling);else{if("ArrowUp"===t.key)return!1;e=this.itemListElement.querySelector("ul li:first-child")}return e&&this.setSelectedItem(e),!1}this.searchByTextInput(t)}else this.showDropdown()},i.prototype.onRowValueChanged=function(t,e){if(this.columnConfiguration.dependsOn){var o=!1,i=this.columnConfiguration.dependsOn;if("object"==typeof i){for(var a=0,n=i.length;a<n;a++)if(i[a]==t){o=!0;break}}else o=i==t;if(o){var r=this.tableObj.getCellValue(e),s=this.getViewContainer(e);this.fetchOptions(e,(function(t){var e=void 0!==t[r]?t[r]:"...";s.textContent=e,s=null}))}}},i.prototype.elementBelongsToProcessor=function(t){return!!this.itemListElement&&this.tableObj.parentContainsElement(this.itemListElement,t)},i.prototype.searchByTextInput=function(t,e){void 0===e&&(e=!1);var o=t.key;if(1===o.length||"Space"===o){this.searching||(this.searching=!0,this.searchQuery=""),this.searchQuery+="Space"===o?" ":o;var i=null,a=this.searchQuery;this.itemListElement.querySelectorAll("ul li").forEach((function(t){null===i&&t.dataset.value&&0===t.dataset.value.toLowerCase().indexOf(a.toLowerCase())&&(i=t)})),i?(!0===e?i.focus():this.setSelectedItem(i),this.searchInterval&&clearTimeout(this.searchInterval),this.searchInterval=setTimeout(this.cancelTextSearch.bind(this),1e3)):this.cancelTextSearch()}},i.prototype.cancelTextSearch=function(){this.searching=!1,this.searchQuery=null,this.searchInterval=null},t.oc.table.processor.dropdown=i}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.processor)throw new Error("The $.oc.table.processor namespace is not defined. Make sure that the table.processor.base.js script is loaded.");var e=t.oc.table.processor.string,o=e.prototype,i=function(t,o,i){this.cachedOptionPromises={},e.call(this,t,o,i)};(i.prototype=Object.create(o)).constructor=i,i.prototype.dispose=function(){this.cachedOptionPromises=null,o.dispose.call(this)},i.prototype.onUnfocus=function(){this.activeCell&&(this.removeAutocomplete(),o.onUnfocus.call(this))},i.prototype.renderCell=function(t,e){o.renderCell.call(this,t,e)},i.prototype.buildEditor=function(t,e,i){o.buildEditor.call(this,t,e,i);var a=this;this.fetchOptions(t,(function(t){a.buildAutoComplete(t),a=null}))},i.prototype.fetchOptions=function(e,o){if(this.columnConfiguration.options)void 0!==o&&o(this.columnConfiguration.options);else{if(!1===this.triggerGetOptions(o))return;var i=e.parentNode,a=this.createOptionsCachingKey(i),n=this.getViewContainer(e);if(t.oc.foundation.element.addClass(n,"loading"),!this.cachedOptionPromises[a]){var r={column:this.columnName,rowData:this.tableObj.getRowData(i)},s=this.tableObj.getAlias()+"::onGetAutocompleteOptions";this.cachedOptionPromises[a]=this.tableObj.$el.request(s,{data:r})}this.cachedOptionPromises[a].done((function(t){void 0!==o&&o(t.options)})).always((function(){t.oc.foundation.element.removeClass(n,"loading")}))}},i.prototype.createOptionsCachingKey=function(t){var e="non-dependent",o=this.columnConfiguration.dependsOn;if(o)if("object"==typeof o)for(var i=0,a=o.length;i<a;i++)e+=o[i]+this.tableObj.getRowCellValueByColumnName(t,o[i]);else e=o+this.tableObj.getRowCellValueByColumnName(t,o);return e},i.prototype.triggerGetOptions=function(e){var o=this.tableObj.getElement();if(o){var i=t.Event("autocompleteitems.oc.table");return t(o).trigger(i,[{values:{},callback:e,column:this.columnName,columnConfiguration:this.columnConfiguration}]),!i.isDefaultPrevented()}},i.prototype.getInput=function(){return this.activeCell?this.activeCell.querySelector(".string-input"):null},i.prototype.buildAutoComplete=function(e){if(this.activeCell){var o=this.getInput();o&&(void 0===e&&(e=[]),t(o).autocomplete({source:this.prepareItems(e),matchWidth:!0,menu:'<ul class="autocomplete dropdown-menu table-widget-autocomplete"></ul>',bodyContainer:!0}))}},i.prototype.prepareItems=function(e){var o={};if(t.isArray(e))for(var i=0,a=e.length;i<a;i++)o[e[i]]=e[i];else o=e;return o},i.prototype.removeAutocomplete=function(){var e=this.getInput();t(e).autocomplete("destroy")},t.oc.table.processor.autocomplete=i}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");void 0===t.oc.table.validator&&(t.oc.table.validator={});var e=function(t){this.options=t};e.prototype.validate=function(t,e){if(void 0===this.options.requiredWith||this.rowHasValue(this.options.requiredWith,e))return this.validateValue(t,e)},e.prototype.validateValue=function(t,e){},e.prototype.trim=function(t){return String.prototype.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},e.prototype.getMessage=function(t){return void 0!==this.options.message?this.options.message:t},e.prototype.rowHasValue=function(t,e){return void 0!==e[t]&&("boolean"==typeof e[t]?e[t]:this.trim(String(e[t])).length>0)},t.oc.table.validator.base=e}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.validator)throw new Error("The $.oc.table.validator namespace is not defined. Make sure that the table.validator.base.js script is loaded.");var e=t.oc.table.validator.base,o=e.prototype,i=function(t){e.call(this,t)};(i.prototype=Object.create(o)).constructor=i,i.prototype.validateValue=function(t,e){if(0===(t=this.trim(t)).length)return this.getMessage("The value should not be empty.")},t.oc.table.validator.required=i}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.validator)throw new Error("The $.oc.table.validator namespace is not defined. Make sure that the table.validator.base.js script is loaded.");var e=t.oc.table.validator.base,o=e.prototype,i=function(t){e.call(this,t)};(i.prototype=Object.create(o)).constructor=i,i.prototype.doCommonChecks=function(t){if(void 0!==this.options.min||void 0!==this.options.max){if(void 0!==this.options.min){if(void 0===this.options.min.value)throw new Error("The min.value parameter is not defined in the table validator configuration");if(t<this.options.min.value)return void 0!==this.options.min.message?this.options.min.message:"The value should not be less than "+this.options.min.value}if(void 0!==this.options.max){if(void 0===this.options.max.value)throw new Error("The max.value parameter is not defined in the table validator configuration");if(t>this.options.max.value)return void 0!==this.options.max.message?this.options.max.message:"The value should not be more than "+this.options.max.value}}},t.oc.table.validator.baseNumber=i}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.validator)throw new Error("The $.oc.table.validator namespace is not defined. Make sure that the table.validator.base.js script is loaded.");if(void 0===t.oc.table.validator.baseNumber)throw new Error("The $.oc.table.validator.baseNumber namespace is not defined. Make sure that the table.validator.baseNumber.js script is loaded.");var e=t.oc.table.validator.baseNumber,o=e.prototype,i=function(t){e.call(this,t)};(i.prototype=Object.create(o)).constructor=i,i.prototype.validateValue=function(t,e){if(0!=(t=this.trim(t)).length){if(!(this.options.allowNegative?/^\-?[0-9]*$/.test(t):/^[0-9]*$/.test(t))){var o=this.options.allowNegative?"The value should be an integer.":"The value should be a positive integer";return this.getMessage(o)}return this.doCommonChecks(parseInt(t))}},t.oc.table.validator.integer=i}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.validator)throw new Error("The $.oc.table.validator namespace is not defined. Make sure that the table.validator.base.js script is loaded.");if(void 0===t.oc.table.validator.baseNumber)throw new Error("The $.oc.table.validator.baseNumber namespace is not defined. Make sure that the table.validator.baseNumber.js script is loaded.");var e=t.oc.table.validator.baseNumber,o=e.prototype,i=function(t){e.call(this,t)};(i.prototype=Object.create(o)).constructor=i,i.prototype.validateValue=function(t,e){if(0!=(t=this.trim(t)).length){if(!(this.options.allowNegative?/^[-]?([0-9]+\.[0-9]+|[0-9]+)$/.test(t):/^([0-9]+\.[0-9]+|[0-9]+)$/.test(t))){var o=this.options.allowNegative?"The value should be a floating point number.":"The value should be a positive floating point number";return this.getMessage(o)}return this.doCommonChecks(parseFloat(t))}},t.oc.table.validator.float=i}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.validator)throw new Error("The $.oc.table.validator namespace is not defined. Make sure that the table.validator.base.js script is loaded.");var e=t.oc.table.validator.base,o=e.prototype,i=function(t){e.call(this,t)};(i.prototype=Object.create(o)).constructor=i,i.prototype.validateValue=function(t,e){if(0!=(t=this.trim(t)).length&&(void 0!==this.options.min||void 0!==this.options.max)){if(void 0!==this.options.min){if(void 0===this.options.min.value)throw new Error("The min.value parameter is not defined in the Length table validator configuration");if(t.length<this.options.min.value)return void 0!==this.options.min.message?this.options.min.message:"The string should not be shorter than "+this.options.min.value}if(void 0!==this.options.max){if(void 0===this.options.max.value)throw new Error("The max.value parameter is not defined in the Length table validator configuration");if(t.length>this.options.max.value)return void 0!==this.options.max.message?this.options.max.message:"The string should not be longer than "+this.options.max.value}}},t.oc.table.validator.length=i}(window.jQuery),function(t){"use strict";if(void 0===t.oc.table)throw new Error("The $.oc.table namespace is not defined. Make sure that the table.js script is loaded.");if(void 0===t.oc.table.validator)throw new Error("The $.oc.table.validator namespace is not defined. Make sure that the table.validator.base.js script is loaded.");var e=t.oc.table.validator.base,o=e.prototype,i=function(t){e.call(this,t)};(i.prototype=Object.create(o)).constructor=i,i.prototype.validateValue=function(t,e){if(0!=(t=this.trim(t)).length){if(void 0===this.options.pattern)throw new Error("The pattern parameter is not defined in the Regex table validator configuration");return new RegExp(this.options.pattern,this.options.modifiers).test(t)?void 0:this.getMessage("Invalid value format.")}},t.oc.table.validator.regex=i}(window.jQuery);
